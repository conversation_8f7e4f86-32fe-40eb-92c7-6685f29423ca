name: 🧪 Test Crestal Diamond App

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.9
      uses: actions/setup-python@v3
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r config/requirements.txt
    
    - name: Run basic tests
      run: |
        python -c "import streamlit; print('✅ Streamlit imported successfully')"
        python -c "import pandas; print('✅ Pandas imported successfully')"
        python -c "import plotly; print('✅ Plotly imported successfully')"
    
    - name: Check main app syntax
      run: |
        python -m py_compile invoice_app.py
        echo "✅ Main app syntax is valid"
    
    - name: Check Excel analyzer syntax
      run: |
        python -m py_compile excel_analyzer.py
        echo "✅ Excel analyzer syntax is valid"
