# 🏗️ هيكل المشروع المنظم

## 📁 الهيكل العام للمشروع

```
company app 1/
├── 📄 main.py                    # الملف الرئيسي لتشغيل التطبيق
├── 📄 LICENSE                    # رخصة المشروع
├── 📄 README.md                  # ملف التوثيق الرئيسي
├── 📄 README_EN.md              # ملف التوثيق باللغة الإنجليزية
├── 📄 PROJECT_STRUCTURE.md      # هذا الملف - شرح هيكل المشروع
│
├── 📂 src/                      # الكود المصدري الرئيسي
│   ├── 📄 invoice_app.py        # تطبيق الفواتير الرئيسي
│   ├── 📂 core/                 # الوحدات الأساسية
│   ├── 📂 models/               # نماذج البيانات
│   ├── 📂 pages/                # صفحات التطبيق
│   └── 📂 utils/                # أدوات مساعدة
│
├── 📂 config/                   # ملفات التكوين
│   ├── 📄 settings.py           # إعدادات التطبيق
│   └── 📄 requirements.txt      # متطلبات Python
│
├── 📂 data/                     # ملفات البيانات
│   ├── 📄 customers.csv         # بيانات العملاء
│   ├── 📄 invoices.csv          # بيانات الفواتير
│   ├── 📂 backups/              # النسخ الاحتياطية
│   ├── 📂 customers/            # ملفات العملاء
│   ├── 📂 exports/              # الملفات المصدرة
│   └── 📂 invoices/             # ملفات الفواتير
│
├── 📂 database/                 # نظام قاعدة البيانات
│   ├── 📄 README.md             # دليل قاعدة البيانات
│   ├── 📄 SCHEMA.md             # مخطط قاعدة البيانات
│   ├── 📄 SETUP_GUIDE.md        # دليل الإعداد
│   ├── 📄 setup.sql             # سكريبت إعداد قاعدة البيانات
│   ├── 📄 config.py             # تكوين قاعدة البيانات
│   ├── 📄 manager.py            # مدير قاعدة البيانات
│   ├── 📄 backup.py             # نظام النسخ الاحتياطي
│   ├── 📄 migrate.py            # نظام الترحيل
│   ├── 📄 test_connection.py    # اختبار الاتصال
│   ├── 📄 test_operations.py    # اختبار العمليات
│   └── 📂 logs/                 # سجلات قاعدة البيانات
│
├── 📂 memory/                   # نظام الذكاء الاصطناعي والذاكرة
│   ├── 📄 README.md             # دليل نظام الذكاء الاصطناعي
│   ├── 📄 config.json           # تكوين الذكاء الاصطناعي
│   ├── 📄 streamlit_integration.py # تكامل مع Streamlit
│   ├── 📄 quick_start.py        # بدء سريع للذكاء الاصطناعي
│   ├── 📄 setup_ai_system.py    # إعداد نظام الذكاء الاصطناعي
│   ├── 📄 start_ai.py           # تشغيل الذكاء الاصطناعي
│   ├── 📄 test_ai_system.py     # اختبار نظام الذكاء الاصطناعي
│   ├── 📂 agents/               # وكلاء الذكاء الاصطناعي
│   ├── 📂 api/                  # واجهات برمجة التطبيقات
│   ├── 📂 backups/              # نسخ احتياطية للذاكرة
│   ├── 📂 core/                 # النواة الأساسية
│   ├── 📂 logs/                 # سجلات الذكاء الاصطناعي
│   └── 📂 storage/              # تخزين البيانات
│
├── 📂 tests/                    # اختبارات المشروع
│   ├── 📄 test_database_connection.py    # اختبار اتصال قاعدة البيانات
│   ├── 📄 comprehensive_system_test.py  # اختبار شامل للنظام
│   ├── 📄 comprehensive_test.py         # اختبار شامل
│   ├── 📄 final_invoice_test.py         # اختبار الفواتير النهائي
│   ├── 📄 quick_final_test.py           # اختبار سريع نهائي
│   ├── 📄 test_app.py                   # اختبار التطبيق
│   ├── 📄 test_db_simple.py             # اختبار قاعدة البيانات البسيط
│   ├── 📄 test_invoice_save.py          # اختبار حفظ الفواتير
│   └── 📄 test_main.py                  # اختبار الملف الرئيسي
│
├── 📂 scripts/                  # سكريبتات التشغيل والإعداد
│   ├── 📄 install_requirements.bat      # تثبيت المتطلبات
│   ├── 📄 install_mysql_requirements.bat # تثبيت متطلبات MySQL
│   ├── 📄 run.bat                       # تشغيل التطبيق
│   ├── 📄 run_all_apps.bat              # تشغيل جميع التطبيقات
│   ├── 📄 run_main.bat                  # تشغيل التطبيق الرئيسي
│   ├── 📄 run_integrated_system.bat     # تشغيل النظام المتكامل
│   ├── 📄 run_main_test.bat             # تشغيل اختبار رئيسي
│   ├── 📄 run_with_ai.bat               # تشغيل مع الذكاء الاصطناعي
│   ├── 📄 run_with_database.bat         # تشغيل مع قاعدة البيانات
│   ├── 📄 setup_database.bat            # إعداد قاعدة البيانات
│   ├── 📄 start_system.bat              # بدء النظام
│   ├── 📄 test_ai.bat                   # اختبار الذكاء الاصطناعي
│   ├── 📄 test_ai_quick.bat             # اختبار سريع للذكاء الاصطناعي
│   └── 📄 upload_to_github.bat          # رفع إلى GitHub
│
├── 📂 docs/                     # التوثيق
│   ├── 📄 README.md             # ملف التوثيق الرئيسي
│   ├── 📄 README_EN.md          # التوثيق باللغة الإنجليزية
│   ├── 📄 CHANGELOG.md          # سجل التغييرات
│   ├── 📄 DEVELOPMENT.md        # دليل التطوير
│   ├── 📄 DATABASE_SETUP_GUIDE.md       # دليل إعداد قاعدة البيانات
│   ├── 📄 DATABASE_UPDATE_SUMMARY.md    # ملخص تحديث قاعدة البيانات
│   ├── 📄 REORGANIZATION_SUMMARY.md     # ملخص إعادة التنظيم
│   └── 📄 LICENSE               # رخصة المشروع
│
├── 📂 logs/                     # ملفات السجلات
│   └── 📄 db_test_result.txt    # نتائج اختبار قاعدة البيانات
│
├── 📂 backups/                  # النسخ الاحتياطية العامة
│   └── 📄 backup_invoices_*.csv # نسخ احتياطية للفواتير
│
├── 📂 assets/                   # الأصول والموارد
│
└── 📂 archive/                  # الملفات المؤرشفة
    ├── 📄 README_ARCHIVE.md     # دليل الأرشيف
    ├── 📄 01_بيانات_العملاء.py  # صفحة بيانات العملاء القديمة
    ├── 📄 app.py                # تطبيق قديم
    ├── 📄 main.py               # ملف رئيسي قديم
    ├── 📄 main_app.py           # تطبيق رئيسي قديم
    └── 📂 assets/               # أصول مؤرشفة
```

## 🚀 كيفية تشغيل المشروع

### 1. التشغيل الأساسي
```bash
# من الجذر الرئيسي
python main.py

# أو باستخدام سكريبت
scripts\run_main.bat
```

### 2. التشغيل مع قاعدة البيانات
```bash
scripts\run_with_database.bat
```

### 3. التشغيل مع الذكاء الاصطناعي
```bash
scripts\run_with_ai.bat
```

### 4. التشغيل المتكامل (كل الميزات)
```bash
scripts\run_integrated_system.bat
```

## 🧪 تشغيل الاختبارات

```bash
# اختبار شامل للنظام
python tests\comprehensive_system_test.py

# اختبار قاعدة البيانات
python tests\test_database_connection.py

# اختبار الذكاء الاصطناعي
scripts\test_ai.bat
```

## 📋 ملاحظات مهمة

1. **الملف الرئيسي**: `main.py` في الجذر هو نقطة البداية
2. **البيانات**: جميع ملفات البيانات في مجلد `data/`
3. **الاختبارات**: جميع الاختبارات في مجلد `tests/`
4. **السكريبتات**: جميع سكريبتات التشغيل في مجلد `scripts/`
5. **التوثيق**: جميع ملفات التوثيق في مجلد `docs/`
6. **الأرشيف**: الملفات القديمة في مجلد `archive/`

## 🔧 الإعداد الأولي

1. تثبيت المتطلبات:
   ```bash
   scripts\install_requirements.bat
   ```

2. إعداد قاعدة البيانات (اختياري):
   ```bash
   scripts\setup_database.bat
   ```

3. تشغيل التطبيق:
   ```bash
   scripts\run_main.bat
   ```

هذا الهيكل المنظم يجعل المشروع أكثر وضوحاً وسهولة في الصيانة والتطوير! 🎉
