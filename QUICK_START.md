# 🚀 دليل البدء السريع - Crestal Diamond Workshop

## ⚡ التشغيل السريع

### 1. التشغيل الأساسي
```bash
# تشغيل النظام المنظم (الأحدث)
streamlit run main.py

# أو باستخدام السكريبت المحسن
scripts\run_organized_app.bat
```

### 2. التشغيل مع فحص النظام
```bash
# سكريبت محسن مع فحص البيئة والهيكل
scripts\run_organized_app.bat
```

### 3. التشغيل مع الذكاء الاصطناعي
```bash
# تشغيل مع نظام الذكاء الاصطناعي
scripts\run_with_ai.bat

# تشغيل النظام المتكامل
streamlit run src\invoice_app.py
```

## 📁 الملفات الرئيسية

- **`main.py`** - النظام المنظم الجديد (الأحدث)
- **`src/invoice_app.py`** - النظام المتكامل مع الذكاء الاصطناعي
- **`PROJECT_STRUCTURE.md`** - دليل هيكل المشروع المفصل
- **`README.md`** - التوثيق الشامل

## 🎯 الصفحات المتاحة

1. **📄 إنشاء فاتورة جديدة** - إنشاء فواتير تفاعلية
2. **📊 عرض الفواتير** - إدارة ومراجعة الفواتير
3. **👥 حسابات العملاء** - كشوف حساب مفصلة
4. **🔍 تحليل Excel** - تحليل البيانات الخارجية
5. **📈 التقارير** - إحصائيات ومؤشرات الأداء
6. **⚙️ الإعدادات** - إدارة النظام والنسخ الاحتياطية

## 🔧 الإعداد الأولي

### 1. تثبيت المتطلبات
```bash
# تثبيت المكتبات
pip install -r config\requirements.txt

# أو باستخدام السكريبت
scripts\install_requirements.bat
```

### 2. إعداد البيئة (إذا لم تكن موجودة)
```bash
# إنشاء البيئة الافتراضية
python -m venv .venv

# تفعيل البيئة
.venv\Scripts\activate.bat

# تثبيت المتطلبات
pip install -r config\requirements.txt
```

### 3. إعداد قاعدة البيانات (اختياري)
```bash
# إعداد MySQL
scripts\setup_database.bat

# أو تشغيل مع قاعدة البيانات
scripts\run_with_database.bat
```

## 🧪 الاختبارات

```bash
# اختبار شامل للنظام
python tests\comprehensive_system_test.py

# اختبار قاعدة البيانات
python tests\test_database_connection.py

# اختبار الذكاء الاصطناعي
scripts\test_ai.bat
```

## 📞 المساعدة

- **التوثيق الشامل:** `README.md`
- **هيكل المشروع:** `PROJECT_STRUCTURE.md`
- **دليل قاعدة البيانات:** `database/README.md`
- **دليل الذكاء الاصطناعي:** `memory/README.md`

## 🎉 المميزات الجديدة

✅ **مشروع منظم بالكامل** مع هيكل احترافي
✅ **إصلاح جميع الأخطاء البرمجية** والمشاكل التقنية
✅ **سكريبتات تشغيل محسنة** وسهلة الاستخدام
✅ **نظام ذكاء اصطناعي متكامل** مع 4 وكلاء متخصصين
✅ **دعم قاعدة بيانات MySQL** مع نظام CSV احتياطي

---

**الإصدار:** 4.1.0 (المنظم مع الإصلاحات الشاملة)
**آخر تحديث:** يوليو 2025
