# 💎 نظام إدارة ورشة Crestal Diamond مع الذكاء الاصطناعي

## 🧠 نظام الذكاء الاصطناعي الجديد

تم إضافة نظام ذكاء اصطناعي متطور مع ذاكرة ذكية ووكلاء متخصصين لتحسين تجربة المستخدم وأتمتة العمليات.

### 🤖 **كريستال - المساعد الذكي:**
- **محادثة طبيعية** باللغة العربية
- **ذاكرة ذكية** تتذكر التفاعلات والتفضيلات
- **تحليل المشاعر** وفهم السياق
- **اقتراحات مخصصة** لكل عميل

### 🎯 **الوكلاء المتخصصون:**
- **📊 محلل البيانات**: تحليل المبيعات والاتجاهات والتنبؤ
- **👥 مستشار العملاء**: توصيات مخصصة وتحليل الرضا
- **📦 مدير المخزون**: مراقبة المخزون والتنبؤ بالاحتياجات
- **💰 مساعد التسعير**: حساب الأسعار والخصومات الذكية

## 🎯 الملفات الرئيسية النشطة

### **النظام المتكامل مع الذكاء الاصطناعي (الأحدث):**
**`invoice_app.py`** - النظام الكامل مع الذكاء الاصطناعي:
- ✅ **6 صفحات متكاملة** مع الذكاء الاصطناعي
- ✅ **نظام ذاكرة متقدم** مع قاعدة بيانات SQLite
- ✅ **4 وكلاء ذكيين متخصصين**
- ✅ **محادثة ذكية** مع كريستال
- ✅ **تحليلات متقدمة** ورؤى ذكية
- ✅ **حاسبة تسعير ذكية**
- ✅ **دعم قاعدة بيانات MySQL** مع نظام CSV احتياطي

### **النظام المنظم (للتطوير المستقبلي):**
**`main.py`** - النظام المنظم:
- ✅ **6 صفحات منفصلة** في مجلد `src/pages/`
- ✅ **بنية منظمة** وقابلة للتوسع
- ✅ **كود نظيف** وسهل الصيانة
- ✅ **أداء محسن** وتحميل أسرع

## 📁 هيكل المشروع

```
company app 1/
├── 🎯 invoice_app.py             # الملف الرئيسي النشط (6 صفحات + AI)
├── 🔍 excel_analyzer.py          # محلل ملفات Excel المنفصل
├── 🚀 run.bat                    # سكريبت التشغيل السريع
├── 🧠 memory/                    # نظام الذكاء الاصطناعي والذاكرة
│   ├── core/                     # النواة الأساسية
│   │   ├── memory_manager.py     # مدير الذاكرة المتقدم
│   │   └── ai_agent.py          # كريستال - الوكيل الذكي الرئيسي
│   ├── agents/                   # الوكلاء المتخصصون
│   │   ├── data_analyst.py       # محلل البيانات الذكي
│   │   ├── customer_advisor.py   # مستشار العملاء الذكي
│   │   ├── inventory_manager.py  # مدير المخزون الذكي
│   │   └── pricing_assistant.py  # مساعد التسعير الذكي
│   ├── storage/                  # نظام التخزين
│   │   ├── memory.db            # قاعدة بيانات الذاكرة
│   │   ├── logs/                # ملفات السجلات
│   │   └── backups/             # النسخ الاحتياطية
│   ├── api/                     # واجهات البرمجة
│   │   └── ai_interface.py      # الواجهة الرئيسية
│   ├── streamlit_integration.py  # تكامل مع Streamlit
│   ├── quick_start.py           # تشغيل سريع للنظام
│   ├── setup_ai_system.py       # إعداد النظام
│   ├── test_ai_system.py        # اختبارات شاملة
│   ├── simple_test.py           # اختبارات مبسطة
│   ├── config.json              # إعدادات النظام
│   └── README.md                # دليل نظام الذكاء الاصطناعي
├── 🗄️ database/                  # مجلد قاعدة البيانات المنظم
│   ├── __init__.py               # ملف التهيئة
│   ├── config.py                 # إعدادات الاتصال
│   ├── manager.py                # مدير قاعدة البيانات الموحد
│   ├── setup.sql                 # سكريبت إنشاء الجداول
│   ├── test_connection.py        # اختبارات الاتصال
│   ├── migrate.py                # ترحيل البيانات من CSV
│   ├── backup.py                 # نظام النسخ الاحتياطية
│   ├── README.md                 # دليل مجلد قاعدة البيانات
│   ├── SETUP_GUIDE.md            # دليل الإعداد التفصيلي
│   ├── SCHEMA.md                 # توثيق بنية قاعدة البيانات
│   └── logs/                     # مجلد السجلات
├── 📁 src/                       # كود منظم (للمستقبل)
│   ├── 📁 core/                  # الوظائف الأساسية
│   │   ├── __init__.py
│   │   ├── database.py           # مدير قاعدة البيانات
│   │   ├── excel_handler.py      # معالج ملفات Excel
│   │   └── utils.py              # الأدوات المساعدة
│   ├── 📁 models/                # نماذج البيانات
│   │   ├── __init__.py
│   │   ├── invoice.py            # نموذج الفاتورة
│   │   ├── customer.py           # نموذج العميل
│   │   └── excel_analyzer.py     # نموذج تحليل Excel
│   ├── 📁 pages/                 # صفحات التطبيق
│   │   ├── __init__.py
│   │   ├── invoice_creation.py   # صفحة إنشاء الفواتير
│   │   ├── invoice_list.py       # صفحة عرض الفواتير
│   │   ├── customer_accounts.py  # صفحة حسابات العملاء
│   │   ├── excel_analysis.py     # صفحة تحليل Excel
│   │   ├── reports.py            # صفحة التقارير
│   │   └── settings.py           # صفحة الإعدادات
│   └── 📁 utils/                 # الأدوات المساعدة
│       ├── __init__.py
│       ├── formatters.py         # أدوات التنسيق
│       └── validators.py         # أدوات التحقق
├── 📁 data/                      # البيانات المنظمة
│   ├── 📁 invoices/              # ملفات الفواتير
│   │   └── invoices.csv
│   ├── 📁 customers/             # بيانات العملاء
│   │   └── customers.csv
│   ├── 📁 exports/               # الملفات المصدرة
│   └── 📁 backups/               # النسخ الاحتياطية
├── 📁 config/                    # إعدادات (للمستقبل)
│   ├── settings.py               # إعدادات التطبيق
│   └── requirements.txt          # المكتبات المطلوبة
├── 📁 docs/                      # التوثيق
│   ├── README.md                 # هذا الملف
│   ├── README_EN.md              # دليل إنجليزي
│   ├── CHANGELOG.md              # سجل التغييرات
│   ├── DEVELOPMENT.md            # دليل المطورين
│   └── LICENSE                   # الرخصة
├── 📁 scripts/                   # سكريبتات التشغيل
│   ├── install_requirements.bat  # تثبيت المكتبات
│   ├── run_main.bat              # تشغيل التطبيق الرئيسي
│   └── run_all_apps.bat          # تشغيل جميع التطبيقات
├── 📁 tests/                     # اختبارات (للمستقبل)
├── 📁 assets/                    # الموارد (صور، أيقونات)
├── 📁 logs/                      # ملفات السجلات
└── 📁 archive/                   # الملفات المرجعية
    ├── README_ARCHIVE.md         # دليل الأرشيف
    ├── app.py                    # الإصدار الأول
    ├── main_app.py               # الإصدار المتوسط
    ├── 01_بيانات_العملاء.py      # الصفحة المنفصلة
    └── main.py                   # البنية المستقبلية
```

## 🧠 إعداد نظام الذكاء الاصطناعي

### إعداد سريع للذكاء الاصطناعي:
```bash
# إعداد النظام الكامل
python memory/setup_ai_system.py

# تشغيل سريع للنظام
python memory/quick_start.py

# اختبار النظام
python memory/simple_test.py
```

### الميزات الذكية:
- ✅ **كريستال AI Agent** - مساعد ذكي يفهم العربية
- ✅ **Smart Memory System** - ذاكرة ذكية مع SQLite
- ✅ **4 Specialized Agents** - وكلاء متخصصون للتحليل والتوصيات
- ✅ **Natural Language Processing** - معالجة طبيعية للنصوص
- ✅ **Sentiment Analysis** - تحليل المشاعر والسياق
- ✅ **Predictive Analytics** - تحليلات تنبؤية للمبيعات والمخزون
- ✅ **Personalized Recommendations** - توصيات مخصصة لكل عميل
- ✅ **Smart Pricing Calculator** - حاسبة تسعير ذكية

## 🗄️ إعداد قاعدة البيانات

### إعداد سريع:
```bash
# تشغيل سكريبت الإعداد الشامل
run_with_database.bat
```

### إعداد يدوي:
1. **إعداد قاعدة البيانات:**
```bash
python database/config.py
```

2. **اختبار الاتصال:**
```bash
python database/test_connection.py
```

3. **ترحيل البيانات من CSV (اختياري):**
```bash
python database/migrate.py
```

4. **إنشاء نسخة احتياطية:**
```bash
python database/backup.py
```

### المميزات:
- ✅ **MySQL Database** - أداء محسن وأمان أفضل
- ✅ **CSV Fallback** - نظام احتياطي تلقائي
- ✅ **Auto Migration** - ترحيل البيانات تلقائياً
- ✅ **Connection Pooling** - إدارة الاتصالات المحسنة
- ✅ **Organized Structure** - مجلد منظم لقاعدة البيانات
- ✅ **Comprehensive Documentation** - توثيق شامل ومفصل
- ✅ **Backup System** - نظام نسخ احتياطية متقدم

## 🚀 التشغيل

### النظام المتكامل مع الذكاء الاصطناعي (الأحدث والموصى به):
```bash
streamlit run invoice_app.py
```
أو باستخدام السكريبت:
```bash
# Windows
run.bat
```

### تشغيل نظام الذكاء الاصطناعي منفصلاً:
```bash
# عرض توضيحي تفاعلي
python memory/quick_start.py

# اختبار النظام
python memory/simple_test.py

# إعداد النظام
python memory/setup_ai_system.py
```

### النظام المنظم (للتطوير المستقبلي):
```bash
streamlit run main.py
```
أو باستخدام السكريبت:
```bash
# Windows
run_main.bat
```

### السكريبتات المتقدمة:
```bash
# Windows
scripts\run_main.bat
scripts\run_all_apps.bat
```

## ✨ المميزات الجديدة

### 🏗️ **البنية المنظمة:**
- **فصل الاهتمامات:** كل وظيفة في ملف منفصل
- **قابلية التوسع:** سهولة إضافة مميزات جديدة
- **سهولة الصيانة:** كود منظم وواضح
- **إعادة الاستخدام:** مكونات قابلة للاستخدام المتكرر

### 📊 **إدارة البيانات المحسنة:**
- **مدير قاعدة بيانات موحد:** `DatabaseManager`
- **نماذج بيانات منظمة:** `Invoice`, `Customer`
- **نسخ احتياطية تلقائية**
- **تصدير واستيراد محسن**

### ⚙️ **إعدادات مركزية:**
- **ملف إعدادات شامل:** `config/settings.py`
- **مسارات منظمة ومرنة**
- **إعدادات قابلة للتخصيص**
- **رسائل نظام موحدة**

### 🔧 **أدوات تطوير:**
- **اختبارات منظمة:** مجلد `tests/`
- **سجلات منظمة:** مجلد `logs/`
- **أرشيف منظم:** مجلد `archive/`
- **توثيق شامل:** مجلد `docs/`

## 📦 التثبيت

### 1. تثبيت المكتبات:
```bash
pip install -r config/requirements.txt
```

### 2. تشغيل التطبيق:
```bash
streamlit run invoice_app.py
```

## 🎯 الصفحات المتاحة (في invoice_app.py)

1. **📄 إنشاء فاتورة جديدة** - إنشاء فواتير تفاعلية مع حساب الذهب والأحجار + **مساعد التسعير الذكي**
2. **📊 عرض الفواتير المحفوظة** - إدارة ومراجعة الفواتير مع البحث والفلترة + **تحليلات ذكية**
3. **👥 حسابات العملاء** - كشوف حساب مفصلة مع رسوم بيانية تفاعلية + **توصيات مخصصة**
4. **🔍 تحليل ملفات Excel** - تحليل البيانات الخارجية من ملفات Excel و CSV + **رؤى ذكية**
5. **📈 إحصائيات وتقارير** - تقارير تفاعلية ومؤشرات الأداء + **تنبؤات ذكية**
6. **⚙️ الإعدادات** - إدارة النظام والنسخ الاحتياطية + **إعدادات الذكاء الاصطناعي**
7. **🤖 كريستال - المساعد الذكي** - محادثة تفاعلية مع الذكاء الاصطناعي ولوحة تحكم ذكية

## 🧠 مميزات الذكاء الاصطناعي الجديدة

### 🤖 **كريستال - المساعد الذكي:**
- **محادثة طبيعية** باللغة العربية مع فهم السياق
- **ذاكرة ذكية** تتذكر التفاعلات والتفضيلات
- **اقتراحات مخصصة** بناءً على تاريخ العميل
- **مساعدة فورية** في جميع جوانب النظام

### 📊 **محلل البيانات الذكي:**
- **تحليل اتجاهات المبيعات** مع التنبؤ المستقبلي
- **تحليل سلوك العملاء** وأنماط الشراء
- **تحديد الفرص** والمخاطر التجارية
- **تقارير ذكية** مع رؤى قابلة للتنفيذ

### 👥 **مستشار العملاء الذكي:**
- **توصيات مخصصة** لكل عميل حسب تفضيلاته
- **تحليل رضا العملاء** ومستوى الولاء
- **تحديد مراحل دورة حياة العميل**
- **اقتراحات البيع الإضافي** والترقية

### 📦 **مدير المخزون الذكي:**
- **مراقبة مستويات المخزون** مع تنبيهات ذكية
- **التنبؤ بالطلب** على المواد والمنتجات
- **اقتراحات إعادة الطلب** المحسنة
- **تحسين مستويات التخزين** لتقليل التكاليف

### 💰 **مساعد التسعير الذكي:**
- **حساب أسعار الذهب والأحجار** بدقة عالية
- **اقتراحات التسعير الأمثل** حسب السوق
- **حساب الخصومات التلقائي** بناءً على قواعد ذكية
- **تحليل أداء التسعير** وتحسين الهوامش

## 🗄️ مجلد قاعدة البيانات

### الملفات الرئيسية:
- **`config.py`** - إعدادات الاتصال والتهيئة
- **`manager.py`** - مدير قاعدة البيانات الموحد
- **`setup.sql`** - سكريبت إنشاء الجداول والبيانات الأولية
- **`test_connection.py`** - اختبارات شاملة للاتصال والعمليات
- **`migrate.py`** - ترحيل البيانات من CSV إلى MySQL
- **`backup.py`** - نظام النسخ الاحتياطية (SQL/CSV/JSON)

### التوثيق:
- **`README.md`** - دليل مجلد قاعدة البيانات
- **`SETUP_GUIDE.md`** - دليل الإعداد التفصيلي خطوة بخطوة
- **`SCHEMA.md`** - توثيق بنية قاعدة البيانات والجداول

### الاستخدام:
```bash
# إعداد قاعدة البيانات
python database/config.py

# اختبار شامل
python database/test_connection.py

# ترحيل البيانات
python database/migrate.py

# نسخة احتياطية
python database/backup.py
```

## 🔧 للمطورين

### إضافة صفحة جديدة:
1. إنشاء ملف في `src/pages/`
2. إضافة الصفحة في `main.py`
3. تحديث التوثيق

### إضافة نموذج بيانات جديد:
1. إنشاء ملف في `src/models/`
2. تحديث `__init__.py`
3. إضافة الوظائف في `DatabaseManager`

### تخصيص الإعدادات:
- تعديل `database/config.py`
- إضافة متغيرات بيئة جديدة في `.env`
- تحديث المسارات حسب الحاجة

### العمل مع قاعدة البيانات:
```python
from database import DatabaseManager

# استخدام مدير قاعدة البيانات
with DatabaseManager() as db:
    customers = db.get_all_customers()
    invoice_id = db.create_invoice(invoice_data)
```

### العمل مع نظام الذكاء الاصطناعي:
```python
from memory.api.ai_interface import AIInterface

# استخدام الذكاء الاصطناعي
ai = AIInterface()

# محادثة مع كريستال
response = ai.chat_with_ai("ما هو سعر الذهب اليوم؟")

# الحصول على توصيات ذكية
recommendations = ai.get_recommendations('customer', customer_id=123)

# تحليل البيانات
insights = ai.get_smart_insights('sales', sales_data)
```

### تكامل Streamlit مع الذكاء الاصطناعي:
```python
from memory.streamlit_integration import ai_chat_interface, ai_pricing_calculator

# إضافة المحادثة الذكية
ai_chat_interface()

# إضافة حاسبة التسعير الذكية
ai_pricing_calculator()
```

## 📈 المستقبل

هذه البنية الجديدة تدعم:
- **ذكاء اصطناعي متقدم** مع تعلم آلي
- **تحليلات تنبؤية** للمبيعات والمخزون
- **معالجة اللغة الطبيعية** المحسنة
- **تكامل مع أنظمة CRM** خارجية
- **قواعد بيانات متقدمة** (PostgreSQL, MySQL)
- **واجهات برمجة التطبيقات** (REST APIs)
- **تطبيقات جوال** مصاحبة
- **نشر سحابي** متقدم

## 📞 الدعم

للدعم الفني أو الاستفسارات:
- **البريد الإلكتروني:** <EMAIL>
- **التوثيق:** `docs/`
- **المشاكل:** GitHub Issues

---

## 🎉 الإنجازات الحديثة

### ✅ **نظام الذكاء الاصطناعي المتكامل:**
- **79 ذكرى** محفوظة في النظام
- **10 ذكريات عملاء** مخصصة
- **11 نمط سلوك** مكتشف
- **4 وكلاء متخصصين** نشطين

### ✅ **الاختبارات والجودة:**
- **جميع المكونات** تم اختبارها بنجاح
- **نظام ذاكرة متقدم** مع SQLite
- **واجهات برمجة** محسنة ومنظمة
- **تكامل Streamlit** كامل وجاهز

### ✅ **التوثيق الشامل:**
- **دليل مفصل** لنظام الذكاء الاصطناعي
- **أمثلة عملية** للاستخدام
- **اختبارات شاملة** ومبسطة
- **إرشادات التطوير** المستقبلي

---

**تم التطوير بواسطة:** فريق Crestal Diamond
**الإصدار:** 4.0.0 (مع الذكاء الاصطناعي)
**آخر تحديث:** يوليو 2025

### 🚀 **الخطوات التالية:**
1. **تكامل كامل** مع تطبيق Streamlit الرئيسي
2. **تدريب النظام** على بيانات حقيقية
3. **تحسين الخوارزميات** والتنبؤات
4. **إضافة ميزات متقدمة** جديدة
