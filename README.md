# 💎 نظام إدارة ورشة Crestal Diamond

## 🏗️ البنية الجديدة المنظمة

تم إعادة تنظيم المشروع بطريقة استراتيجية ومهنية لتحضيره للتوسع المستقبلي.

## 🎯 الملفات الرئيسية النشطة

### **النظام المنظم الجديد (موصى به):**
**`main.py`** - النظام الجديد المنظم:
- ✅ **6 صفحات منفصلة** في مجلد `src/pages/`
- ✅ **بنية منظمة** وقابلة للتوسع
- ✅ **كود نظيف** وسهل الصيانة
- ✅ **أداء محسن** وتحميل أسرع

### **النظام الكلاسيكي (للاستخدام الحالي):**
**`invoice_app.py`** - النظام الأصلي مع قاعدة البيانات:
- ✅ **6 صفحات في ملف واحد** (1100+ سطر)
- ✅ **دعم قاعدة بيانات MySQL** مع نظام CSV احتياطي
- ✅ **جميع الوظائف** متاحة ومتكاملة
- ✅ **مُختبر ومستقر** بالكامل
- ✅ **أداء محسن** مع قاعدة البيانات
- ⚠️ **ملف كبير** يحتاج تنظيم

## 📁 هيكل المشروع

```
company app 1/
├── 🎯 invoice_app.py             # الملف الرئيسي النشط (6 صفحات)
├── 🔍 excel_analyzer.py          # محلل ملفات Excel المنفصل
├── 🚀 run.bat                    # سكريبت التشغيل السريع
├── 🗄️ database/                  # مجلد قاعدة البيانات المنظم
│   ├── __init__.py               # ملف التهيئة
│   ├── config.py                 # إعدادات الاتصال
│   ├── manager.py                # مدير قاعدة البيانات الموحد
│   ├── setup.sql                 # سكريبت إنشاء الجداول
│   ├── test_connection.py        # اختبارات الاتصال
│   ├── migrate.py                # ترحيل البيانات من CSV
│   ├── backup.py                 # نظام النسخ الاحتياطية
│   ├── README.md                 # دليل مجلد قاعدة البيانات
│   ├── SETUP_GUIDE.md            # دليل الإعداد التفصيلي
│   ├── SCHEMA.md                 # توثيق بنية قاعدة البيانات
│   └── logs/                     # مجلد السجلات
├── 📁 src/                       # كود منظم (للمستقبل)
│   ├── 📁 core/                  # الوظائف الأساسية
│   │   ├── __init__.py
│   │   ├── database.py           # مدير قاعدة البيانات
│   │   ├── excel_handler.py      # معالج ملفات Excel
│   │   └── utils.py              # الأدوات المساعدة
│   ├── 📁 models/                # نماذج البيانات
│   │   ├── __init__.py
│   │   ├── invoice.py            # نموذج الفاتورة
│   │   ├── customer.py           # نموذج العميل
│   │   └── excel_analyzer.py     # نموذج تحليل Excel
│   ├── 📁 pages/                 # صفحات التطبيق
│   │   ├── __init__.py
│   │   ├── invoice_creation.py   # صفحة إنشاء الفواتير
│   │   ├── invoice_list.py       # صفحة عرض الفواتير
│   │   ├── customer_accounts.py  # صفحة حسابات العملاء
│   │   ├── excel_analysis.py     # صفحة تحليل Excel
│   │   ├── reports.py            # صفحة التقارير
│   │   └── settings.py           # صفحة الإعدادات
│   └── 📁 utils/                 # الأدوات المساعدة
│       ├── __init__.py
│       ├── formatters.py         # أدوات التنسيق
│       └── validators.py         # أدوات التحقق
├── 📁 data/                      # البيانات المنظمة
│   ├── 📁 invoices/              # ملفات الفواتير
│   │   └── invoices.csv
│   ├── 📁 customers/             # بيانات العملاء
│   │   └── customers.csv
│   ├── 📁 exports/               # الملفات المصدرة
│   └── 📁 backups/               # النسخ الاحتياطية
├── 📁 config/                    # إعدادات (للمستقبل)
│   ├── settings.py               # إعدادات التطبيق
│   └── requirements.txt          # المكتبات المطلوبة
├── 📁 docs/                      # التوثيق
│   ├── README.md                 # هذا الملف
│   ├── README_EN.md              # دليل إنجليزي
│   ├── CHANGELOG.md              # سجل التغييرات
│   ├── DEVELOPMENT.md            # دليل المطورين
│   └── LICENSE                   # الرخصة
├── 📁 scripts/                   # سكريبتات التشغيل
│   ├── install_requirements.bat  # تثبيت المكتبات
│   ├── run_main.bat              # تشغيل التطبيق الرئيسي
│   └── run_all_apps.bat          # تشغيل جميع التطبيقات
├── 📁 tests/                     # اختبارات (للمستقبل)
├── 📁 assets/                    # الموارد (صور، أيقونات)
├── 📁 logs/                      # ملفات السجلات
└── 📁 archive/                   # الملفات المرجعية
    ├── README_ARCHIVE.md         # دليل الأرشيف
    ├── app.py                    # الإصدار الأول
    ├── main_app.py               # الإصدار المتوسط
    ├── 01_بيانات_العملاء.py      # الصفحة المنفصلة
    └── main.py                   # البنية المستقبلية
```

## 🗄️ إعداد قاعدة البيانات

### إعداد سريع:
```bash
# تشغيل سكريبت الإعداد الشامل
run_with_database.bat
```

### إعداد يدوي:
1. **إعداد قاعدة البيانات:**
```bash
python database/config.py
```

2. **اختبار الاتصال:**
```bash
python database/test_connection.py
```

3. **ترحيل البيانات من CSV (اختياري):**
```bash
python database/migrate.py
```

4. **إنشاء نسخة احتياطية:**
```bash
python database/backup.py
```

### المميزات:
- ✅ **MySQL Database** - أداء محسن وأمان أفضل
- ✅ **CSV Fallback** - نظام احتياطي تلقائي
- ✅ **Auto Migration** - ترحيل البيانات تلقائياً
- ✅ **Connection Pooling** - إدارة الاتصالات المحسنة
- ✅ **Organized Structure** - مجلد منظم لقاعدة البيانات
- ✅ **Comprehensive Documentation** - توثيق شامل ومفصل
- ✅ **Backup System** - نظام نسخ احتياطية متقدم

## 🚀 التشغيل

### النظام المنظم الجديد (موصى به):
```bash
streamlit run main.py
```
أو باستخدام السكريبت:
```bash
# Windows
run_main.bat
```

### النظام الكلاسيكي مع قاعدة البيانات (للاستخدام الحالي):
```bash
streamlit run invoice_app.py
```
أو باستخدام السكريبت:
```bash
# Windows
run.bat
```

### السكريبتات المتقدمة:
```bash
# Windows
scripts\run_main.bat
scripts\run_all_apps.bat
```

## ✨ المميزات الجديدة

### 🏗️ **البنية المنظمة:**
- **فصل الاهتمامات:** كل وظيفة في ملف منفصل
- **قابلية التوسع:** سهولة إضافة مميزات جديدة
- **سهولة الصيانة:** كود منظم وواضح
- **إعادة الاستخدام:** مكونات قابلة للاستخدام المتكرر

### 📊 **إدارة البيانات المحسنة:**
- **مدير قاعدة بيانات موحد:** `DatabaseManager`
- **نماذج بيانات منظمة:** `Invoice`, `Customer`
- **نسخ احتياطية تلقائية**
- **تصدير واستيراد محسن**

### ⚙️ **إعدادات مركزية:**
- **ملف إعدادات شامل:** `config/settings.py`
- **مسارات منظمة ومرنة**
- **إعدادات قابلة للتخصيص**
- **رسائل نظام موحدة**

### 🔧 **أدوات تطوير:**
- **اختبارات منظمة:** مجلد `tests/`
- **سجلات منظمة:** مجلد `logs/`
- **أرشيف منظم:** مجلد `archive/`
- **توثيق شامل:** مجلد `docs/`

## 📦 التثبيت

### 1. تثبيت المكتبات:
```bash
pip install -r config/requirements.txt
```

### 2. تشغيل التطبيق:
```bash
streamlit run invoice_app.py
```

## 🎯 الصفحات المتاحة (في invoice_app.py)

1. **📄 إنشاء فاتورة جديدة** - إنشاء فواتير تفاعلية مع حساب الذهب والأحجار
2. **📊 عرض الفواتير المحفوظة** - إدارة ومراجعة الفواتير مع البحث والفلترة
3. **👥 حسابات العملاء** - كشوف حساب مفصلة مع رسوم بيانية تفاعلية
4. **🔍 تحليل ملفات Excel** - تحليل البيانات الخارجية من ملفات Excel و CSV
5. **📈 إحصائيات وتقارير** - تقارير تفاعلية ومؤشرات الأداء
6. **⚙️ الإعدادات** - إدارة النظام والنسخ الاحتياطية

## 🗄️ مجلد قاعدة البيانات

### الملفات الرئيسية:
- **`config.py`** - إعدادات الاتصال والتهيئة
- **`manager.py`** - مدير قاعدة البيانات الموحد
- **`setup.sql`** - سكريبت إنشاء الجداول والبيانات الأولية
- **`test_connection.py`** - اختبارات شاملة للاتصال والعمليات
- **`migrate.py`** - ترحيل البيانات من CSV إلى MySQL
- **`backup.py`** - نظام النسخ الاحتياطية (SQL/CSV/JSON)

### التوثيق:
- **`README.md`** - دليل مجلد قاعدة البيانات
- **`SETUP_GUIDE.md`** - دليل الإعداد التفصيلي خطوة بخطوة
- **`SCHEMA.md`** - توثيق بنية قاعدة البيانات والجداول

### الاستخدام:
```bash
# إعداد قاعدة البيانات
python database/config.py

# اختبار شامل
python database/test_connection.py

# ترحيل البيانات
python database/migrate.py

# نسخة احتياطية
python database/backup.py
```

## 🔧 للمطورين

### إضافة صفحة جديدة:
1. إنشاء ملف في `src/pages/`
2. إضافة الصفحة في `main.py`
3. تحديث التوثيق

### إضافة نموذج بيانات جديد:
1. إنشاء ملف في `src/models/`
2. تحديث `__init__.py`
3. إضافة الوظائف في `DatabaseManager`

### تخصيص الإعدادات:
- تعديل `database/config.py`
- إضافة متغيرات بيئة جديدة في `.env`
- تحديث المسارات حسب الحاجة

### العمل مع قاعدة البيانات:
```python
from database import DatabaseManager

# استخدام مدير قاعدة البيانات
with DatabaseManager() as db:
    customers = db.get_all_customers()
    invoice_id = db.create_invoice(invoice_data)
```

## 📈 المستقبل

هذه البنية الجديدة تدعم:
- **قواعد بيانات متقدمة** (PostgreSQL, MySQL)
- **واجهات برمجة التطبيقات** (REST APIs)
- **تطبيقات جوال** مصاحبة
- **تكامل مع أنظمة خارجية**
- **نشر سحابي** متقدم

## 📞 الدعم

للدعم الفني أو الاستفسارات:
- **البريد الإلكتروني:** <EMAIL>
- **التوثيق:** `docs/`
- **المشاكل:** GitHub Issues

---

**تم التطوير بواسطة:** فريق Crestal Diamond  
**الإصدار:** 3.0.0 (البنية الجديدة)  
**آخر تحديث:** يناير 2024
