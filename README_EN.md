# 💎 Crestal Diamond Workshop Management System

A comprehensive management system for diamond workshops, built with Streamlit and Python.

## 🌟 Features

### 📄 Invoice Management
- Create detailed invoices with gold and gemstone calculations
- Automatic weight and price calculations
- Customer information management
- Multi-language support (Arabic/English)

### 👥 Customer Accounts
- Interactive customer account statements
- Visual charts and analytics
- Payment tracking and history
- Customer profile management

### 🔍 Excel Data Analysis
- Import and analyze Excel/CSV files
- Data visualization and insights
- Automated data processing
- Export capabilities

### 📊 Reports & Analytics
- Interactive dashboards
- Performance indicators (KPIs)
- Financial reports
- Visual analytics with charts

### ⚙️ System Management
- Automated backups
- Settings configuration
- Data export/import
- System logs

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Streamlit
- Pandas
- Plotly

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/[your-username]/crestal-diamond-workshop.git
cd crestal-diamond-workshop
```

2. **Install dependencies:**
```bash
pip install -r config/requirements.txt
```

3. **Run the application:**
```bash
streamlit run invoice_app.py
```

Or use the quick start script:
```bash
# Windows
run.bat
```

## 📁 Project Structure

```
crestal-diamond-workshop/
├── 🎯 invoice_app.py             # Main application (6 pages)
├── 🔍 excel_analyzer.py          # Standalone Excel analyzer
├── 🚀 run.bat                    # Quick start script
├── 📁 src/                       # Organized source code
├── 📁 data/                      # Data storage
├── 📁 config/                    # Configuration files
├── 📁 docs/                      # Documentation
├── 📁 scripts/                   # Utility scripts
├── 📁 tests/                     # Test files
└── 📁 archive/                   # Legacy files
```

## 🎯 Available Pages

1. **📄 Create New Invoice** - Interactive invoice creation with automatic calculations
2. **📊 View Saved Invoices** - Invoice management with search and filtering
3. **👥 Customer Accounts** - Detailed account statements with interactive charts
4. **🔍 Excel File Analysis** - External data analysis from Excel/CSV files
5. **📈 Statistics & Reports** - Interactive reports and KPIs
6. **⚙️ Settings** - System management and backup operations

## 🛠️ Development

### Adding New Pages
1. Create file in `src/pages/`
2. Add page to main navigation
3. Update documentation

### Technology Stack
- **Frontend:** Streamlit
- **Backend:** Python 3.8+
- **Data Processing:** Pandas
- **Visualization:** Plotly, Matplotlib
- **Storage:** CSV files (expandable to SQL databases)

## 📈 Future Roadmap

- 🗄️ Advanced database integration (PostgreSQL, MySQL)
- 🌐 REST API development
- 📱 Mobile application companion
- 🔗 External system integrations
- ☁️ Cloud deployment options

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](docs/LICENSE) file for details.

## 📞 Support

For technical support or inquiries:
- **Email:** <EMAIL>
- **Documentation:** `docs/`
- **Issues:** GitHub Issues

---

**Developed by:** Crestal Diamond Team  
**Version:** 3.0.0 (New Architecture)  
**Last Updated:** January 2024

## 🌍 Languages

- [العربية (Arabic)](README.md) - الوثائق باللغة العربية
- [English](README_EN.md) - English Documentation
