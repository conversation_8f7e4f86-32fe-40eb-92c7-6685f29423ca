"""
التطبيق الرئيسي المحسن - نظام إدارة ورشة Crestal Diamond
يدمج نظام الفواتير مع تحليل ملفات Excel
"""

import streamlit as st
import pandas as pd
import math
from excel_analyzer import ExcelAnalyzer
import os
from datetime import datetime

# إعداد الصفحة
st.set_page_config(
    page_title="نظام إدارة ورشة Crestal Diamond",
    page_icon="💎",
    layout="wide",
    initial_sidebar_state="expanded"
)

# الشريط الجانبي للتنقل
st.sidebar.title("💎 Crestal Diamond")
st.sidebar.markdown("---")

# قائمة التنقل
page = st.sidebar.selectbox(
    "اختر القسم:",
    [
        "🏠 الصفحة الرئيسية",
        "📄 نظام الفواتير", 
        "🔍 تحليل ملفات Excel",
        "👥 إدارة العملاء",
        "📊 التقارير"
    ]
)

# الصفحة الرئيسية
if page == "🏠 الصفحة الرئيسية":
    st.title("🏠 نظام إدارة ورشة Crestal Diamond")
    st.markdown("---")
    
    # إحصائيات سريعة
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="📄 الفواتير اليوم",
            value="12",
            delta="3"
        )
    
    with col2:
        st.metric(
            label="👥 العملاء النشطين",
            value="45",
            delta="5"
        )
    
    with col3:
        st.metric(
            label="💰 المبيعات اليوم",
            value="$2,450",
            delta="$320"
        )
    
    with col4:
        st.metric(
            label="📦 المخزون",
            value="89%",
            delta="-2%"
        )
    
    st.markdown("---")
    
    # الأقسام المتاحة
    st.subheader("🔧 الأقسام المتاحة")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info("""
        **📄 نظام الفواتير**
        - إنشاء فواتير جديدة
        - حساب المصنعية والأحجار
        - إدارة العملتين (دولار/جنيه)
        """)
        
        st.info("""
        **👥 إدارة العملاء**
        - قاعدة بيانات العملاء
        - تتبع المعاملات
        - إدارة الحسابات
        """)
    
    with col2:
        st.info("""
        **🔍 تحليل ملفات Excel**
        - قراءة ملفات البيانات الموجودة
        - تحليل المخزون والمبيعات
        - استيراد بيانات العملاء
        """)
        
        st.info("""
        **📊 التقارير**
        - تقارير المبيعات
        - تحليل الأرباح
        - إحصائيات العمليات
        """)

# نظام الفواتير
elif page == "📄 نظام الفواتير":
    st.title("📄 نظام الفواتير")
    st.markdown("---")
    
    # معلومات الفاتورة الأساسية
    with st.container(border=True):
        st.subheader("معلومات الفاتورة الأساسية")
        col1, col2 = st.columns([3, 1])
        with col1:
            customer_name = st.text_input("👤 اسم العميل:")
        with col2:
            invoice_date = st.date_input("🗓️ تاريخ الفاتورة", value=datetime.now())
    
    # حسابات الذهب والأحجار
    with st.container(border=True):
        st.subheader("⚖️ حساب الذهب والأحجار")
        
        # القسم الخاص بالدولار
        st.markdown("**الحساب بالدولار ($)**")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            gold_weight = st.number_input("وزن الذهب (جرام)", min_value=0.0, format="%.2f")
        with col2:
            workmanship_price_usd = st.number_input("سعر مصنعية الجرام ($)", min_value=0.0, format="%.2f")
        workmanship_subtotal_usd = gold_weight * workmanship_price_usd
        
        with col3:
            stone_weight_carats = st.number_input("وزن أحجار الورشة (قيراط)", min_value=0.0, format="%.3f")
        with col4:
            stone_price_per_carat_usd = st.number_input("سعر القيراط ($)", min_value=0.0, format="%.2f")
        stone_cost_usd = stone_weight_carats * stone_price_per_carat_usd
        
        # القسم الخاص بالجنيه
        st.markdown("**الحساب بالجنيه المصري (EGP)**")
        col5, col6, col7 = st.columns(3)
        
        with col5:
            stone_count = st.number_input("عدد أحجار العميل", min_value=0, step=1)
        with col6:
            stone_setting_price_egp = st.number_input("سعر تركيب الحجر (EGP)", min_value=0.0, format="%.2f")
        stone_setting_cost_egp = stone_count * stone_setting_price_egp
    
    # الخدمات الإضافية
    with st.container(border=True):
        st.subheader("🔧 خدمات إضافية وتصليح (EGP)")
        serv_col1, serv_col2, serv_col3, serv_col4 = st.columns(4)
        
        with serv_col1:
            plating_white_egp = st.number_input("بانيو أبيض", min_value=0.0, format="%.2f")
        with serv_col2:
            plating_yellow_egp = st.number_input("بانيو أصفر", min_value=0.0, format="%.2f")
        with serv_col3:
            hallmark_egp = st.number_input("دمغة", min_value=0.0, format="%.2f")
        with serv_col4:
            repair_egp = st.number_input("تصليح", min_value=0.0, format="%.2f")
    
    # الملخص النهائي
    with st.container(border=True):
        st.header("💵 الملخص النهائي للحساب")
        
        # تجميع الحسابات
        final_usd_charge = workmanship_subtotal_usd + stone_cost_usd
        final_egp_charge = stone_setting_cost_egp + plating_white_egp + plating_yellow_egp + hallmark_egp + repair_egp
        
        round_usd_checkbox = st.checkbox("تقريب المبلغ بالدولار لأعلى رقم صحيح؟")
        if round_usd_checkbox:
            final_usd_charge = math.ceil(final_usd_charge)
        
        # عرض النتائج النهائية
        res_col1, res_col2, res_col3 = st.columns(3)
        with res_col1:
            st.metric("التغير في رصيد الذهب", f"{-gold_weight:.2f} جرام")
        with res_col2:
            st.metric("إجمالي المطلوب بالدولار", f"$ {final_usd_charge:.2f}")
        with res_col3:
            st.metric("إجمالي المطلوب بالجنيه", f"{final_egp_charge:.2f} جنيه")
        
        if st.button("💾 حفظ الفاتورة"):
            if customer_name:
                st.success(f"✅ تم تسجيل الفاتورة للعميل: {customer_name}")
                # هنا يمكن إضافة كود حفظ الفاتورة في قاعدة البيانات
            else:
                st.error("❌ يرجى إدخال اسم العميل")

# تحليل ملفات Excel
elif page == "🔍 تحليل ملفات Excel":
    st.title("🔍 تحليل ملفات Excel")
    st.markdown("---")
    
    # مسار المجلد
    default_path = r"C:\Users\<USER>\OneDrive\Desktop\crestal diamond"
    folder_path = st.text_input("📁 مسار مجلد ملفات Excel:", value=default_path)
    
    col1, col2 = st.columns([1, 3])
    
    with col1:
        analyze_button = st.button("🔍 تحليل الملفات", type="primary")
    
    if analyze_button:
        if folder_path and os.path.exists(folder_path):
            analyzer = ExcelAnalyzer(folder_path)
            
            with st.spinner("جاري تحليل الملفات..."):
                results = analyzer.analyze_all_files()
            
            if results:
                analyzer.display_analysis_results()
                st.session_state['analyzer'] = analyzer
                st.session_state['analysis_results'] = results
                st.success("✅ تم تحليل الملفات بنجاح!")
            else:
                st.error("❌ فشل في تحليل الملفات")
        else:
            st.error("❌ المسار المحدد غير صحيح أو غير موجود")

# إدارة العملاء
elif page == "👥 إدارة العملاء":
    st.title("👥 إدارة العملاء")
    st.markdown("---")
    st.info("🚧 هذا القسم قيد التطوير")

# التقارير
elif page == "📊 التقارير":
    st.title("📊 التقارير والإحصائيات")
    st.markdown("---")
    st.info("🚧 هذا القسم قيد التطوير")

# تذييل الصفحة
st.sidebar.markdown("---")
st.sidebar.markdown("💎 **Crestal Diamond Management System**")
st.sidebar.markdown("🔧 النسخة 1.0")