#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل ونهائي للنظام المتكامل
Comprehensive Final System Test
"""

import sys
import os
import time
from datetime import datetime, timedelta

# إضافة المسار للوصول للمكتبات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)

def print_section(title):
    """طباعة قسم فرعي"""
    print(f"\n📋 {title}")
    print("-" * 40)

def test_database_manager():
    """اختبار مدير قاعدة البيانات"""
    print_section("اختبار مدير قاعدة البيانات")
    
    try:
        from src.core.database import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات بنجاح")
        
        # اختبار إنشاء الملفات
        if os.path.exists('invoices.csv'):
            print("✅ ملف الفواتير موجود")
        else:
            print("❌ ملف الفواتير غير موجود")
            return False
            
        if os.path.exists('customers.csv'):
            print("✅ ملف العملاء موجود")
        else:
            print("❌ ملف العملاء غير موجود")
            return False
            
        if os.path.exists('backups'):
            print("✅ مجلد النسخ الاحتياطية موجود")
        else:
            print("❌ مجلد النسخ الاحتياطية غير موجود")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير قاعدة البيانات: {e}")
        return False

def test_invoice_operations():
    """اختبار عمليات الفواتير"""
    print_section("اختبار عمليات الفواتير")
    
    try:
        from src.core.database import DatabaseManager
        
        db = DatabaseManager()
        
        # إنشاء فواتير تجريبية متنوعة
        test_invoices = [
            {
                'customer_name': 'أحمد محمد علي',
                'date': '2025-07-02',
                'description': 'خاتم ذهب عيار 21 قيراط',
                'gold_change': -2.5,
                'usd_change': 85.0,
                'egp_change': 2635.0
            },
            {
                'customer_name': 'فاطمة حسن',
                'date': '2025-07-02',
                'description': 'سوار ذهب مع أحجار كريمة',
                'gold_change': -4.2,
                'usd_change': 145.0,
                'egp_change': 4495.0
            },
            {
                'customer_name': 'محمد عبدالله',
                'date': '2025-07-02',
                'description': 'قلادة ذهب للزفاف',
                'gold_change': -3.8,
                'usd_change': 125.0,
                'egp_change': 3875.0
            }
        ]
        
        # حفظ الفواتير
        saved_count = 0
        for i, invoice in enumerate(test_invoices):
            # إضافة timestamp فريد لكل فاتورة
            current_time = datetime.now() + timedelta(seconds=i)
            invoice['timestamp'] = current_time.strftime('%Y-%m-%d %H:%M:%S')
            
            if db.save_invoice(invoice):
                saved_count += 1
                print(f"✅ تم حفظ فاتورة {invoice['customer_name']}")
            else:
                print(f"❌ فشل في حفظ فاتورة {invoice['customer_name']}")
        
        print(f"📊 تم حفظ {saved_count}/{len(test_invoices)} فاتورة")
        
        # اختبار تحميل الفواتير
        invoices_df = db.load_invoices()
        if not invoices_df.empty:
            print(f"✅ تم تحميل {len(invoices_df)} فاتورة من قاعدة البيانات")
            
            # التحقق من وجود التاريخ والوقت
            if 'timestamp' in invoices_df.columns:
                timestamps_count = invoices_df['timestamp'].notna().sum()
                print(f"✅ {timestamps_count} فاتورة تحتوي على التاريخ والوقت")
            else:
                print("❌ عمود التاريخ والوقت غير موجود")
                return False
            
            return saved_count == len(test_invoices)
        else:
            print("❌ لم يتم تحميل أي فواتير")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات الفواتير: {e}")
        return False

def test_customer_operations():
    """اختبار عمليات العملاء"""
    print_section("اختبار عمليات العملاء")
    
    try:
        from src.core.database import DatabaseManager
        
        db = DatabaseManager()
        
        # إنشاء عملاء تجريبيين
        test_customers = [
            {
                'name': 'سارة أحمد محمد',
                'phone': '01234567890',
                'email': '<EMAIL>',
                'address': 'القاهرة، مصر',
                'notes': 'عميلة مميزة - تفضل الذهب عيار 21',
                'created_date': datetime.now().strftime('%Y-%m-%d')
            },
            {
                'name': 'خالد عبدالرحمن',
                'phone': '01987654321',
                'email': '<EMAIL>',
                'address': 'الإسكندرية، مصر',
                'notes': 'يطلب تصاميم حديثة',
                'created_date': datetime.now().strftime('%Y-%m-%d')
            }
        ]
        
        # حفظ العملاء
        saved_customers = 0
        for customer in test_customers:
            if db.save_customer(customer):
                saved_customers += 1
                print(f"✅ تم حفظ العميل {customer['name']}")
            else:
                print(f"❌ فشل في حفظ العميل {customer['name']}")
        
        # تحميل العملاء
        customers_df = db.load_customers()
        if not customers_df.empty:
            print(f"✅ تم تحميل {len(customers_df)} عميل من قاعدة البيانات")
            return saved_customers == len(test_customers)
        else:
            print("❌ لم يتم تحميل أي عملاء")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات العملاء: {e}")
        return False

def test_ai_system():
    """اختبار نظام الذكاء الاصطناعي"""
    print_section("اختبار نظام الذكاء الاصطناعي")
    
    try:
        # اختبار استيراد نظام الذكاء الاصطناعي
        try:
            from memory.streamlit_integration import (
                get_pricing_suggestion, 
                get_ai_recommendations,
                analyze_sales_data
            )
            print("✅ تم استيراد نظام الذكاء الاصطناعي بنجاح")
            ai_available = True
        except ImportError as e:
            print(f"⚠️ نظام الذكاء الاصطناعي غير متوفر: {e}")
            ai_available = False
        
        if ai_available:
            # اختبار مساعد التسعير
            try:
                test_item = {
                    'gold_weight': 3.0,
                    'gold_karat': 21,
                    'stone_weight': 0.5,
                    'customer_segment': 'متوسط',
                    'cost': 3000
                }
                
                suggestion = get_pricing_suggestion(test_item)
                if suggestion:
                    print("✅ مساعد التسعير الذكي يعمل")
                else:
                    print("⚠️ مساعد التسعير يعمل لكن بدون نتائج")
                    
            except Exception as e:
                print(f"❌ خطأ في مساعد التسعير: {e}")
            
            # اختبار التوصيات الذكية
            try:
                recommendations = get_ai_recommendations({
                    'type': 'general',
                    'context': 'test'
                })
                if recommendations:
                    print("✅ نظام التوصيات الذكية يعمل")
                else:
                    print("⚠️ نظام التوصيات يعمل لكن بدون نتائج")
                    
            except Exception as e:
                print(f"❌ خطأ في نظام التوصيات: {e}")
        
        return ai_available
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الذكاء الاصطناعي: {e}")
        return False

def test_pages_import():
    """اختبار استيراد جميع الصفحات"""
    print_section("اختبار استيراد صفحات النظام")
    
    pages = [
        ('src.pages.crystal_ai', 'كريستال - المساعد الذكي'),
        ('src.pages.invoice_creation', 'إنشاء فاتورة جديدة'),
        ('src.pages.invoice_list', 'عرض الفواتير'),
        ('src.pages.customer_accounts', 'حسابات العملاء'),
        ('src.pages.excel_analysis', 'تحليل ملفات Excel'),
        ('src.pages.reports', 'الإحصائيات والتقارير'),
        ('src.pages.settings', 'الإعدادات')
    ]
    
    imported_pages = 0
    for module_name, page_name in pages:
        try:
            __import__(module_name)
            print(f"✅ {page_name}")
            imported_pages += 1
        except Exception as e:
            print(f"❌ {page_name}: {e}")
    
    print(f"📊 تم استيراد {imported_pages}/{len(pages)} صفحة بنجاح")
    return imported_pages == len(pages)

def test_main_app():
    """اختبار الملف الرئيسي للتطبيق"""
    print_section("اختبار الملف الرئيسي")
    
    try:
        import main
        print("✅ تم استيراد main.py بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في استيراد main.py: {e}")
        return False

def test_file_structure():
    """اختبار بنية الملفات"""
    print_section("اختبار بنية الملفات")
    
    required_files = [
        'main.py',
        'src/core/database.py',
        'src/pages/crystal_ai.py',
        'src/pages/invoice_creation.py',
        'src/pages/invoice_list.py',
        'src/pages/customer_accounts.py',
        'src/pages/excel_analysis.py',
        'src/pages/reports.py',
        'src/pages/settings.py',
        'memory/streamlit_integration.py'
    ]
    
    existing_files = 0
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
            existing_files += 1
        else:
            print(f"❌ {file_path} غير موجود")
    
    print(f"📊 {existing_files}/{len(required_files)} ملف موجود")
    return existing_files == len(required_files)

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print_header("اختبار شامل ونهائي للنظام المتكامل")
    
    tests = [
        ("بنية الملفات", test_file_structure),
        ("مدير قاعدة البيانات", test_database_manager),
        ("عمليات الفواتير", test_invoice_operations),
        ("عمليات العملاء", test_customer_operations),
        ("استيراد الصفحات", test_pages_import),
        ("الملف الرئيسي", test_main_app),
        ("نظام الذكاء الاصطناعي", test_ai_system)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    start_time = time.time()
    
    for test_name, test_function in tests:
        print_header(f"اختبار {test_name}")
        try:
            if test_function():
                print(f"✅ اختبار {test_name} نجح")
                passed_tests += 1
            else:
                print(f"❌ اختبار {test_name} فشل")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    # النتيجة النهائية
    print_header("النتيجة النهائية")
    print(f"⏱️ وقت الاختبار: {duration:.2f} ثانية")
    print(f"📊 الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام المتكامل جاهز للاستخدام")
        print("✅ جميع الميزات تعمل بشكل صحيح")
        print("✅ قاعدة البيانات تعمل بشكل مثالي")
        print("✅ نظام الذكاء الاصطناعي متكامل")
        print("\n🚀 يمكنك الآن تشغيل النظام باستخدام:")
        print("   streamlit run main.py")
        return True
    else:
        failed_tests = total_tests - passed_tests
        print(f"\n⚠️ {failed_tests} اختبار فشل")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
