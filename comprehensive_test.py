#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام إدارة فواتير ورشة الماس
Comprehensive Test for Crestal Diamond Workshop Invoice System
"""

import mysql.connector
from mysql.connector import Error
import os
from datetime import datetime, date
import json

class DatabaseTester:
    def __init__(self):
        """تهيئة فئة الاختبار"""
        self.connection = None
        self.cursor = None
        self.test_results = []
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = mysql.connector.connect(
                host='localhost',
                database='crestal_diamond_workshop',
                user='root',
                password='2452329511',
                charset='utf8mb4',
                collation='utf8mb4_unicode_ci'
            )
            
            if self.connection.is_connected():
                self.cursor = self.connection.cursor(dictionary=True)
                return True
        except Error as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        if self.cursor:
            self.cursor.close()
        if self.connection and self.connection.is_connected():
            self.connection.close()
    
    def run_test(self, test_name, test_function):
        """تشغيل اختبار واحد"""
        print(f"\n🔍 {test_name}...")
        try:
            result = test_function()
            if result:
                print(f"✅ {test_name} - نجح")
                self.test_results.append((test_name, True, None))
            else:
                print(f"❌ {test_name} - فشل")
                self.test_results.append((test_name, False, "Test returned False"))
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
            self.test_results.append((test_name, False, str(e)))
    
    def test_connection(self):
        """اختبار الاتصال"""
        return self.connection and self.connection.is_connected()
    
    def test_tables_exist(self):
        """اختبار وجود الجداول"""
        expected_tables = ['customers', 'invoices', 'invoice_details', 'services', 'activity_log']
        
        self.cursor.execute("SHOW TABLES")
        existing_tables = [list(table.values())[0] for table in self.cursor.fetchall()]
        
        print(f"📋 الجداول الموجودة: {existing_tables}")
        
        for table in expected_tables:
            if table not in existing_tables:
                print(f"❌ الجدول {table} غير موجود")
                return False
            print(f"✅ الجدول {table} موجود")
        
        return True
    
    def test_services_data(self):
        """اختبار بيانات الخدمات"""
        self.cursor.execute("SELECT COUNT(*) as count FROM services")
        count = self.cursor.fetchone()['count']
        
        if count == 0:
            print("⚠️ لا توجد خدمات في قاعدة البيانات")
            return False
        
        print(f"📊 عدد الخدمات: {count}")
        
        self.cursor.execute("SELECT name, category, default_price FROM services LIMIT 3")
        services = self.cursor.fetchall()
        
        print("📋 عينة من الخدمات:")
        for service in services:
            print(f"  - {service['name']} ({service['category']}): {service['default_price']} جنيه")
        
        return True
    
    def test_insert_customer(self):
        """اختبار إدراج عميل جديد"""
        test_customer = {
            'name': 'عميل تجريبي',
            'phone': '01234567890',
            'email': '<EMAIL>',
            'address': 'عنوان تجريبي',
            'notes': 'عميل للاختبار'
        }
        
        insert_query = """
        INSERT INTO customers (name, phone, email, address, notes)
        VALUES (%(name)s, %(phone)s, %(email)s, %(address)s, %(notes)s)
        """
        
        self.cursor.execute(insert_query, test_customer)
        customer_id = self.cursor.lastrowid
        
        # التحقق من الإدراج
        self.cursor.execute("SELECT * FROM customers WHERE id = %s", (customer_id,))
        inserted_customer = self.cursor.fetchone()
        
        if inserted_customer and inserted_customer['name'] == test_customer['name']:
            print(f"✅ تم إدراج العميل بنجاح - ID: {customer_id}")
            return customer_id
        
        return False
    
    def test_insert_invoice(self, customer_id):
        """اختبار إدراج فاتورة جديدة"""
        test_invoice = {
            'invoice_number': f'INV-TEST-{datetime.now().strftime("%Y%m%d%H%M%S")}',
            'customer_id': customer_id,
            'invoice_date': date.today(),
            'gold_weight': 10.5,
            'gold_karat': 21,
            'gold_price_per_gram': 3500.00,
            'gold_total': 36750.00,
            'stone_weight': 2.0,
            'stone_price_per_carat': 1000.00,
            'stone_total': 2000.00,
            'services_total': 500.00,
            'subtotal': 39250.00,
            'tax_rate': 14.00,
            'tax_amount': 5495.00,
            'total_amount': 44745.00,
            'currency': 'EGP',
            'status': 'draft'
        }
        
        insert_query = """
        INSERT INTO invoices (
            invoice_number, customer_id, invoice_date, gold_weight, gold_karat,
            gold_price_per_gram, gold_total, stone_weight, stone_price_per_carat,
            stone_total, services_total, subtotal, tax_rate, tax_amount, total_amount,
            currency, status
        ) VALUES (
            %(invoice_number)s, %(customer_id)s, %(invoice_date)s, %(gold_weight)s,
            %(gold_karat)s, %(gold_price_per_gram)s, %(gold_total)s, %(stone_weight)s,
            %(stone_price_per_carat)s, %(stone_total)s, %(services_total)s,
            %(subtotal)s, %(tax_rate)s, %(tax_amount)s, %(total_amount)s,
            %(currency)s, %(status)s
        )
        """
        
        self.cursor.execute(insert_query, test_invoice)
        invoice_id = self.cursor.lastrowid
        
        # التحقق من الإدراج
        self.cursor.execute("SELECT * FROM invoices WHERE id = %s", (invoice_id,))
        inserted_invoice = self.cursor.fetchone()
        
        if inserted_invoice and inserted_invoice['invoice_number'] == test_invoice['invoice_number']:
            print(f"✅ تم إدراج الفاتورة بنجاح - ID: {invoice_id}")
            print(f"📄 رقم الفاتورة: {test_invoice['invoice_number']}")
            print(f"💰 المبلغ الإجمالي: {test_invoice['total_amount']} {test_invoice['currency']}")
            return invoice_id
        
        return False
    
    def test_database_queries(self):
        """اختبار الاستعلامات المختلفة"""
        queries = [
            ("عدد العملاء", "SELECT COUNT(*) as count FROM customers"),
            ("عدد الفواتير", "SELECT COUNT(*) as count FROM invoices"),
            ("إجمالي المبيعات", "SELECT SUM(total_amount) as total FROM invoices WHERE status != 'cancelled'"),
            ("الفواتير المعلقة", "SELECT COUNT(*) as count FROM invoices WHERE payment_status = 'pending'")
        ]
        
        for query_name, query in queries:
            self.cursor.execute(query)
            result = self.cursor.fetchone()
            print(f"📊 {query_name}: {list(result.values())[0] or 0}")
        
        return True
    
    def test_cleanup(self):
        """تنظيف البيانات التجريبية"""
        try:
            # حذف الفواتير التجريبية
            self.cursor.execute("DELETE FROM invoices WHERE invoice_number LIKE 'INV-TEST-%'")
            deleted_invoices = self.cursor.rowcount
            
            # حذف العملاء التجريبيين
            self.cursor.execute("DELETE FROM customers WHERE name = 'عميل تجريبي'")
            deleted_customers = self.cursor.rowcount
            
            print(f"🧹 تم حذف {deleted_invoices} فاتورة تجريبية و {deleted_customers} عميل تجريبي")
            return True
        except Exception as e:
            print(f"⚠️ خطأ في التنظيف: {e}")
            return False
    
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        print("🧪 بدء الاختبار الشامل لنظام إدارة فواتير ورشة الماس")
        print("=" * 60)
        
        if not self.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # تشغيل الاختبارات
        self.run_test("اختبار الاتصال", self.test_connection)
        self.run_test("اختبار وجود الجداول", self.test_tables_exist)
        self.run_test("اختبار بيانات الخدمات", self.test_services_data)
        
        # اختبار العمليات
        customer_id = None
        invoice_id = None
        
        try:
            customer_id = self.test_insert_customer()
            if customer_id:
                self.test_results.append(("إدراج عميل تجريبي", True, None))
                
                invoice_id = self.test_insert_invoice(customer_id)
                if invoice_id:
                    self.test_results.append(("إدراج فاتورة تجريبية", True, None))
                else:
                    self.test_results.append(("إدراج فاتورة تجريبية", False, "فشل في إدراج الفاتورة"))
            else:
                self.test_results.append(("إدراج عميل تجريبي", False, "فشل في إدراج العميل"))
        except Exception as e:
            print(f"❌ خطأ في اختبار العمليات: {e}")
        
        self.run_test("اختبار الاستعلامات", self.test_database_queries)
        self.run_test("تنظيف البيانات التجريبية", self.test_cleanup)
        
        # عرض النتائج
        self.show_results()
        
        self.disconnect()
        return True
    
    def show_results(self):
        """عرض نتائج الاختبارات"""
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبار الشامل")
        print("=" * 60)
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success, error in self.test_results:
            status = "✅ نجح" if success else "❌ فشل"
            print(f"{status} - {test_name}")
            if error:
                print(f"    خطأ: {error}")
            if success:
                passed += 1
        
        print("\n" + "=" * 60)
        print(f"📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
        
        if passed == total:
            print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء")
        
        print("=" * 60)

if __name__ == "__main__":
    tester = DatabaseTester()
    tester.run_comprehensive_test()
