# مكتبات أساسية لتطبيق Streamlit
streamlit>=1.28.0

# مكتبات تحليل البيانات وقراءة ملفات Excel
pandas>=2.0.0
openpyxl>=3.1.0          # لقراءة ملفات Excel (.xlsx)
xlrd>=2.0.0              # لقراءة ملفات Excel القديمة (.xls)
xlsxwriter>=3.1.0        # لكتابة ملفات Excel

# مكتبات قواعد البيانات
# sqlite3 مدمجة مع Python (لا تحتاج تثبيت)
sqlalchemy>=2.0.0        # للتعامل مع قواعد البيانات

# مكتبات الرسوم البيانية والتصور
plotly>=5.15.0           # للرسوم البيانية التفاعلية
matplotlib>=3.7.0        # للرسوم البيانية الأساسية
seaborn>=0.12.0          # للرسوم البيانية المتقدمة

# مكتبات معالجة النصوص والتواريخ
python-dateutil>=2.8.0   # لمعالجة التواريخ
pytz>=2023.3             # للتوقيتات الزمنية

# مكتبات التشفير والأمان
cryptography>=41.0.0     # للتشفير وحماية البيانات

# مكتبات إضافية مفيدة
numpy>=1.24.0            # للعمليات الرياضية
pillow>=10.0.0           # لمعالجة الصور
requests>=2.31.0         # للطلبات HTTP

# مكتبات واجهة المستخدم المحسنة
streamlit-aggrid>=0.3.4  # لجداول تفاعلية متقدمة
streamlit-option-menu>=0.3.6  # لقوائم تنقل محسنة
streamlit-extras>=0.3.0  # لمكونات إضافية

# مكتبات التحقق من صحة البيانات
pydantic>=2.0.0          # للتحقق من صحة البيانات
