"""
إعدادات التطبيق الرئيسية - Crestal Diamond Management System
"""

import os
from pathlib import Path

# مسارات المشروع
PROJECT_ROOT = Path(__file__).parent.parent
SRC_DIR = PROJECT_ROOT / "src"
DATA_DIR = PROJECT_ROOT / "data"
DOCS_DIR = PROJECT_ROOT / "docs"
LOGS_DIR = PROJECT_ROOT / "logs"
ASSETS_DIR = PROJECT_ROOT / "assets"

# مسارات البيانات
INVOICES_DIR = DATA_DIR / "invoices"
CUSTOMERS_DIR = DATA_DIR / "customers"
EXPORTS_DIR = DATA_DIR / "exports"
BACKUPS_DIR = DATA_DIR / "backups"

# ملفات البيانات
INVOICES_FILE = INVOICES_DIR / "invoices.csv"
CUSTOMERS_FILE = CUSTOMERS_DIR / "customers.csv"

# إعدادات التطبيق
APP_TITLE = "نظام إدارة ورشة Crestal Diamond"
APP_ICON = "💎"
APP_VERSION = "2.1.0"

# إعدادات Streamlit
STREAMLIT_CONFIG = {
    "layout": "wide",
    "page_title": APP_TITLE,
    "page_icon": APP_ICON,
    "initial_sidebar_state": "expanded"
}

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    "type": "csv",  # يمكن تغييرها إلى sqlite أو postgresql لاحقاً
    "encoding": "utf-8-sig",
    "backup_enabled": True,
    "backup_interval": 24  # ساعات
}

# إعدادات العملات
CURRENCIES = {
    "USD": {
        "symbol": "$",
        "name": "دولار أمريكي",
        "decimal_places": 2
    },
    "EGP": {
        "symbol": "ج.م",
        "name": "جنيه مصري",
        "decimal_places": 2
    }
}

# إعدادات Excel
EXCEL_CONFIG = {
    "default_path": r"C:\Users\<USER>\OneDrive\Desktop\crestal diamond",
    "supported_formats": [".xlsx", ".xls", ".csv"],
    "max_file_size": 50 * 1024 * 1024,  # 50 MB
    "encoding": "utf-8-sig"
}

# إعدادات التصدير
EXPORT_CONFIG = {
    "date_format": "%Y-%m-%d",
    "datetime_format": "%Y-%m-%d %H:%M:%S",
    "filename_format": "{type}_{timestamp}",
    "default_encoding": "utf-8-sig"
}

# إعدادات السجلات
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": LOGS_DIR / "app.log",
    "max_size": 10 * 1024 * 1024,  # 10 MB
    "backup_count": 5
}

# إعدادات الأمان
SECURITY_CONFIG = {
    "enable_backup": True,
    "max_login_attempts": 3,
    "session_timeout": 3600,  # ثانية
    "encrypt_sensitive_data": False  # للمستقبل
}

# إعدادات واجهة المستخدم
UI_CONFIG = {
    "theme": "light",
    "language": "ar",
    "date_format": "dd/mm/yyyy",
    "number_format": "arabic",
    "items_per_page": 20
}

# إعدادات التقارير
REPORTS_CONFIG = {
    "chart_height": 400,
    "chart_width": 600,
    "default_chart_type": "bar",
    "color_palette": ["#FFD700", "#1E3A8A", "#10B981", "#EF4444", "#F59E0B"]
}

# رسائل النظام
MESSAGES = {
    "success": {
        "invoice_saved": "✅ تم حفظ الفاتورة بنجاح",
        "data_exported": "✅ تم تصدير البيانات بنجاح",
        "backup_created": "✅ تم إنشاء نسخة احتياطية"
    },
    "error": {
        "file_not_found": "❌ الملف غير موجود",
        "invalid_data": "❌ البيانات غير صحيحة",
        "permission_denied": "❌ ليس لديك صلاحية للوصول"
    },
    "warning": {
        "no_data": "⚠️ لا توجد بيانات للعرض",
        "large_file": "⚠️ حجم الملف كبير، قد يستغرق وقتاً أطول"
    },
    "info": {
        "loading": "🔄 جاري التحميل...",
        "processing": "⚙️ جاري المعالجة...",
        "feature_coming": "🚧 هذه الميزة قيد التطوير"
    }
}

# إنشاء المجلدات إذا لم تكن موجودة
def create_directories():
    """إنشاء جميع المجلدات المطلوبة"""
    directories = [
        DATA_DIR, INVOICES_DIR, CUSTOMERS_DIR, EXPORTS_DIR, 
        BACKUPS_DIR, LOGS_DIR, ASSETS_DIR
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

# تشغيل إنشاء المجلدات عند استيراد الملف
create_directories()
