# 🗄️ مجلد قاعدة البيانات - Database Folder

## 📁 محتويات المجلد - Folder Contents

### 📋 ملفات الإعداد - Setup Files
- **`setup.sql`** - سكريبت إنشاء قاعدة البيانات والجداول
- **`config.py`** - إعدادات الاتصال بقاعدة البيانات
- **`manager.py`** - مدير قاعدة البيانات الموحد

### 🧪 ملفات الاختبار - Test Files
- **`test_connection.py`** - اختبار الاتصال بقاعدة البيانات
- **`test_operations.py`** - اختبار العمليات المتقدمة والشاملة

### 📚 ملفات التوثيق - Documentation Files
- **`SETUP_GUIDE.md`** - دليل الإعداد التفصيلي
- **`SCHEMA.md`** - توثيق بنية قاعدة البيانات

### 🔧 ملفات المساعدة - Utility Files
- **`migrate.py`** - ترحيل البيانات من CSV إلى MySQL
- **`backup.py`** - نسخ احتياطية لقاعدة البيانات

## 🚀 الاستخدام السريع - Quick Usage

### إعداد قاعدة البيانات:
```bash
# من المجلد الرئيسي
python database/config.py
```

### اختبار الاتصال:
```bash
python database/test_connection.py
```

### اختبار العمليات المتقدمة:
```bash
python database/test_operations.py
```

### تشغيل سكريبت الإعداد:
```sql
-- في MySQL Workbench
SOURCE database/setup.sql;
```

## 📊 بنية قاعدة البيانات - Database Structure

### الجداول الرئيسية:
- **customers** - بيانات العملاء
- **invoices** - الفواتير
- **invoice_details** - تفاصيل الفواتير
- **services** - الخدمات المتاحة
- **activity_log** - سجل النشاطات

## 🔗 الربط مع التطبيق - Application Integration

يتم استيراد مدير قاعدة البيانات في التطبيق الرئيسي:
```python
from database.manager import DatabaseManager
```

## 📝 ملاحظات مهمة - Important Notes

- جميع ملفات قاعدة البيانات منظمة في هذا المجلد
- يتم الحفاظ على النظام الاحتياطي CSV
- إعدادات الأمان في ملف `.env` في المجلد الرئيسي
- جميع الاختبارات والأدوات متوفرة في هذا المجلد
