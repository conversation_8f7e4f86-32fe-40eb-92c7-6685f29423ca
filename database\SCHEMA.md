# 📊 مخطط قاعدة البيانات - Database Schema

## 🎯 نظرة عامة - Overview

قاعدة بيانات نظام إدارة فواتير ورشة الماس الكريستالي مصممة لإدارة:
- بيانات العملاء
- الفواتير وتفاصيلها
- الخدمات المتاحة
- سجل النشاطات

## 📋 الجداول - Tables

### 1. جدول العملاء - `customers`

```sql
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### الحقول - Fields:
| الحقل | النوع | الوصف | قيود |
|-------|-------|--------|------|
| `id` | INT | المعرف الفريد | PRIMARY KEY, AUTO_INCREMENT |
| `name` | VARCHAR(255) | اسم العميل | NOT NULL |
| `phone` | VARCHAR(20) | رقم الهاتف | اختياري |
| `email` | VARCHAR(255) | البريد الإلكتروني | اختياري |
| `address` | TEXT | العنوان | اختياري |
| `notes` | TEXT | ملاحظات | اختياري |
| `created_date` | TIMESTAMP | تاريخ الإنشاء | DEFAULT CURRENT_TIMESTAMP |
| `updated_date` | TIMESTAMP | تاريخ التحديث | ON UPDATE CURRENT_TIMESTAMP |

---

### 2. جدول الفواتير - `invoices`

```sql
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT,
    invoice_date DATE NOT NULL,
    due_date DATE,
    
    -- تفاصيل الذهب
    gold_weight DECIMAL(10,3) DEFAULT 0,
    gold_karat INT DEFAULT 0,
    gold_price_per_gram DECIMAL(10,2) DEFAULT 0,
    gold_total DECIMAL(12,2) DEFAULT 0,
    
    -- تفاصيل الأحجار
    stone_weight DECIMAL(10,3) DEFAULT 0,
    stone_price_per_carat DECIMAL(10,2) DEFAULT 0,
    stone_total DECIMAL(12,2) DEFAULT 0,
    
    -- الخدمات الإضافية
    additional_services TEXT,
    services_total DECIMAL(12,2) DEFAULT 0,
    
    -- المجاميع
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    
    -- العملة والدفع
    currency ENUM('USD', 'EGP') DEFAULT 'USD',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
    paid_amount DECIMAL(12,2) DEFAULT 0,
    
    -- ملاحظات وحالة
    notes TEXT,
    status ENUM('draft', 'sent', 'paid', 'cancelled') DEFAULT 'draft',
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
);
```

#### الحقول الرئيسية - Main Fields:
| الحقل | النوع | الوصف |
|-------|-------|--------|
| `id` | INT | المعرف الفريد |
| `invoice_number` | VARCHAR(50) | رقم الفاتورة (فريد) |
| `customer_id` | INT | معرف العميل (مفتاح خارجي) |
| `invoice_date` | DATE | تاريخ الفاتورة |
| `due_date` | DATE | تاريخ الاستحقاق |

#### حقول الذهب - Gold Fields:
| الحقل | النوع | الوصف |
|-------|-------|--------|
| `gold_weight` | DECIMAL(10,3) | وزن الذهب بالجرام |
| `gold_karat` | INT | عيار الذهب |
| `gold_price_per_gram` | DECIMAL(10,2) | سعر الجرام |
| `gold_total` | DECIMAL(12,2) | إجمالي قيمة الذهب |

#### حقول الأحجار - Stone Fields:
| الحقل | النوع | الوصف |
|-------|-------|--------|
| `stone_weight` | DECIMAL(10,3) | وزن الأحجار بالقيراط |
| `stone_price_per_carat` | DECIMAL(10,2) | سعر القيراط |
| `stone_total` | DECIMAL(12,2) | إجمالي قيمة الأحجار |

#### حقول المالية - Financial Fields:
| الحقل | النوع | الوصف |
|-------|-------|--------|
| `subtotal` | DECIMAL(12,2) | المجموع الفرعي |
| `tax_rate` | DECIMAL(5,2) | معدل الضريبة (%) |
| `tax_amount` | DECIMAL(12,2) | مبلغ الضريبة |
| `discount_amount` | DECIMAL(12,2) | مبلغ الخصم |
| `total_amount` | DECIMAL(12,2) | المبلغ الإجمالي |
| `currency` | ENUM | العملة (USD/EGP) |
| `exchange_rate` | DECIMAL(10,4) | سعر الصرف |

#### حقول الحالة - Status Fields:
| الحقل | النوع | القيم المسموحة |
|-------|-------|----------------|
| `payment_status` | ENUM | pending, partial, paid, overdue |
| `status` | ENUM | draft, sent, paid, cancelled |

---

### 3. جدول تفاصيل الفواتير - `invoice_details`

```sql
CREATE TABLE invoice_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    item_type ENUM('gold', 'stone', 'service') NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(10,3) DEFAULT 1,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(12,2) DEFAULT 0,
    
    -- تفاصيل خاصة بالذهب
    karat INT NULL,
    gold_type VARCHAR(100) NULL,
    
    -- تفاصيل خاصة بالأحجار
    stone_type VARCHAR(100) NULL,
    clarity VARCHAR(50) NULL,
    color VARCHAR(50) NULL,
    cut_quality VARCHAR(50) NULL,
    
    -- تفاصيل خاصة بالخدمات
    service_category VARCHAR(100) NULL,
    labor_hours DECIMAL(5,2) NULL,
    
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);
```

#### الحقول - Fields:
| الحقل | النوع | الوصف |
|-------|-------|--------|
| `id` | INT | المعرف الفريد |
| `invoice_id` | INT | معرف الفاتورة |
| `item_type` | ENUM | نوع العنصر (gold/stone/service) |
| `description` | TEXT | وصف العنصر |
| `quantity` | DECIMAL(10,3) | الكمية |
| `unit_price` | DECIMAL(10,2) | سعر الوحدة |
| `total_price` | DECIMAL(12,2) | السعر الإجمالي |

#### حقول خاصة بالذهب:
- `karat` - العيار
- `gold_type` - نوع الذهب

#### حقول خاصة بالأحجار:
- `stone_type` - نوع الحجر
- `clarity` - النقاء
- `color` - اللون
- `cut_quality` - جودة القطع

#### حقول خاصة بالخدمات:
- `service_category` - فئة الخدمة
- `labor_hours` - ساعات العمل

---

### 4. جدول الخدمات - `services`

```sql
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    default_price DECIMAL(10,2) DEFAULT 0,
    unit VARCHAR(50) DEFAULT 'piece',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### الحقول - Fields:
| الحقل | النوع | الوصف |
|-------|-------|--------|
| `id` | INT | المعرف الفريد |
| `name` | VARCHAR(255) | اسم الخدمة |
| `description` | TEXT | وصف الخدمة |
| `category` | VARCHAR(100) | فئة الخدمة |
| `default_price` | DECIMAL(10,2) | السعر الافتراضي |
| `unit` | VARCHAR(50) | وحدة القياس |
| `is_active` | BOOLEAN | حالة النشاط |

---

### 5. جدول سجل النشاطات - `activity_log`

```sql
CREATE TABLE activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    user_info VARCHAR(255) NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### الحقول - Fields:
| الحقل | النوع | الوصف |
|-------|-------|--------|
| `id` | INT | المعرف الفريد |
| `table_name` | VARCHAR(100) | اسم الجدول |
| `record_id` | INT | معرف السجل |
| `action` | ENUM | نوع العملية (INSERT/UPDATE/DELETE) |
| `old_values` | JSON | القيم القديمة |
| `new_values` | JSON | القيم الجديدة |
| `user_info` | VARCHAR(255) | معلومات المستخدم |
| `timestamp` | TIMESTAMP | وقت العملية |

---

## 🔗 العلاقات - Relationships

### العلاقات الرئيسية:
1. **customers → invoices**: علاقة واحد إلى متعدد
2. **invoices → invoice_details**: علاقة واحد إلى متعدد

### المفاتيح الخارجية:
- `invoices.customer_id` → `customers.id`
- `invoice_details.invoice_id` → `invoices.id`

---

## 📇 الفهارس - Indexes

### فهارس الأداء:
```sql
-- فهارس جدول الفواتير
INDEX idx_invoice_number (invoice_number)
INDEX idx_customer_id (customer_id)
INDEX idx_invoice_date (invoice_date)
INDEX idx_status (status)
INDEX idx_payment_status (payment_status)

-- فهارس جدول تفاصيل الفواتير
INDEX idx_invoice_id (invoice_id)
INDEX idx_item_type (item_type)

-- فهارس جدول الخدمات
INDEX idx_category (category)
INDEX idx_is_active (is_active)

-- فهارس جدول سجل النشاطات
INDEX idx_table_record (table_name, record_id)
INDEX idx_timestamp (timestamp)
```

---

## 🔧 القيود والقواعد - Constraints & Rules

### قيود الفريدة:
- `invoices.invoice_number` - رقم فاتورة فريد

### قيود المفاتيح الخارجية:
- `ON DELETE SET NULL` للعملاء (الاحتفاظ بالفواتير عند حذف العميل)
- `ON DELETE CASCADE` لتفاصيل الفواتير (حذف التفاصيل عند حذف الفاتورة)

### قيود التحقق:
- أسعار موجبة أو صفر
- أوزان موجبة أو صفر
- تواريخ صحيحة

---

## 📊 البيانات الأولية - Initial Data

### خدمات افتراضية:
```sql
INSERT INTO services (name, description, category, default_price, unit) VALUES
('تصميم مجوهرات', 'تصميم قطعة مجوهرات حسب الطلب', 'تصميم', 500.00, 'قطعة'),
('تلميع وتنظيف', 'تلميع وتنظيف المجوهرات', 'صيانة', 50.00, 'قطعة'),
('إصلاح سلسلة', 'إصلاح وتقوية السلاسل', 'إصلاح', 100.00, 'قطعة'),
('تركيب حجر', 'تركيب الأحجار الكريمة', 'تركيب', 200.00, 'حجر'),
('تغيير مقاس خاتم', 'تعديل مقاس الخواتم', 'تعديل', 75.00, 'قطعة'),
('نقش أسماء', 'نقش الأسماء والتواريخ', 'نقش', 150.00, 'قطعة');
```

---

## 🔄 إصدارات المخطط - Schema Versions

### الإصدار 1.0.0 (الحالي)
- إنشاء الجداول الأساسية
- دعم العملات المتعددة
- نظام سجل النشاطات

### تحديثات مستقبلية محتملة:
- دعم المرفقات والصور
- نظام الخصومات المتقدم
- تتبع المخزون
- تقارير متقدمة

---

## 📝 ملاحظات التطوير

### اعتبارات الأداء:
- استخدام الفهارس المناسبة
- تحسين الاستعلامات المعقدة
- تنظيف البيانات القديمة دورياً

### اعتبارات الأمان:
- تشفير البيانات الحساسة
- تدقيق العمليات
- نسخ احتياطية منتظمة

### اعتبارات التوسع:
- دعم قواعد بيانات متعددة
- تقسيم البيانات (Partitioning)
- نسخ متماثل (Replication)
