# 📚 دليل إعداد قاعدة البيانات - Database Setup Guide

## 🎯 نظرة عامة - Overview

هذا الدليل يوضح كيفية إعداد قاعدة البيانات MySQL لنظام إدارة فواتير ورشة الماس الكريستالي.

## 📋 المتطلبات - Requirements

### 1. برامج مطلوبة - Required Software
- **MySQL Server 8.0+** أو **MariaDB 10.5+**
- **Python 3.8+**
- **MySQL Workbench** (اختياري للإدارة المرئية)

### 2. مكتبات Python المطلوبة
```bash
pip install mysql-connector-python pandas streamlit
```

## 🚀 خطوات الإعداد - Setup Steps

### الخطوة 1: تثبيت MySQL Server

#### على Windows:
1. تحميل MySQL من [الموقع الرسمي](https://dev.mysql.com/downloads/mysql/)
2. تشغيل المثبت واتباع التعليمات
3. تذكر كلمة مرور المستخدم `root`

#### على Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

#### على macOS:
```bash
brew install mysql
brew services start mysql
```

### الخطوة 2: إعداد كلمة المرور

1. **إنشاء ملف `.env`** في المجلد الرئيسي:
```env
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_NAME=crestal_diamond_workshop
DB_USER=root
DB_PASSWORD=your_mysql_password_here

# إعدادات إضافية
DEBUG=True
SECRET_KEY=your_secret_key_here
```

2. **تحديث كلمة المرور** في الملف أعلاه

### الخطوة 3: تشغيل سكريبت الإعداد

```bash
# من المجلد الرئيسي للمشروع
python database/config.py
```

هذا السكريبت سيقوم بـ:
- إنشاء قاعدة البيانات
- إنشاء جميع الجداول المطلوبة
- إدراج البيانات الأولية

### الخطوة 4: اختبار الاتصال

```bash
python database/test_connection.py
```

## 🔧 الإعداد اليدوي - Manual Setup

إذا فشل الإعداد التلقائي، يمكنك الإعداد يدوياً:

### 1. إنشاء قاعدة البيانات
```sql
-- في MySQL Workbench أو سطر الأوامر
CREATE DATABASE crestal_diamond_workshop 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE crestal_diamond_workshop;
```

### 2. تشغيل سكريبت الجداول
```sql
-- في MySQL Workbench
SOURCE database/setup.sql;
```

أو من سطر الأوامر:
```bash
mysql -u root -p crestal_diamond_workshop < database/setup.sql
```

## 📊 بنية قاعدة البيانات - Database Structure

### الجداول الرئيسية:

#### 1. جدول العملاء - customers
- `id` - المعرف الفريد
- `name` - اسم العميل
- `phone` - رقم الهاتف
- `email` - البريد الإلكتروني
- `address` - العنوان
- `notes` - ملاحظات

#### 2. جدول الفواتير - invoices
- `id` - المعرف الفريد
- `invoice_number` - رقم الفاتورة
- `customer_id` - معرف العميل
- `invoice_date` - تاريخ الفاتورة
- `gold_weight` - وزن الذهب
- `gold_total` - إجمالي الذهب
- `stone_total` - إجمالي الأحجار
- `total_amount` - المبلغ الإجمالي
- `currency` - العملة (USD/EGP)
- `status` - حالة الفاتورة

#### 3. جدول تفاصيل الفواتير - invoice_details
- `id` - المعرف الفريد
- `invoice_id` - معرف الفاتورة
- `item_type` - نوع العنصر (gold/stone/service)
- `description` - الوصف
- `quantity` - الكمية
- `unit_price` - سعر الوحدة
- `total_price` - السعر الإجمالي

#### 4. جدول الخدمات - services
- `id` - المعرف الفريد
- `name` - اسم الخدمة
- `description` - الوصف
- `category` - الفئة
- `default_price` - السعر الافتراضي

## 🔄 ترحيل البيانات - Data Migration

### ترحيل من CSV إلى MySQL:
```bash
python database/migrate.py
```

هذا السكريبت سيقوم بـ:
- إنشاء نسخة احتياطية من ملفات CSV
- ترحيل البيانات إلى قاعدة البيانات
- التحقق من نجاح الترحيل

## 💾 النسخ الاحتياطية - Backups

### إنشاء نسخة احتياطية:
```bash
python database/backup.py
```

أنواع النسخ الاحتياطية:
- **SQL**: نسخة كاملة باستخدام mysqldump
- **CSV**: تصدير الجداول إلى ملفات CSV
- **JSON**: تصدير البيانات بتنسيق JSON

## 🛠️ استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة وحلولها:

#### 1. خطأ الاتصال بقاعدة البيانات
```
Error: Can't connect to MySQL server
```
**الحل:**
- تأكد من تشغيل خادم MySQL
- تحقق من كلمة المرور في ملف `.env`
- تأكد من المنفذ الصحيح (3306)

#### 2. خطأ في كلمة المرور
```
Error: Access denied for user 'root'
```
**الحل:**
- تحديث كلمة المرور في ملف `.env`
- إعادة تعيين كلمة مرور MySQL إذا لزم الأمر

#### 3. خطأ في إنشاء الجداول
```
Error: Table already exists
```
**الحل:**
- حذف قاعدة البيانات وإعادة إنشائها:
```sql
DROP DATABASE IF EXISTS crestal_diamond_workshop;
```

#### 4. مشاكل الترميز
```
Error: Incorrect string value
```
**الحل:**
- التأكد من استخدام UTF-8 في جميع الملفات
- تحديث إعدادات MySQL للدعم الكامل لـ UTF-8

## 📞 الدعم الفني - Technical Support

### ملفات السجلات:
- `database/logs/database.log` - سجل عمليات قاعدة البيانات
- `database/logs/error.log` - سجل الأخطاء

### أوامر مفيدة:

#### فحص حالة MySQL:
```bash
# Windows
net start mysql

# Linux/macOS
sudo systemctl status mysql
```

#### الاتصال بقاعدة البيانات:
```bash
mysql -u root -p crestal_diamond_workshop
```

#### عرض الجداول:
```sql
SHOW TABLES;
DESCRIBE invoices;
```

## 🔐 الأمان - Security

### نصائح أمنية:
1. **لا تشارك ملف `.env`** مع أي شخص
2. **استخدم كلمات مرور قوية** لقاعدة البيانات
3. **قم بعمل نسخ احتياطية منتظمة**
4. **حدث MySQL بانتظام** للحصول على تحديثات الأمان

### إنشاء مستخدم مخصص (اختياري):
```sql
CREATE USER 'crestal_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON crestal_diamond_workshop.* TO 'crestal_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📈 الصيانة - Maintenance

### مهام صيانة دورية:
1. **نسخ احتياطية أسبوعية**
2. **تنظيف السجلات القديمة**
3. **تحسين الجداول**
4. **مراقبة الأداء**

### أوامر الصيانة:
```sql
-- تحسين الجداول
OPTIMIZE TABLE invoices, customers, invoice_details;

-- فحص سلامة الجداول
CHECK TABLE invoices, customers;

-- إصلاح الجداول إذا لزم الأمر
REPAIR TABLE table_name;
```

---

## 📝 ملاحظات إضافية

- هذا النظام يدعم العملتين USD و EGP
- يمكن العمل بنظام CSV كبديل إذا لم تكن قاعدة البيانات متاحة
- جميع التواريخ والأوقات محفوظة بالتوقيت المحلي
- النظام يدعم النسخ الاحتياطية التلقائية

للمساعدة الإضافية، يرجى مراجعة ملفات التوثيق الأخرى في مجلد `database/`.
