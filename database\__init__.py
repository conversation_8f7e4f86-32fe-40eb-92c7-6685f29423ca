"""
مجلد قاعدة البيانات - Database Package
الموقع: database/__init__.py

هذا المجلد يحتوي على جميع ملفات قاعدة البيانات المنظمة:
- config.py: إعدادات الاتصال
- manager.py: مدير قاعدة البيانات الموحد
- setup.sql: سكريبت إنشاء الجداول
- test_connection.py: اختبارات الاتصال
"""

from .config import db_config, get_connection, get_pooled_connection
from .manager import DatabaseManager, db_manager

__version__ = "1.0.0"
__author__ = "Crestal Diamond Workshop"

# تصدير الفئات والدوال الرئيسية
__all__ = [
    'db_config',
    'get_connection', 
    'get_pooled_connection',
    'DatabaseManager',
    'db_manager'
]
