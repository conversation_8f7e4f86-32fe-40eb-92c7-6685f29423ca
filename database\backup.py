"""
نظام النسخ الاحتياطية لقاعدة البيانات - Database Backup System
الموقع: database/backup.py
"""

import sys
import os
import subprocess
from datetime import datetime
import pandas as pd
import logging
import json

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.manager import DatabaseManager
from database.config import db_config

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseBackup:
    """فئة النسخ الاحتياطية لقاعدة البيانات"""
    
    def __init__(self):
        """تهيئة نظام النسخ الاحتياطي"""
        self.backup_dir = "database/backups"
        self.ensure_backup_directory()
        
    def ensure_backup_directory(self):
        """التأكد من وجود مجلد النسخ الاحتياطية"""
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(f"{self.backup_dir}/sql", exist_ok=True)
        os.makedirs(f"{self.backup_dir}/csv", exist_ok=True)
        os.makedirs(f"{self.backup_dir}/json", exist_ok=True)
    
    def create_sql_backup(self) -> str:
        """إنشاء نسخة احتياطية SQL باستخدام mysqldump"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"{self.backup_dir}/sql/backup_{timestamp}.sql"
        
        try:
            # بناء أمر mysqldump
            cmd = [
                'mysqldump',
                f'--host={db_config.host}',
                f'--port={db_config.port}',
                f'--user={db_config.username}',
                f'--password={db_config.password}',
                '--single-transaction',
                '--routines',
                '--triggers',
                '--default-character-set=utf8mb4',
                db_config.database
            ]
            
            # تشغيل الأمر وحفظ النتيجة
            with open(backup_filename, 'w', encoding='utf-8') as backup_file:
                result = subprocess.run(cmd, stdout=backup_file, stderr=subprocess.PIPE, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ تم إنشاء النسخة الاحتياطية SQL: {backup_filename}")
                return backup_filename
            else:
                logger.error(f"❌ فشل إنشاء النسخة الاحتياطية SQL: {result.stderr}")
                return None
                
        except FileNotFoundError:
            logger.error("❌ mysqldump غير موجود. تأكد من تثبيت MySQL Client")
            return None
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية SQL: {e}")
            return None
    
    def create_csv_backup(self) -> str:
        """إنشاء نسخة احتياطية CSV"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_folder = f"{self.backup_dir}/csv/backup_{timestamp}"
        os.makedirs(backup_folder, exist_ok=True)
        
        try:
            with DatabaseManager() as db:
                tables = ['customers', 'invoices', 'invoice_details', 'services']
                
                for table in tables:
                    try:
                        # تصدير الجدول إلى DataFrame
                        df = db.export_to_dataframe(table)
                        if df is not None and not df.empty:
                            csv_file = f"{backup_folder}/{table}.csv"
                            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                            logger.info(f"✅ تم تصدير {table} إلى {csv_file}")
                        else:
                            logger.warning(f"⚠️ الجدول {table} فارغ أو غير موجود")
                    except Exception as e:
                        logger.error(f"❌ فشل تصدير {table}: {e}")
                
                logger.info(f"✅ تم إنشاء النسخة الاحتياطية CSV: {backup_folder}")
                return backup_folder
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية CSV: {e}")
            return None
    
    def create_json_backup(self) -> str:
        """إنشاء نسخة احتياطية JSON"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"{self.backup_dir}/json/backup_{timestamp}.json"
        
        try:
            backup_data = {
                'metadata': {
                    'timestamp': timestamp,
                    'database': db_config.database,
                    'version': '1.0.0'
                },
                'data': {}
            }
            
            with DatabaseManager() as db:
                tables = ['customers', 'invoices', 'invoice_details', 'services']
                
                for table in tables:
                    try:
                        df = db.export_to_dataframe(table)
                        if df is not None and not df.empty:
                            # تحويل DataFrame إلى قاموس
                            backup_data['data'][table] = df.to_dict('records')
                            logger.info(f"✅ تم تصدير {table} إلى JSON")
                        else:
                            backup_data['data'][table] = []
                            logger.warning(f"⚠️ الجدول {table} فارغ")
                    except Exception as e:
                        logger.error(f"❌ فشل تصدير {table}: {e}")
                        backup_data['data'][table] = []
                
                # حفظ البيانات في ملف JSON
                with open(backup_filename, 'w', encoding='utf-8') as json_file:
                    json.dump(backup_data, json_file, ensure_ascii=False, indent=2, default=str)
                
                logger.info(f"✅ تم إنشاء النسخة الاحتياطية JSON: {backup_filename}")
                return backup_filename
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية JSON: {e}")
            return None
    
    def create_full_backup(self) -> dict:
        """إنشاء نسخة احتياطية شاملة بجميع التنسيقات"""
        logger.info("🚀 بدء إنشاء النسخة الاحتياطية الشاملة...")
        
        results = {
            'sql': None,
            'csv': None,
            'json': None,
            'success': False
        }
        
        # نسخة SQL
        logger.info("📄 إنشاء نسخة SQL...")
        results['sql'] = self.create_sql_backup()
        
        # نسخة CSV
        logger.info("📊 إنشاء نسخة CSV...")
        results['csv'] = self.create_csv_backup()
        
        # نسخة JSON
        logger.info("🔧 إنشاء نسخة JSON...")
        results['json'] = self.create_json_backup()
        
        # تحديد النجاح
        results['success'] = any([results['sql'], results['csv'], results['json']])
        
        if results['success']:
            logger.info("✅ تم إنشاء النسخة الاحتياطية الشاملة بنجاح")
        else:
            logger.error("❌ فشل إنشاء النسخة الاحتياطية الشاملة")
        
        return results
    
    def list_backups(self) -> dict:
        """عرض قائمة النسخ الاحتياطية المتاحة"""
        backups = {
            'sql': [],
            'csv': [],
            'json': []
        }
        
        try:
            # نسخ SQL
            sql_dir = f"{self.backup_dir}/sql"
            if os.path.exists(sql_dir):
                for file in os.listdir(sql_dir):
                    if file.endswith('.sql'):
                        file_path = os.path.join(sql_dir, file)
                        stat = os.stat(file_path)
                        backups['sql'].append({
                            'filename': file,
                            'path': file_path,
                            'size': stat.st_size,
                            'created': datetime.fromtimestamp(stat.st_ctime)
                        })
            
            # نسخ CSV
            csv_dir = f"{self.backup_dir}/csv"
            if os.path.exists(csv_dir):
                for folder in os.listdir(csv_dir):
                    folder_path = os.path.join(csv_dir, folder)
                    if os.path.isdir(folder_path):
                        stat = os.stat(folder_path)
                        backups['csv'].append({
                            'foldername': folder,
                            'path': folder_path,
                            'created': datetime.fromtimestamp(stat.st_ctime)
                        })
            
            # نسخ JSON
            json_dir = f"{self.backup_dir}/json"
            if os.path.exists(json_dir):
                for file in os.listdir(json_dir):
                    if file.endswith('.json'):
                        file_path = os.path.join(json_dir, file)
                        stat = os.stat(file_path)
                        backups['json'].append({
                            'filename': file,
                            'path': file_path,
                            'size': stat.st_size,
                            'created': datetime.fromtimestamp(stat.st_ctime)
                        })
            
            return backups
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض النسخ الاحتياطية: {e}")
            return backups
    
    def cleanup_old_backups(self, keep_days: int = 30):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            cutoff_date = datetime.now().timestamp() - (keep_days * 24 * 60 * 60)
            deleted_count = 0
            
            for backup_type in ['sql', 'csv', 'json']:
                backup_path = f"{self.backup_dir}/{backup_type}"
                if os.path.exists(backup_path):
                    for item in os.listdir(backup_path):
                        item_path = os.path.join(backup_path, item)
                        if os.path.getctime(item_path) < cutoff_date:
                            if os.path.isfile(item_path):
                                os.remove(item_path)
                            elif os.path.isdir(item_path):
                                import shutil
                                shutil.rmtree(item_path)
                            deleted_count += 1
                            logger.info(f"🗑️ تم حذف النسخة القديمة: {item}")
            
            logger.info(f"✅ تم تنظيف {deleted_count} نسخة احتياطية قديمة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف النسخ الاحتياطية: {e}")

def run_backup():
    """تشغيل النسخ الاحتياطي"""
    print("💾 بدء عملية النسخ الاحتياطي...")
    print("=" * 50)
    
    backup_system = DatabaseBackup()
    
    # إنشاء نسخة احتياطية شاملة
    results = backup_system.create_full_backup()
    
    # عرض النتائج
    print("\n📊 نتائج النسخ الاحتياطي:")
    print(f"SQL: {'✅' if results['sql'] else '❌'}")
    print(f"CSV: {'✅' if results['csv'] else '❌'}")
    print(f"JSON: {'✅' if results['json'] else '❌'}")
    
    if results['success']:
        print("\n🎉 تم إنشاء النسخة الاحتياطية بنجاح!")
        
        # تنظيف النسخ القديمة
        print("\n🧹 تنظيف النسخ القديمة...")
        backup_system.cleanup_old_backups()
        
        return True
    else:
        print("\n❌ فشل إنشاء النسخة الاحتياطية!")
        return False

if __name__ == "__main__":
    # التحقق من الاتصال بقاعدة البيانات
    if not db_config.test_connection():
        print("❌ لا يمكن الاتصال بقاعدة البيانات")
        sys.exit(1)
    
    # تشغيل النسخ الاحتياطي
    success = run_backup()
    sys.exit(0 if success else 1)
