"""
إعدادات قاعدة البيانات - Database Configuration
الموقع: database/config.py
"""

import os
import mysql.connector
from mysql.connector import Error
import logging
from typing import Optional, Dict, Any

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database/logs/database.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """فئة إعدادات قاعدة البيانات"""
    
    def __init__(self):
        """تهيئة إعدادات قاعدة البيانات"""
        # قراءة المتغيرات من ملف البيئة أو القيم الافتراضية
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = int(os.getenv('DB_PORT', 3306))
        self.database = os.getenv('DB_NAME', 'crestal_diamond_workshop')
        self.username = os.getenv('DB_USER', 'root')
        self.password = os.getenv('DB_PASSWORD', '2452329511')
        
        # إعدادات الاتصال
        self.charset = 'utf8mb4'
        self.collation = 'utf8mb4_unicode_ci'
        self.autocommit = True
        self.pool_name = 'crestal_pool'
        self.pool_size = 5
        self.pool_reset_session = True
        
    def get_connection_config(self) -> Dict[str, Any]:
        """إرجاع إعدادات الاتصال كقاموس"""
        return {
            'host': self.host,
            'port': self.port,
            'database': self.database,
            'user': self.username,
            'password': self.password,
            'charset': self.charset,
            'collation': self.collation,
            'autocommit': self.autocommit,
            'raise_on_warnings': True,
            'use_unicode': True,
            'sql_mode': 'TRADITIONAL'
        }
    
    def get_pool_config(self) -> Dict[str, Any]:
        """إرجاع إعدادات مجموعة الاتصالات"""
        config = self.get_connection_config()
        config.update({
            'pool_name': self.pool_name,
            'pool_size': self.pool_size,
            'pool_reset_session': self.pool_reset_session
        })
        return config
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            connection = mysql.connector.connect(**self.get_connection_config())
            if connection.is_connected():
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                logger.info(f"✅ نجح الاتصال بقاعدة البيانات MySQL {version[0]}")
                cursor.close()
                connection.close()
                return True
        except Error as e:
            logger.error(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
            return False
    
    def create_database_if_not_exists(self) -> bool:
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # الاتصال بدون تحديد قاعدة بيانات
            config = self.get_connection_config()
            config.pop('database')
            
            connection = mysql.connector.connect(**config)
            cursor = connection.cursor()
            
            # إنشاء قاعدة البيانات
            cursor.execute(f"""
                CREATE DATABASE IF NOT EXISTS {self.database} 
                CHARACTER SET {self.charset} 
                COLLATE {self.collation}
            """)
            
            logger.info(f"✅ تم إنشاء قاعدة البيانات {self.database}")
            cursor.close()
            connection.close()
            return True
            
        except Error as e:
            logger.error(f"❌ فشل إنشاء قاعدة البيانات: {e}")
            return False
    
    def setup_database(self) -> bool:
        """إعداد قاعدة البيانات الكامل"""
        try:
            # إنشاء قاعدة البيانات
            if not self.create_database_if_not_exists():
                return False
            
            # اختبار الاتصال
            if not self.test_connection():
                return False
            
            # تشغيل سكريبت الإعداد
            return self.run_setup_script()
            
        except Exception as e:
            logger.error(f"❌ فشل إعداد قاعدة البيانات: {e}")
            return False
    
    def run_setup_script(self) -> bool:
        """تشغيل سكريبت إعداد الجداول"""
        try:
            connection = mysql.connector.connect(**self.get_connection_config())
            cursor = connection.cursor()
            
            # قراءة سكريبت الإعداد
            with open('database/setup.sql', 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            # تنفيذ السكريبت
            for statement in sql_script.split(';'):
                if statement.strip():
                    cursor.execute(statement)
            
            connection.commit()
            logger.info("✅ تم تشغيل سكريبت الإعداد بنجاح")
            
            cursor.close()
            connection.close()
            return True
            
        except Error as e:
            logger.error(f"❌ فشل تشغيل سكريبت الإعداد: {e}")
            return False
        except FileNotFoundError:
            logger.error("❌ لم يتم العثور على ملف setup.sql")
            return False

# إنشاء مثيل عام من إعدادات قاعدة البيانات
db_config = DatabaseConfig()

def get_connection() -> Optional[mysql.connector.MySQLConnection]:
    """الحصول على اتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(**db_config.get_connection_config())
    except Error as e:
        logger.error(f"❌ فشل الحصول على اتصال: {e}")
        return None

def get_pooled_connection() -> Optional[mysql.connector.MySQLConnection]:
    """الحصول على اتصال من مجموعة الاتصالات"""
    try:
        return mysql.connector.connect(**db_config.get_pool_config())
    except Error as e:
        logger.error(f"❌ فشل الحصول على اتصال مجمع: {e}")
        return None

if __name__ == "__main__":
    """تشغيل إعداد قاعدة البيانات"""
    print("🔧 بدء إعداد قاعدة البيانات...")
    
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    os.makedirs('database/logs', exist_ok=True)
    
    if db_config.setup_database():
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
    else:
        print("❌ فشل إعداد قاعدة البيانات!")
