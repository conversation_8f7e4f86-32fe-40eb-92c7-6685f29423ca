"""
مدير قاعدة البيانات الموحد - Unified Database Manager
الموقع: database/manager.py
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, date
import json

from .config import db_config, get_connection

logger = logging.getLogger(__name__)

class DatabaseManager:
    """مدير قاعدة البيانات الموحد لجميع العمليات"""
    
    def __init__(self):
        """تهيئة مدير قاعدة البيانات"""
        self.connection = None
        self.cursor = None
    
    def connect(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = get_connection()
            if self.connection and self.connection.is_connected():
                self.cursor = self.connection.cursor(dictionary=True)
                return True
            return False
        except Error as e:
            logger.error(f"❌ فشل الاتصال: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection and self.connection.is_connected():
                self.connection.close()
        except Error as e:
            logger.error(f"❌ خطأ في قطع الاتصال: {e}")
    
    def __enter__(self):
        """دخول السياق"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """خروج السياق"""
        self.disconnect()
    
    # ==================== عمليات العملاء ====================
    
    def create_customer(self, customer_data: Dict[str, Any]) -> Optional[int]:
        """إنشاء عميل جديد"""
        try:
            query = """
                INSERT INTO customers (name, phone, email, address, notes)
                VALUES (%(name)s, %(phone)s, %(email)s, %(address)s, %(notes)s)
            """
            self.cursor.execute(query, customer_data)
            self.connection.commit()
            customer_id = self.cursor.lastrowid
            logger.info(f"✅ تم إنشاء عميل جديد برقم {customer_id}")
            return customer_id
        except Error as e:
            logger.error(f"❌ فشل إنشاء العميل: {e}")
            return None
    
    def get_customer(self, customer_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات عميل"""
        try:
            query = "SELECT * FROM customers WHERE id = %s"
            self.cursor.execute(query, (customer_id,))
            return self.cursor.fetchone()
        except Error as e:
            logger.error(f"❌ فشل الحصول على العميل: {e}")
            return None
    
    def search_customers(self, search_term: str) -> List[Dict[str, Any]]:
        """البحث عن العملاء"""
        try:
            query = """
                SELECT * FROM customers 
                WHERE name LIKE %s OR phone LIKE %s OR email LIKE %s
                ORDER BY name
            """
            search_pattern = f"%{search_term}%"
            self.cursor.execute(query, (search_pattern, search_pattern, search_pattern))
            return self.cursor.fetchall()
        except Error as e:
            logger.error(f"❌ فشل البحث عن العملاء: {e}")
            return []
    
    def get_all_customers(self) -> List[Dict[str, Any]]:
        """الحصول على جميع العملاء"""
        try:
            query = "SELECT * FROM customers ORDER BY name"
            self.cursor.execute(query)
            return self.cursor.fetchall()
        except Error as e:
            logger.error(f"❌ فشل الحصول على العملاء: {e}")
            return []
    
    # ==================== عمليات الفواتير ====================
    
    def create_invoice(self, invoice_data: Dict[str, Any]) -> Optional[int]:
        """إنشاء فاتورة جديدة"""
        try:
            # إنشاء رقم فاتورة تلقائي إذا لم يتم توفيره
            if 'invoice_number' not in invoice_data:
                invoice_data['invoice_number'] = self.generate_invoice_number()
            
            query = """
                INSERT INTO invoices (
                    invoice_number, customer_id, invoice_date, due_date,
                    gold_weight, gold_karat, gold_price_per_gram, gold_total,
                    stone_weight, stone_price_per_carat, stone_total,
                    additional_services, services_total,
                    subtotal, tax_rate, tax_amount, discount_amount, total_amount,
                    currency, exchange_rate, payment_status, paid_amount,
                    notes, status
                ) VALUES (
                    %(invoice_number)s, %(customer_id)s, %(invoice_date)s, %(due_date)s,
                    %(gold_weight)s, %(gold_karat)s, %(gold_price_per_gram)s, %(gold_total)s,
                    %(stone_weight)s, %(stone_price_per_carat)s, %(stone_total)s,
                    %(additional_services)s, %(services_total)s,
                    %(subtotal)s, %(tax_rate)s, %(tax_amount)s, %(discount_amount)s, %(total_amount)s,
                    %(currency)s, %(exchange_rate)s, %(payment_status)s, %(paid_amount)s,
                    %(notes)s, %(status)s
                )
            """
            self.cursor.execute(query, invoice_data)
            self.connection.commit()
            invoice_id = self.cursor.lastrowid
            logger.info(f"✅ تم إنشاء فاتورة جديدة برقم {invoice_id}")
            return invoice_id
        except Error as e:
            logger.error(f"❌ فشل إنشاء الفاتورة: {e}")
            return None
    
    def get_invoice(self, invoice_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على فاتورة مع بيانات العميل"""
        try:
            query = """
                SELECT i.*, c.name as customer_name, c.phone as customer_phone,
                       c.email as customer_email, c.address as customer_address
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                WHERE i.id = %s
            """
            self.cursor.execute(query, (invoice_id,))
            return self.cursor.fetchone()
        except Error as e:
            logger.error(f"❌ فشل الحصول على الفاتورة: {e}")
            return None
    
    def get_invoices_by_customer(self, customer_id: int) -> List[Dict[str, Any]]:
        """الحصول على فواتير عميل معين"""
        try:
            query = """
                SELECT * FROM invoices 
                WHERE customer_id = %s 
                ORDER BY invoice_date DESC
            """
            self.cursor.execute(query, (customer_id,))
            return self.cursor.fetchall()
        except Error as e:
            logger.error(f"❌ فشل الحصول على فواتير العميل: {e}")
            return []
    
    def search_invoices(self, search_term: str) -> List[Dict[str, Any]]:
        """البحث عن الفواتير"""
        try:
            query = """
                SELECT i.*, c.name as customer_name
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                WHERE i.invoice_number LIKE %s OR c.name LIKE %s
                ORDER BY i.invoice_date DESC
            """
            search_pattern = f"%{search_term}%"
            self.cursor.execute(query, (search_pattern, search_pattern))
            return self.cursor.fetchall()
        except Error as e:
            logger.error(f"❌ فشل البحث عن الفواتير: {e}")
            return []
    
    def update_invoice_status(self, invoice_id: int, status: str) -> bool:
        """تحديث حالة الفاتورة"""
        try:
            query = "UPDATE invoices SET status = %s WHERE id = %s"
            self.cursor.execute(query, (status, invoice_id))
            self.connection.commit()
            logger.info(f"✅ تم تحديث حالة الفاتورة {invoice_id} إلى {status}")
            return True
        except Error as e:
            logger.error(f"❌ فشل تحديث حالة الفاتورة: {e}")
            return False
    
    def generate_invoice_number(self) -> str:
        """توليد رقم فاتورة تلقائي"""
        try:
            # الحصول على آخر رقم فاتورة
            query = """
                SELECT invoice_number FROM invoices 
                WHERE invoice_number REGEXP '^INV-[0-9]{6}$'
                ORDER BY id DESC LIMIT 1
            """
            self.cursor.execute(query)
            result = self.cursor.fetchone()
            
            if result:
                last_number = int(result['invoice_number'].split('-')[1])
                new_number = last_number + 1
            else:
                new_number = 1
            
            return f"INV-{new_number:06d}"
        except Error as e:
            logger.error(f"❌ فشل توليد رقم الفاتورة: {e}")
            # إرجاع رقم افتراضي بناءً على الوقت
            return f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # ==================== عمليات الخدمات ====================
    
    def get_all_services(self) -> List[Dict[str, Any]]:
        """الحصول على جميع الخدمات المتاحة"""
        try:
            query = "SELECT * FROM services WHERE is_active = TRUE ORDER BY category, name"
            self.cursor.execute(query)
            return self.cursor.fetchall()
        except Error as e:
            logger.error(f"❌ فشل الحصول على الخدمات: {e}")
            return []
    
    # ==================== التقارير والإحصائيات ====================
    
    def get_dashboard_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات لوحة التحكم"""
        try:
            stats = {}
            
            # عدد العملاء
            self.cursor.execute("SELECT COUNT(*) as count FROM customers")
            stats['total_customers'] = self.cursor.fetchone()['count']
            
            # عدد الفواتير
            self.cursor.execute("SELECT COUNT(*) as count FROM invoices")
            stats['total_invoices'] = self.cursor.fetchone()['count']
            
            # إجمالي المبيعات
            self.cursor.execute("SELECT SUM(total_amount) as total FROM invoices WHERE status != 'cancelled'")
            result = self.cursor.fetchone()
            stats['total_sales'] = result['total'] or 0
            
            # الفواتير المعلقة
            self.cursor.execute("SELECT COUNT(*) as count FROM invoices WHERE payment_status = 'pending'")
            stats['pending_invoices'] = self.cursor.fetchone()['count']
            
            return stats
        except Error as e:
            logger.error(f"❌ فشل الحصول على الإحصائيات: {e}")
            return {}
    
    def export_to_dataframe(self, table_name: str) -> Optional[pd.DataFrame]:
        """تصدير جدول إلى DataFrame"""
        try:
            query = f"SELECT * FROM {table_name}"
            return pd.read_sql(query, self.connection)
        except Error as e:
            logger.error(f"❌ فشل تصدير الجدول {table_name}: {e}")
            return None

# إنشاء مثيل عام من مدير قاعدة البيانات
db_manager = DatabaseManager()
