"""
ترحيل البيانات من CSV إلى MySQL - Data Migration from CSV to MySQL
الموقع: database/migrate.py
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.manager import DatabaseManager
from database.config import db_config

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataMigrator:
    """فئة ترحيل البيانات من CSV إلى MySQL"""
    
    def __init__(self):
        """تهيئة مرحل البيانات"""
        self.db_manager = DatabaseManager()
        self.csv_files = {
            'invoices': 'invoices.csv',
            'customers': 'customers.csv'  # إذا كان موجوداً
        }
    
    def migrate_invoices_from_csv(self, csv_file_path: str = 'invoices.csv') -> bool:
        """ترحيل الفواتير من ملف CSV"""
        try:
            if not os.path.exists(csv_file_path):
                logger.warning(f"ملف CSV غير موجود: {csv_file_path}")
                return True  # ليس خطأ إذا لم يكن الملف موجوداً
            
            # قراءة ملف CSV
            df = pd.read_csv(csv_file_path)
            logger.info(f"تم قراءة {len(df)} سجل من {csv_file_path}")
            
            if df.empty:
                logger.info("ملف CSV فارغ")
                return True
            
            migrated_count = 0
            errors_count = 0
            
            with self.db_manager as db:
                for index, row in df.iterrows():
                    try:
                        # تحويل البيانات من تنسيق CSV القديم إلى تنسيق قاعدة البيانات
                        invoice_data = self._convert_csv_row_to_invoice_data(row)
                        
                        # البحث عن العميل أو إنشاؤه
                        customer_id = self._get_or_create_customer(db, invoice_data['customer_name'])
                        invoice_data['customer_id'] = customer_id
                        
                        # إزالة اسم العميل من بيانات الفاتورة
                        del invoice_data['customer_name']
                        
                        # إنشاء الفاتورة
                        invoice_id = db.create_invoice(invoice_data)
                        
                        if invoice_id:
                            migrated_count += 1
                            logger.info(f"تم ترحيل الفاتورة {invoice_id}")
                        else:
                            errors_count += 1
                            logger.error(f"فشل ترحيل السجل {index + 1}")
                            
                    except Exception as e:
                        errors_count += 1
                        logger.error(f"خطأ في ترحيل السجل {index + 1}: {e}")
            
            logger.info(f"انتهى الترحيل: {migrated_count} نجح، {errors_count} فشل")
            return errors_count == 0
            
        except Exception as e:
            logger.error(f"خطأ في ترحيل الفواتير: {e}")
            return False
    
    def _convert_csv_row_to_invoice_data(self, row) -> dict:
        """تحويل سجل CSV إلى بيانات فاتورة"""
        # تحويل التاريخ
        invoice_date = row.get('date', datetime.now().date())
        if isinstance(invoice_date, str):
            try:
                invoice_date = datetime.strptime(invoice_date, '%Y-%m-%d').date()
            except:
                invoice_date = datetime.now().date()
        
        # حساب المجاميع
        gold_total = float(row.get('gold_change', 0)) if pd.notna(row.get('gold_change')) else 0
        usd_total = float(row.get('usd_change', 0)) if pd.notna(row.get('usd_change')) else 0
        egp_total = float(row.get('egp_change', 0)) if pd.notna(row.get('egp_change')) else 0
        
        # تحديد العملة الرئيسية
        currency = 'USD' if usd_total != 0 else 'EGP'
        total_amount = usd_total if currency == 'USD' else egp_total
        
        return {
            'customer_name': row.get('customer_name', 'عميل غير محدد'),
            'invoice_date': invoice_date,
            'invoice_number': f"MIG-{datetime.now().strftime('%Y%m%d')}-{row.name + 1:04d}",
            'description': row.get('description', ''),
            'gold_weight': abs(gold_total) if gold_total != 0 else 0,
            'gold_karat': 21,  # افتراضي
            'gold_price_per_gram': 0,
            'gold_total': gold_total,
            'stone_weight': 0,
            'stone_price_per_carat': 0,
            'stone_total': 0,
            'additional_services': '',
            'services_total': 0,
            'subtotal': total_amount,
            'tax_rate': 0,
            'tax_amount': 0,
            'discount_amount': 0,
            'total_amount': total_amount,
            'currency': currency,
            'exchange_rate': 1.0,
            'payment_status': 'paid' if total_amount > 0 else 'pending',
            'paid_amount': total_amount if total_amount > 0 else 0,
            'notes': f"مرحل من CSV - {row.get('description', '')}",
            'status': 'paid' if total_amount > 0 else 'draft'
        }
    
    def _get_or_create_customer(self, db, customer_name: str) -> int:
        """البحث عن العميل أو إنشاؤه"""
        # البحث عن العميل الموجود
        customers = db.search_customers(customer_name)
        
        if customers:
            return customers[0]['id']
        
        # إنشاء عميل جديد
        customer_data = {
            'name': customer_name,
            'phone': '',
            'email': '',
            'address': '',
            'notes': 'مرحل من CSV'
        }
        
        return db.create_customer(customer_data)
    
    def backup_csv_files(self) -> bool:
        """إنشاء نسخة احتياطية من ملفات CSV"""
        try:
            backup_dir = f"csv_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(backup_dir, exist_ok=True)
            
            for file_type, file_path in self.csv_files.items():
                if os.path.exists(file_path):
                    backup_path = os.path.join(backup_dir, file_path)
                    import shutil
                    shutil.copy2(file_path, backup_path)
                    logger.info(f"تم نسخ {file_path} إلى {backup_path}")
            
            logger.info(f"تم إنشاء النسخة الاحتياطية في {backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"فشل إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def verify_migration(self) -> bool:
        """التحقق من نجاح الترحيل"""
        try:
            with self.db_manager as db:
                stats = db.get_dashboard_stats()
                logger.info(f"إحصائيات ما بعد الترحيل: {stats}")
                
                # عرض بعض الفواتير المرحلة
                db.cursor.execute("SELECT * FROM invoices WHERE notes LIKE '%مرحل من CSV%' LIMIT 5")
                migrated_invoices = db.cursor.fetchall()
                
                logger.info(f"عدد الفواتير المرحلة: {len(migrated_invoices)}")
                for invoice in migrated_invoices:
                    logger.info(f"  - فاتورة {invoice['invoice_number']}: {invoice['total_amount']} {invoice['currency']}")
                
                return True
                
        except Exception as e:
            logger.error(f"فشل التحقق من الترحيل: {e}")
            return False

def run_migration():
    """تشغيل عملية الترحيل الكاملة"""
    print("🚀 بدء عملية ترحيل البيانات من CSV إلى MySQL")
    print("=" * 60)
    
    migrator = DataMigrator()
    
    # 1. إنشاء نسخة احتياطية
    print("📦 إنشاء نسخة احتياطية من ملفات CSV...")
    if not migrator.backup_csv_files():
        print("❌ فشل إنشاء النسخة الاحتياطية")
        return False
    
    # 2. ترحيل الفواتير
    print("\n📄 ترحيل الفواتير...")
    if not migrator.migrate_invoices_from_csv():
        print("❌ فشل ترحيل الفواتير")
        return False
    
    # 3. التحقق من الترحيل
    print("\n✅ التحقق من نجاح الترحيل...")
    if not migrator.verify_migration():
        print("❌ فشل التحقق من الترحيل")
        return False
    
    print("\n🎉 تم الترحيل بنجاح!")
    print("💡 يمكنك الآن استخدام قاعدة البيانات بدلاً من ملفات CSV")
    print("📁 تم الاحتفاظ بنسخة احتياطية من ملفات CSV")
    
    return True

if __name__ == "__main__":
    # التحقق من وجود قاعدة البيانات
    if not db_config.test_connection():
        print("❌ لا يمكن الاتصال بقاعدة البيانات")
        print("💡 قم بتشغيل: python database/config.py")
        sys.exit(1)
    
    # تشغيل الترحيل
    success = run_migration()
    sys.exit(0 if success else 1)
