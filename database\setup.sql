-- إن<PERSON><PERSON><PERSON> قاعدة بيانات نظام إدارة فواتير الورشة
-- Crestal Diamond Workshop Invoice System Database
-- الموقع: database/setup.sql

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS crestal_diamond_workshop 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE crestal_diamond_workshop;

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT,
    invoice_date DATE NOT NULL,
    due_date DATE,
    
    -- تفاصيل الذهب
    gold_weight DECIMAL(10,3) DEFAULT 0,
    gold_karat INT DEFAULT 0,
    gold_price_per_gram DECIMAL(10,2) DEFAULT 0,
    gold_total DECIMAL(12,2) DEFAULT 0,
    
    -- تفاصيل الأحجار
    stone_weight DECIMAL(10,3) DEFAULT 0,
    stone_price_per_carat DECIMAL(10,2) DEFAULT 0,
    stone_total DECIMAL(12,2) DEFAULT 0,
    
    -- الخدمات الإضافية
    additional_services TEXT,
    services_total DECIMAL(12,2) DEFAULT 0,
    
    -- المجاميع
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    
    -- العملة والدفع
    currency ENUM('USD', 'EGP') DEFAULT 'USD',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
    paid_amount DECIMAL(12,2) DEFAULT 0,
    
    -- ملاحظات وحالة
    notes TEXT,
    status ENUM('draft', 'sent', 'paid', 'cancelled') DEFAULT 'draft',
    
    -- تواريخ النظام
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- المفاتيح الخارجية
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    
    -- فهارس للبحث السريع
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_customer_id (customer_id),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status)
);

-- جدول تفاصيل الفواتير (للعناصر المتعددة)
CREATE TABLE IF NOT EXISTS invoice_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    item_type ENUM('gold', 'stone', 'service') NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(10,3) DEFAULT 1,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(12,2) DEFAULT 0,
    
    -- تفاصيل خاصة بالذهب
    karat INT NULL,
    gold_type VARCHAR(100) NULL,
    
    -- تفاصيل خاصة بالأحجار
    stone_type VARCHAR(100) NULL,
    clarity VARCHAR(50) NULL,
    color VARCHAR(50) NULL,
    cut_quality VARCHAR(50) NULL,
    
    -- تفاصيل خاصة بالخدمات
    service_category VARCHAR(100) NULL,
    labor_hours DECIMAL(5,2) NULL,
    
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_item_type (item_type)
);

-- جدول الخدمات المتاحة
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    default_price DECIMAL(10,2) DEFAULT 0,
    unit VARCHAR(50) DEFAULT 'piece',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_is_active (is_active)
);

-- جدول سجل النشاطات
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    user_info VARCHAR(255) NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_timestamp (timestamp)
);

-- إدراج بيانات أولية للخدمات
INSERT INTO services (name, description, category, default_price, unit) VALUES
('تصميم مجوهرات', 'تصميم قطعة مجوهرات حسب الطلب', 'تصميم', 500.00, 'قطعة'),
('تلميع وتنظيف', 'تلميع وتنظيف المجوهرات', 'صيانة', 50.00, 'قطعة'),
('إصلاح سلسلة', 'إصلاح وتقوية السلاسل', 'إصلاح', 100.00, 'قطعة'),
('تركيب حجر', 'تركيب الأحجار الكريمة', 'تركيب', 200.00, 'حجر'),
('تغيير مقاس خاتم', 'تعديل مقاس الخواتم', 'تعديل', 75.00, 'قطعة'),
('نقش أسماء', 'نقش الأسماء والتواريخ', 'نقش', 150.00, 'قطعة');

-- إنشاء مستخدم قاعدة البيانات (اختياري)
-- CREATE USER IF NOT EXISTS 'crestal_user'@'localhost' IDENTIFIED BY 'secure_password_2024';
-- GRANT ALL PRIVILEGES ON crestal_diamond_workshop.* TO 'crestal_user'@'localhost';
-- FLUSH PRIVILEGES;

-- عرض الجداول المنشأة
SHOW TABLES;

-- عرض بنية جدول الفواتير
DESCRIBE invoices;
