"""
اختبار الاتصال بقاعدة البيانات - Database Connection Test
الموقع: database/test_connection.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.config import db_config
from database.manager import DatabaseManager
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_basic_connection():
    """اختبار الاتصال الأساسي"""
    print("🔍 اختبار الاتصال الأساسي...")
    
    if db_config.test_connection():
        print("✅ نجح الاتصال الأساسي بقاعدة البيانات")
        return True
    else:
        print("❌ فشل الاتصال الأساسي بقاعدة البيانات")
        return False

def test_database_manager():
    """اختبار مدير قاعدة البيانات"""
    print("\n🔍 اختبار مدير قاعدة البيانات...")
    
    try:
        with DatabaseManager() as db:
            if db.connection and db.connection.is_connected():
                print("✅ نجح الاتصال عبر مدير قاعدة البيانات")
                
                # اختبار استعلام بسيط
                db.cursor.execute("SELECT VERSION() as version")
                result = db.cursor.fetchone()
                print(f"📊 إصدار MySQL: {result['version']}")
                
                return True
            else:
                print("❌ فشل الاتصال عبر مدير قاعدة البيانات")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في مدير قاعدة البيانات: {e}")
        return False

def test_tables_exist():
    """اختبار وجود الجداول"""
    print("\n🔍 اختبار وجود الجداول...")
    
    expected_tables = ['customers', 'invoices', 'invoice_details', 'services', 'activity_log']
    
    try:
        with DatabaseManager() as db:
            db.cursor.execute("SHOW TABLES")
            existing_tables = [table[f'Tables_in_{db_config.database}'] for table in db.cursor.fetchall()]
            
            print(f"📋 الجداول الموجودة: {existing_tables}")
            
            missing_tables = []
            for table in expected_tables:
                if table in existing_tables:
                    print(f"✅ الجدول {table} موجود")
                else:
                    print(f"❌ الجدول {table} غير موجود")
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"\n⚠️ الجداول المفقودة: {missing_tables}")
                print("💡 قم بتشغيل سكريبت الإعداد: python database/config.py")
                return False
            else:
                print("\n✅ جميع الجداول المطلوبة موجودة")
                return True
                
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False

def test_sample_operations():
    """اختبار العمليات الأساسية"""
    print("\n🔍 اختبار العمليات الأساسية...")
    
    try:
        with DatabaseManager() as db:
            # اختبار الحصول على الخدمات
            services = db.get_all_services()
            print(f"📋 عدد الخدمات المتاحة: {len(services)}")
            
            # اختبار الحصول على العملاء
            customers = db.get_all_customers()
            print(f"👥 عدد العملاء: {len(customers)}")
            
            # اختبار إحصائيات لوحة التحكم
            stats = db.get_dashboard_stats()
            print(f"📊 إحصائيات لوحة التحكم: {stats}")
            
            print("✅ نجحت جميع العمليات الأساسية")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في العمليات الأساسية: {e}")
        return False

def test_data_integrity():
    """اختبار سلامة البيانات"""
    print("\n🔍 اختبار سلامة البيانات...")
    
    try:
        with DatabaseManager() as db:
            # فحص المفاتيح الخارجية
            db.cursor.execute("""
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                WHERE REFERENCED_TABLE_SCHEMA = %s
                AND REFERENCED_TABLE_NAME IS NOT NULL
            """, (db_config.database,))
            
            foreign_keys = db.cursor.fetchall()
            print(f"🔗 عدد المفاتيح الخارجية: {len(foreign_keys)}")
            
            for fk in foreign_keys:
                print(f"   {fk['TABLE_NAME']}.{fk['COLUMN_NAME']} -> {fk['REFERENCED_TABLE_NAME']}.{fk['REFERENCED_COLUMN_NAME']}")
            
            # فحص الفهارس
            db.cursor.execute("""
                SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_SCHEMA = %s
                AND INDEX_NAME != 'PRIMARY'
                ORDER BY TABLE_NAME, INDEX_NAME
            """, (db_config.database,))
            
            indexes = db.cursor.fetchall()
            print(f"📇 عدد الفهارس: {len(indexes)}")
            
            print("✅ سلامة البيانات جيدة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص سلامة البيانات: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبارات قاعدة البيانات الشاملة")
    print("=" * 50)
    
    tests = [
        ("الاتصال الأساسي", test_basic_connection),
        ("مدير قاعدة البيانات", test_database_manager),
        ("وجود الجداول", test_tables_exist),
        ("العمليات الأساسية", test_sample_operations),
        ("سلامة البيانات", test_data_integrity)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبارات: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! قاعدة البيانات جاهزة للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    os.makedirs('database/logs', exist_ok=True)
    
    # تشغيل جميع الاختبارات
    success = run_all_tests()
    
    if not success:
        print("\n💡 نصائح لحل المشاكل:")
        print("1. تأكد من تشغيل خادم MySQL")
        print("2. تحقق من صحة بيانات الاتصال في ملف .env")
        print("3. قم بتشغيل: python database/config.py")
        print("4. تأكد من وجود قاعدة البيانات والجداول")
        
        sys.exit(1)
    else:
        print("\n🚀 قاعدة البيانات جاهزة للاستخدام!")
        sys.exit(0)
