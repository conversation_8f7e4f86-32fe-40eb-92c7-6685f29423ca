"""
اختبارات العمليات المتقدمة لقاعدة البيانات - Advanced Database Operations Tests
الموقع: database/test_operations.py
"""

import sys
import os
from datetime import datetime, date, timedelta
import logging

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.manager import DatabaseManager
from database.config import db_config

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseOperationsTest:
    """فئة اختبار العمليات المتقدمة"""
    
    def __init__(self):
        """تهيئة اختبارات العمليات"""
        self.test_data = {
            'customers': [],
            'invoices': [],
            'services': []
        }
    
    def test_customer_operations(self) -> bool:
        """اختبار عمليات العملاء"""
        print("🧪 اختبار عمليات العملاء...")
        
        try:
            with DatabaseManager() as db:
                # إنشاء عميل تجريبي
                customer_data = {
                    'name': 'عميل تجريبي للاختبار',
                    'phone': '01234567890',
                    'email': '<EMAIL>',
                    'address': 'عنوان تجريبي للاختبار',
                    'notes': 'عميل تم إنشاؤه للاختبار'
                }
                
                customer_id = db.create_customer(customer_data)
                if not customer_id:
                    print("❌ فشل إنشاء العميل")
                    return False
                
                self.test_data['customers'].append(customer_id)
                print(f"✅ تم إنشاء العميل برقم {customer_id}")
                
                # البحث عن العميل
                found_customers = db.search_customers('تجريبي')
                if not found_customers:
                    print("❌ فشل البحث عن العميل")
                    return False
                
                print(f"✅ تم العثور على {len(found_customers)} عميل")
                
                # الحصول على العميل
                customer = db.get_customer(customer_id)
                if not customer:
                    print("❌ فشل الحصول على بيانات العميل")
                    return False
                
                print(f"✅ تم الحصول على بيانات العميل: {customer['name']}")
                
                return True
                
        except Exception as e:
            print(f"❌ خطأ في اختبار العملاء: {e}")
            return False
    
    def test_invoice_operations(self) -> bool:
        """اختبار عمليات الفواتير"""
        print("\n🧪 اختبار عمليات الفواتير...")
        
        try:
            with DatabaseManager() as db:
                # التأكد من وجود عميل للاختبار
                if not self.test_data['customers']:
                    print("❌ لا يوجد عميل للاختبار")
                    return False
                
                customer_id = self.test_data['customers'][0]
                
                # إنشاء فاتورة تجريبية
                invoice_data = {
                    'customer_id': customer_id,
                    'invoice_date': date.today(),
                    'due_date': date.today() + timedelta(days=30),
                    'gold_weight': 25.5,
                    'gold_karat': 21,
                    'gold_price_per_gram': 75.50,
                    'gold_total': 1925.25,
                    'stone_weight': 2.5,
                    'stone_price_per_carat': 500.00,
                    'stone_total': 1250.00,
                    'additional_services': 'تلميع وتنظيف',
                    'services_total': 100.00,
                    'subtotal': 3275.25,
                    'tax_rate': 14.00,
                    'tax_amount': 458.54,
                    'discount_amount': 0.00,
                    'total_amount': 3733.79,
                    'currency': 'USD',
                    'exchange_rate': 1.0000,
                    'payment_status': 'pending',
                    'paid_amount': 0.00,
                    'notes': 'فاتورة تجريبية للاختبار',
                    'status': 'draft'
                }
                
                invoice_id = db.create_invoice(invoice_data)
                if not invoice_id:
                    print("❌ فشل إنشاء الفاتورة")
                    return False
                
                self.test_data['invoices'].append(invoice_id)
                print(f"✅ تم إنشاء الفاتورة برقم {invoice_id}")
                
                # الحصول على الفاتورة مع بيانات العميل
                invoice = db.get_invoice(invoice_id)
                if not invoice:
                    print("❌ فشل الحصول على بيانات الفاتورة")
                    return False
                
                print(f"✅ تم الحصول على الفاتورة: {invoice['invoice_number']}")
                print(f"   العميل: {invoice['customer_name']}")
                print(f"   المبلغ: {invoice['total_amount']} {invoice['currency']}")
                
                # تحديث حالة الفاتورة
                if not db.update_invoice_status(invoice_id, 'sent'):
                    print("❌ فشل تحديث حالة الفاتورة")
                    return False
                
                print("✅ تم تحديث حالة الفاتورة إلى 'sent'")
                
                # البحث عن الفواتير
                found_invoices = db.search_invoices('تجريبي')
                if not found_invoices:
                    print("❌ فشل البحث عن الفواتير")
                    return False
                
                print(f"✅ تم العثور على {len(found_invoices)} فاتورة")
                
                # الحصول على فواتير العميل
                customer_invoices = db.get_invoices_by_customer(customer_id)
                if not customer_invoices:
                    print("❌ فشل الحصول على فواتير العميل")
                    return False
                
                print(f"✅ العميل لديه {len(customer_invoices)} فاتورة")
                
                return True
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الفواتير: {e}")
            return False
    
    def test_services_operations(self) -> bool:
        """اختبار عمليات الخدمات"""
        print("\n🧪 اختبار عمليات الخدمات...")
        
        try:
            with DatabaseManager() as db:
                # الحصول على جميع الخدمات
                services = db.get_all_services()
                if not services:
                    print("❌ لا توجد خدمات متاحة")
                    return False
                
                print(f"✅ تم العثور على {len(services)} خدمة متاحة")
                
                # عرض بعض الخدمات
                for i, service in enumerate(services[:3]):
                    print(f"   {i+1}. {service['name']} - {service['default_price']} {service['unit']}")
                
                return True
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الخدمات: {e}")
            return False
    
    def test_dashboard_stats(self) -> bool:
        """اختبار إحصائيات لوحة التحكم"""
        print("\n🧪 اختبار إحصائيات لوحة التحكم...")
        
        try:
            with DatabaseManager() as db:
                stats = db.get_dashboard_stats()
                if not stats:
                    print("❌ فشل الحصول على الإحصائيات")
                    return False
                
                print("✅ إحصائيات لوحة التحكم:")
                print(f"   عدد العملاء: {stats.get('total_customers', 0)}")
                print(f"   عدد الفواتير: {stats.get('total_invoices', 0)}")
                print(f"   إجمالي المبيعات: {stats.get('total_sales', 0)}")
                print(f"   الفواتير المعلقة: {stats.get('pending_invoices', 0)}")
                
                return True
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الإحصائيات: {e}")
            return False
    
    def test_data_export(self) -> bool:
        """اختبار تصدير البيانات"""
        print("\n🧪 اختبار تصدير البيانات...")
        
        try:
            with DatabaseManager() as db:
                # تصدير جدول العملاء
                customers_df = db.export_to_dataframe('customers')
                if customers_df is None:
                    print("❌ فشل تصدير جدول العملاء")
                    return False
                
                print(f"✅ تم تصدير {len(customers_df)} عميل")
                
                # تصدير جدول الفواتير
                invoices_df = db.export_to_dataframe('invoices')
                if invoices_df is None:
                    print("❌ فشل تصدير جدول الفواتير")
                    return False
                
                print(f"✅ تم تصدير {len(invoices_df)} فاتورة")
                
                return True
                
        except Exception as e:
            print(f"❌ خطأ في اختبار التصدير: {e}")
            return False
    
    def cleanup_test_data(self) -> bool:
        """تنظيف البيانات التجريبية"""
        print("\n🧹 تنظيف البيانات التجريبية...")
        
        try:
            with DatabaseManager() as db:
                # حذف الفواتير التجريبية
                for invoice_id in self.test_data['invoices']:
                    db.cursor.execute("DELETE FROM invoices WHERE id = %s", (invoice_id,))
                    print(f"🗑️ تم حذف الفاتورة {invoice_id}")
                
                # حذف العملاء التجريبيين
                for customer_id in self.test_data['customers']:
                    db.cursor.execute("DELETE FROM customers WHERE id = %s", (customer_id,))
                    print(f"🗑️ تم حذف العميل {customer_id}")
                
                db.connection.commit()
                print("✅ تم تنظيف جميع البيانات التجريبية")
                
                return True
                
        except Exception as e:
            print(f"❌ خطأ في تنظيف البيانات: {e}")
            return False

def run_advanced_tests():
    """تشغيل جميع الاختبارات المتقدمة"""
    print("🧪 بدء الاختبارات المتقدمة لقاعدة البيانات")
    print("=" * 60)
    
    tester = DatabaseOperationsTest()
    
    tests = [
        ("عمليات العملاء", tester.test_customer_operations),
        ("عمليات الفواتير", tester.test_invoice_operations),
        ("عمليات الخدمات", tester.test_services_operations),
        ("إحصائيات لوحة التحكم", tester.test_dashboard_stats),
        ("تصدير البيانات", tester.test_data_export)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    # تنظيف البيانات التجريبية
    print("\n" + "=" * 60)
    tester.cleanup_test_data()
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبارات المتقدمة: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات المتقدمة نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات المتقدمة فشلت")
        return False

if __name__ == "__main__":
    # التحقق من الاتصال بقاعدة البيانات
    if not db_config.test_connection():
        print("❌ لا يمكن الاتصال بقاعدة البيانات")
        print("💡 قم بتشغيل: python database/config.py")
        sys.exit(1)
    
    # تشغيل الاختبارات المتقدمة
    success = run_advanced_tests()
    sys.exit(0 if success else 1)
