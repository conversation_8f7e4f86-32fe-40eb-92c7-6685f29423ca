"""
إعدادات قاعدة البيانات لنظام إدارة فواتير الورشة
Database Configuration for Crestal Diamond Workshop Invoice System
"""

import os
from typing import Dict, Any

# إعدادات قاعدة البيانات الافتراضية
DEFAULT_DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'crestal_diamond_workshop',
    'user': 'root',
    'password': '',  # سيتم تحديدها من متغيرات البيئة أو المستخدم
    'charset': 'utf8mb4',
    'autocommit': True,
    'connect_timeout': 10,
    'read_timeout': 10,
    'write_timeout': 10
}

def get_database_config() -> Dict[str, Any]:
    """
    الحصول على إعدادات قاعدة البيانات من متغيرات البيئة أو الإعدادات الافتراضية
    Get database configuration from environment variables or default settings
    """
    config = DEFAULT_DB_CONFIG.copy()
    
    # قراءة الإعدادات من متغيرات البيئة إذا كانت متوفرة
    config['host'] = os.getenv('DB_HOST', config['host'])
    config['port'] = int(os.getenv('DB_PORT', config['port']))
    config['database'] = os.getenv('DB_NAME', config['database'])
    config['user'] = os.getenv('DB_USER', config['user'])
    config['password'] = os.getenv('DB_PASSWORD', config['password'])
    
    return config

def create_env_file():
    """
    إنشاء ملف .env للإعدادات الحساسة
    Create .env file for sensitive configuration
    """
    env_content = """# إعدادات قاعدة البيانات - Database Configuration
# قم بتحديث كلمة المرور وفقاً لإعدادات MySQL الخاصة بك
# Update the password according to your MySQL settings

DB_HOST=localhost
DB_PORT=3306
DB_NAME=crestal_diamond_workshop
DB_USER=root
DB_PASSWORD=2452329511

# إعدادات إضافية - Additional Settings
DEBUG=True
SECRET_KEY=your_secret_key_here
"""
    
    if not os.path.exists('.env'):
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("تم إنشاء ملف .env - .env file created")
        print("يرجى تحديث كلمة مرور قاعدة البيانات في الملف")
        print("Please update the database password in the file")

# إنشاء ملف .env عند استيراد الوحدة
if __name__ == "__main__":
    create_env_file()
