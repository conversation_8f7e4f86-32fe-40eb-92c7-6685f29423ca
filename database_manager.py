"""
مدير قاعدة البيانات لنظام إدارة فواتير الورشة
Database Manager for Crestal Diamond Workshop Invoice System
"""

import pandas as pd
import mysql.connector
from mysql.connector import Error
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from database_config import get_database_config

class DatabaseManager:
    def __init__(self, use_mysql: bool = True):
        """
        تهيئة مدير قاعدة البيانات
        Initialize the database manager
        """
        self.use_mysql = use_mysql
        self.connection = None
        
        if self.use_mysql:
            self._connect_to_mysql()
        else:
            # الاحتفاظ بنظام CSV كبديل
            self.data_dir = "data"
            self.invoices_file = os.path.join(self.data_dir, "invoices.csv")
            if not os.path.exists(self.data_dir):
                os.makedirs(self.data_dir)
            if not os.path.exists(self.invoices_file):
                self._create_invoices_file()
    
    def _connect_to_mysql(self):
        """
        الاتصال بقاعدة بيانات MySQL
        Connect to MySQL database
        """
        try:
            config = get_database_config()
            self.connection = mysql.connector.connect(**config)
            if self.connection.is_connected():
                print("✅ تم الاتصال بقاعدة البيانات بنجاح")
                print("✅ Successfully connected to MySQL database")
                return True
        except Error as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            print("🔄 سيتم استخدام نظام CSV كبديل")
            print("🔄 Falling back to CSV system")
            self.use_mysql = False
            self.__init__(use_mysql=False)
            return False
    
    def test_connection(self) -> bool:
        """
        اختبار الاتصال بقاعدة البيانات
        Test database connection
        """
        if self.use_mysql and self.connection:
            try:
                if self.connection.is_connected():
                    cursor = self.connection.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
                    return True
            except Error:
                return False
        return not self.use_mysql  # CSV system is always "connected"
    
    def _create_invoices_file(self):
        """
        إنشاء ملف CSV للفواتير مع الأعمدة المطلوبة
        Create CSV file for invoices with required columns
        """
        columns = [
            'customer_name', 'invoice_date', 'description',
            'gold_change', 'usd_change', 'egp_change', 'created_timestamp'
        ]
        df = pd.DataFrame(columns=columns)
        df.to_csv(self.invoices_file, index=False, encoding='utf-8-sig')
    
    def add_invoice(self, invoice_data: Dict[str, Any]) -> bool:
        """
        إضافة فاتورة جديدة
        Add a new invoice
        """
        if self.use_mysql:
            return self._add_invoice_mysql(invoice_data)
        else:
            return self._add_invoice_csv(invoice_data)
    
    def _add_invoice_mysql(self, invoice_data: Dict[str, Any]) -> bool:
        """
        إضافة فاتورة جديدة إلى MySQL
        Add a new invoice to MySQL
        """
        try:
            cursor = self.connection.cursor()
            
            # إدراج العميل أولاً إذا لم يكن موجوداً
            customer_id = self._get_or_create_customer(invoice_data['customer_name'])
            
            # إدراج الفاتورة
            insert_query = """
            INSERT INTO invoices (customer_id, customer_name, invoice_date, description, 
                                gold_change, usd_change, egp_change)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            values = (
                customer_id,
                invoice_data['customer_name'],
                invoice_data['invoice_date'],
                invoice_data['description'],
                float(invoice_data.get('gold_change', 0)),
                float(invoice_data.get('usd_change', 0)),
                float(invoice_data.get('egp_change', 0))
            )
            
            cursor.execute(insert_query, values)
            self.connection.commit()
            cursor.close()
            return True
            
        except Error as e:
            print(f"خطأ في إضافة الفاتورة إلى MySQL: {e}")
            return False
    
    def _add_invoice_csv(self, invoice_data: Dict[str, Any]) -> bool:
        """
        إضافة فاتورة جديدة إلى CSV
        Add a new invoice to CSV
        """
        try:
            # قراءة البيانات الحالية
            df = pd.read_csv(self.invoices_file, encoding='utf-8-sig')
            
            # إضافة timestamp للإنشاء
            invoice_data['created_timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # إضافة الفاتورة الجديدة
            new_row = pd.DataFrame([invoice_data])
            df = pd.concat([df, new_row], ignore_index=True)
            
            # حفظ البيانات
            df.to_csv(self.invoices_file, index=False, encoding='utf-8-sig')
            return True
            
        except Exception as e:
            print(f"خطأ في إضافة الفاتورة: {e}")
            return False
    
    def _get_or_create_customer(self, customer_name: str) -> int:
        """
        الحصول على معرف العميل أو إنشاؤه إذا لم يكن موجوداً
        Get customer ID or create if not exists
        """
        try:
            cursor = self.connection.cursor()
            
            # البحث عن العميل
            select_query = "SELECT id FROM customers WHERE name = %s"
            cursor.execute(select_query, (customer_name,))
            result = cursor.fetchone()
            
            if result:
                customer_id = result[0]
            else:
                # إنشاء عميل جديد
                insert_query = "INSERT INTO customers (name) VALUES (%s)"
                cursor.execute(insert_query, (customer_name,))
                self.connection.commit()
                customer_id = cursor.lastrowid
            
            cursor.close()
            return customer_id
            
        except Error as e:
            print(f"خطأ في إدارة العميل: {e}")
            return None
    
    def get_all_invoices(self) -> pd.DataFrame:
        """
        الحصول على جميع الفواتير
        Get all invoices
        """
        if self.use_mysql:
            return self._get_all_invoices_mysql()
        else:
            return self._get_all_invoices_csv()
    
    def _get_all_invoices_mysql(self) -> pd.DataFrame:
        """
        الحصول على جميع الفواتير من MySQL
        Get all invoices from MySQL
        """
        try:
            query = """
            SELECT customer_name, invoice_date as date, description, 
                   gold_change, usd_change, egp_change, created_timestamp as timestamp
            FROM invoices 
            ORDER BY created_timestamp DESC
            """
            df = pd.read_sql(query, self.connection)
            return df
        except Error as e:
            print(f"خطأ في قراءة الفواتير من MySQL: {e}")
            return pd.DataFrame()
    
    def _get_all_invoices_csv(self) -> pd.DataFrame:
        """
        الحصول على جميع الفواتير من CSV
        Get all invoices from CSV
        """
        try:
            if os.path.exists(self.invoices_file):
                df = pd.read_csv(self.invoices_file, encoding='utf-8-sig')
                # تحويل أسماء الأعمدة للتوافق مع النظام الحالي
                if 'invoice_date' in df.columns:
                    df = df.rename(columns={'invoice_date': 'date'})
                if 'created_timestamp' in df.columns:
                    df = df.rename(columns={'created_timestamp': 'timestamp'})
                return df
            else:
                return pd.DataFrame()
        except Exception as e:
            print(f"خطأ في قراءة الفواتير: {e}")
            return pd.DataFrame()
    
    def close_connection(self):
        """
        إغلاق الاتصال بقاعدة البيانات
        Close database connection
        """
        if self.use_mysql and self.connection and self.connection.is_connected():
            self.connection.close()
            print("تم إغلاق الاتصال بقاعدة البيانات")
            print("Database connection closed")
    
    def __del__(self):
        """
        تنظيف الموارد عند حذف الكائن
        Cleanup resources when object is deleted
        """
        self.close_connection()
