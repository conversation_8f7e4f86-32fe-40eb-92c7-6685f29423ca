-- إن<PERSON><PERSON><PERSON> قاعدة بيانات نظام إدارة فواتير الورشة
-- Crestal Diamond Workshop Invoice System Database

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS crestal_diamond_workshop 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE crestal_diamond_workshop;

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    address TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_phone (phone)
);

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT,
    customer_name VARCHAR(255) NOT NULL,
    invoice_date DATE NOT NULL,
    description TEXT,
    gold_change DECIMAL(10,3) DEFAULT 0.000,
    usd_change DECIMAL(10,2) DEFAULT 0.00,
    egp_change DECIMAL(10,2) DEFAULT 0.00,
    created_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    INDEX idx_customer_name (customer_name),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_created_timestamp (created_timestamp)
);

-- جدول تفاصيل الفواتير (للتوسع المستقبلي)
CREATE TABLE IF NOT EXISTS invoice_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    item_type ENUM('gold_work', 'stone_setting', 'additional_service') NOT NULL,
    item_name VARCHAR(255),
    quantity DECIMAL(10,3) DEFAULT 1.000,
    unit_price DECIMAL(10,2) DEFAULT 0.00,
    total_price DECIMAL(10,2) DEFAULT 0.00,
    currency ENUM('USD', 'EGP') DEFAULT 'USD',
    notes TEXT,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_item_type (item_type)
);

-- جدول الخدمات (للتوسع المستقبلي)
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    default_price DECIMAL(10,2) DEFAULT 0.00,
    currency ENUM('USD', 'EGP') DEFAULT 'USD',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_is_active (is_active)
);

-- جدول سجل النشاطات (للمراجعة)
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id INT,
    action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    old_values JSON,
    new_values JSON,
    user_info VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_table_name (table_name),
    INDEX idx_action (action),
    INDEX idx_timestamp (timestamp)
);

-- إدراج بعض الخدمات الافتراضية
INSERT INTO services (name, description, default_price, currency) VALUES
('بانيو أبيض', 'خدمة الطلاء الأبيض للمجوهرات', 50.00, 'EGP'),
('بانيو أصفر', 'خدمة الطلاء الأصفر للمجوهرات', 50.00, 'EGP'),
('دمغة', 'خدمة الدمغة والختم', 25.00, 'EGP'),
('تصليح', 'خدمات التصليح العامة', 100.00, 'EGP'),
('مصنعية ذهب', 'أجرة تصنيع المجوهرات الذهبية', 15.00, 'USD'),
('تركيب أحجار', 'خدمة تركيب الأحجار الكريمة', 30.00, 'EGP');

-- إنشاء مستخدم للتطبيق (اختياري)
-- CREATE USER IF NOT EXISTS 'crestal_app'@'localhost' IDENTIFIED BY 'secure_password_123';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON crestal_diamond_workshop.* TO 'crestal_app'@'localhost';
-- FLUSH PRIVILEGES;

-- عرض الجداول المنشأة
SHOW TABLES;

-- عرض بنية جدول الفواتير
DESCRIBE invoices;

-- عرض بنية جدول العملاء
DESCRIBE customers;
