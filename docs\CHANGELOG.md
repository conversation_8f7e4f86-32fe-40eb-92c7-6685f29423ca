# سجل التغييرات - Changelog

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

## [4.1.0] - 2025-07-02 🏗️ **إصدار المشروع المنظم مع الإصلاحات الشاملة**

### 🏗️ إعادة التنظيم الشاملة
- **تنظيم كامل للمشروع** مع هيكل احترافي منظم
- **نقل جميع الملفات** إلى مجلداتها المناسبة:
  - ملفات الاختبار → `tests/`
  - ملفات البيانات → `data/`
  - سكريبتات التشغيل → `scripts/`
  - ملفات السجلات → `logs/`
- **تنظيف الجذر الرئيسي** من الملفات المبعثرة
- **إنشاء دليل هيكل المشروع** `PROJECT_STRUCTURE.md`

### 🔧 الإصلاحات البرمجية الشاملة
- **إصلاح خطأ TypeError** `bad operand type for abs(): 'str'` في جميع الملفات
- **تحسين معالجة البيانات** باستخدام `pd.to_numeric(errors='coerce')`
- **إصلاح العمليات الحسابية** في:
  - `src/pages/customer_accounts.py`
  - `src/invoice_app.py`
  - `src/pages/reports.py`
  - `src/core/database.py`
  - `src/models/invoice.py`
- **معالجة محسنة للتواريخ** مع `pd.to_datetime()`

### ✨ الميزات الجديدة
- **سكريبت تشغيل محسن** `scripts/run_organized_app.bat`
- **فحص تلقائي للهيكل** عند التشغيل
- **رسائل خطأ واضحة** ومفيدة
- **دعم إنشاء المجلدات** التلقائي

### 📁 الملفات المنقولة والمنظمة
#### ملفات الاختبار → `tests/`:
- `comprehensive_system_test.py`, `comprehensive_test.py`
- `final_invoice_test.py`, `quick_final_test.py`
- `test_app.py`, `test_db_simple.py`
- `test_invoice_save.py`, `test_main.py`

#### ملفات البيانات → `data/`:
- `customers.csv`, `invoices.csv`

#### سكريبتات التشغيل → `scripts/`:
- `run_integrated_system.bat`, `run_main_test.bat`
- `run_with_ai.bat`, `start_system.bat`
- `test_ai.bat`, `test_ai_quick.bat`

#### ملفات السجلات → `logs/`:
- `db_test_result.txt`

### 🔄 التحديثات والتحسينات
- **تحديث شامل لـ README.md** ليعكس الهيكل الجديد
- **تحسين التوثيق** مع أمثلة عملية
- **سكريبتات تشغيل محسنة** مع فحص البيئة
- **رسائل نظام واضحة** باللغة العربية

---

## [4.0.0] - 2025-07-02 🧠 **إصدار الذكاء الاصطناعي**

### 🤖 نظام الذكاء الاصطناعي الجديد
- **كريستال AI Agent**: مساعد ذكي يفهم اللغة العربية الطبيعية
- **نظام ذاكرة متقدم**: قاعدة بيانات SQLite مع 5 جداول متخصصة
- **4 وكلاء متخصصين**:
  - 📊 محلل البيانات الذكي
  - 👥 مستشار العملاء الذكي
  - 📦 مدير المخزون الذكي
  - 💰 مساعد التسعير الذكي

### 🧠 الميزات الذكية الجديدة
- **محادثة تفاعلية** مع كريستال في واجهة Streamlit
- **تحليل المشاعر** وفهم السياق للتفاعلات
- **توصيات مخصصة** لكل عميل بناءً على تاريخه
- **تحليلات تنبؤية** للمبيعات والمخزون
- **حاسبة تسعير ذكية** مع اقتراحات محسنة
- **رؤى ذكية** لجميع جوانب العمل

### 📁 البنية الجديدة
- **مجلد `memory/`**: نظام الذكاء الاصطناعي الكامل
- **5 مكونات أساسية**: النواة، الوكلاء، التخزين، الواجهات، التكامل
- **اختبارات شاملة**: نظام اختبار متكامل ومبسط
- **توثيق شامل**: دليل مفصل مع أمثلة عملية

### 🔧 أدوات التطوير الجديدة
- **`memory/quick_start.py`**: عرض توضيحي تفاعلي
- **`memory/setup_ai_system.py`**: إعداد النظام تلقائياً
- **`memory/test_ai_system.py`**: اختبارات شاملة
- **`memory/streamlit_integration.py`**: تكامل مع Streamlit
- **`run_with_ai.bat`**: تشغيل سريع مع فحص النظام

### 📊 الإحصائيات الحالية
- **79 ذكرى** محفوظة في النظام
- **10 ذكريات عملاء** مخصصة
- **11 نمط سلوك** مكتشف
- **جميع الاختبارات** نجحت بنسبة 100%

## [2.1.0] - 2024-01-16

### 🗂️ إعادة تنظيم المشروع
- إنشاء مجلد `archive/` للملفات المرجعية
- نقل الملفات غير المستخدمة إلى الأرشيف:
  - `app.py` (الإصدار الأول)
  - `main_app.py` (الإصدار المتوسط)
  - `01_بيانات_العملاء.py` (الصفحة المنفصلة)
- إضافة `README_ARCHIVE.md` لتوثيق الملفات المؤرشفة
- تحديث `README.md` ليعكس البنية الجديدة
- تحديث `run_all_apps.bat` للتطبيقات النشطة فقط

### 🎯 توضيح البنية
- **الملف الرئيسي:** `invoice_app.py` (قلب المشروع)
- **الملف المساعد:** `excel_analyzer.py` (محلل منفصل)
- **الأرشيف:** جميع الإصدارات القديمة محفوظة للمرجعية

## [2.0.0] - 2024-01-15

### ✨ إضافات جديدة
- إضافة نظام التنقل متعدد الصفحات
- صفحة تحليل ملفات Excel المتقدمة
- صفحة الإحصائيات والتقارير التفاعلية
- صفحة الإعدادات وإدارة البيانات
- رسوم بيانية تفاعلية باستخدام Plotly
- نظام البحث والفلترة في الفواتير
- تصدير البيانات إلى Excel
- نظام النسخ الاحتياطية
- واجهة محسنة مع containers وحدود
- دعم timestamp للفواتير

### 🔧 تحسينات
- تحسين واجهة المستخدم بشكل كبير
- تحسين معالجة الأخطاء
- تحسين أداء قراءة ملفات Excel
- إضافة رسائل تأكيد وتحذير
- تحسين تنظيم الكود وإضافة التوثيق

### 🐛 إصلاحات
- إصلاح مشاكل ترميز النصوص العربية
- إصلاح مشاكل حفظ البيانات
- إصلاح مشاكل قراءة ملفات CSV

## [1.5.0] - 2024-01-10

### ✨ إضافات جديدة
- إضافة حفظ الفواتير في ملف CSV
- إضافة حقل وصف الفاتورة
- إضافة timestamp للفواتير

### 🔧 تحسينات
- تحسين دالة حفظ البيانات
- إضافة معالجة أفضل للأخطاء

## [1.0.0] - 2024-01-01

### ✨ الإصدار الأول
- نظام فواتير أساسي
- حساب تكاليف الذهب والمصنعية
- دعم الأحجار الكريمة
- دعم العملتين (دولار وجنيه)
- خدمات إضافية (بانيو، دمغة، تصليح)
- واجهة Streamlit أساسية

---

## تنسيق سجل التغييرات

هذا السجل يتبع [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
ويلتزم بـ [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### أنواع التغييرات
- **✨ إضافات جديدة** - للميزات الجديدة
- **🔧 تحسينات** - للتحسينات على الميزات الموجودة
- **🐛 إصلاحات** - لإصلاح الأخطاء
- **🗑️ إزالة** - للميزات المحذوفة
- **🔒 أمان** - للتحديثات الأمنية
- **📚 توثيق** - للتغييرات في التوثيق فقط
