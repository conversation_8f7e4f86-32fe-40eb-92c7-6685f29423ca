# دليل إعداد قاعدة البيانات - Database Setup Guide

## نظام إدارة فواتير الورشة - Crestal Diamond Workshop Invoice System

### 📋 المتطلبات - Requirements

1. **MySQL Server** - يج<PERSON> أن يكون مثبت ويعمل على النظام
2. **MySQL Workbench** - لإدارة قاعدة البيانات (اختياري)
3. **Python Libraries** - مكتبات Python المطلوبة

### 🚀 خطوات الإعداد - Setup Steps

#### الخطوة 1: تثبيت المكتبات المطلوبة
```bash
# تشغيل ملف التثبيت
install_mysql_requirements.bat

# أو تثبيت يدوي
pip install mysql-connector-python>=8.1.0
pip install PyMySQL>=1.1.0
pip install python-dotenv>=1.0.0
```

#### الخطوة 2: إن<PERSON>اء قاعدة البيانات
1. افتح **MySQL Workbench**
2. اتصل بخادم MySQL المحلي
3. افتح ملف `database_setup.sql`
4. قم بتشغيل السكريبت لإنشاء قاعدة البيانات والجداول

```sql
-- أو يمكنك تشغيل الأوامر التالية مباشرة
SOURCE database_setup.sql;
```

#### الخطوة 3: إعداد ملف البيئة
1. قم بتشغيل `database_config.py` لإنشاء ملف `.env`
```bash
python database_config.py
```

2. افتح ملف `.env` وقم بتحديث إعدادات قاعدة البيانات:
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=crestal_diamond_workshop
DB_USER=root
DB_PASSWORD=your_mysql_password_here
```

#### الخطوة 4: اختبار الاتصال
قم بتشغيل التطبيق للتأكد من نجاح الاتصال:
```bash
streamlit run invoice_app.py
```

### 🗄️ بنية قاعدة البيانات - Database Structure

#### جدول العملاء - Customers Table
```sql
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    address TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### جدول الفواتير - Invoices Table
```sql
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT,
    customer_name VARCHAR(255) NOT NULL,
    invoice_date DATE NOT NULL,
    description TEXT,
    gold_change DECIMAL(10,3) DEFAULT 0.000,
    usd_change DECIMAL(10,2) DEFAULT 0.00,
    egp_change DECIMAL(10,2) DEFAULT 0.00,
    created_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 🔧 استكشاف الأخطاء - Troubleshooting

#### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تشغيل خادم MySQL
2. تحقق من صحة كلمة المرور في ملف `.env`
3. تأكد من وجود قاعدة البيانات `crestal_diamond_workshop`

#### مكتبات غير متوفرة
```bash
# إعادة تثبيت المكتبات
pip uninstall mysql-connector-python
pip install mysql-connector-python>=8.1.0
```

#### النظام الاحتياطي CSV
إذا فشل الاتصال بقاعدة البيانات، سيعمل النظام تلقائياً بنظام CSV كبديل.

### 📊 مميزات قاعدة البيانات - Database Features

✅ **الأداء المحسن** - استعلامات أسرع وأكثر كفاءة
✅ **الأمان** - حماية أفضل للبيانات
✅ **التوسع** - إمكانية إضافة مميزات متقدمة
✅ **النسخ الاحتياطي** - سهولة عمل نسخ احتياطية
✅ **التقارير** - إمكانيات تقارير متقدمة
✅ **تعدد المستخدمين** - دعم عدة مستخدمين (مستقبلاً)

### 🔄 الترحيل من CSV إلى MySQL

إذا كان لديك بيانات في ملف CSV، يمكن ترحيلها إلى قاعدة البيانات:

```python
# سكريبت ترحيل البيانات (سيتم إضافته لاحقاً)
python migrate_csv_to_mysql.py
```

### 📞 الدعم الفني - Technical Support

في حالة مواجهة أي مشاكل:
1. تحقق من ملف `logs/` للأخطاء
2. راجع هذا الدليل
3. تأكد من تحديث جميع المكتبات

---

**ملاحظة**: النظام يعمل بنظام CSV كبديل إذا لم تكن قاعدة البيانات متوفرة، لذا لن تفقد أي وظائف أساسية.
