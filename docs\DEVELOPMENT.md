# 🛠️ دليل التطوير - Development Guide

## 📋 نظرة عامة للمطورين

هذا الدليل مخصص للمطورين الذين يريدون المساهمة في تطوير نظام إدارة ورشة Crestal Diamond أو فهم البنية التقنية للمشروع.

## 🏗️ بنية المشروع

### الملفات الرئيسية

```
company app 1/
├── 📄 invoice_app.py          # التطبيق الرئيسي المحسن
├── 🔍 excel_analyzer.py       # محلل ملفات Excel المنفصل
├── 📱 main_app.py             # التطبيق الأصلي البسيط
├── 📋 requirements.txt        # المكتبات المطلوبة
├── 🔧 install_requirements.bat # سكريبت التثبيت
├── 📊 invoices.csv           # بيانات الفواتير (يتم إنشاؤه تلقائياً)
├── 📖 README.md              # دليل المستخدم
├── 🌍 README_EN.md           # دليل المستخدم (إنجليزي)
├── 📝 CHANGELOG.md           # سجل التغييرات
├── 🛠️ DEVELOPMENT.md         # هذا الملف
├── ⚖️ LICENSE                # رخصة المشروع
└── 🚫 .gitignore             # ملفات Git المستبعدة
```

### بنية الكود

#### `invoice_app.py` - التطبيق الرئيسي
```python
# الاستيرادات والإعدادات
import streamlit as st
import pandas as pd
# ... المكتبات الأخرى

# الدوال المساعدة
def save_invoice_to_csv()    # حفظ الفواتير
def load_invoices()          # تحميل الفواتير
def delete_invoice()         # حذف فاتورة

# فئة تحليل Excel
class ExcelAnalyzer:
    def find_excel_files()   # البحث عن ملفات Excel
    def read_excel_file()    # قراءة ملف واحد
    def analyze_all_files()  # تحليل جميع الملفات

# الصفحات
if page == "📄 إنشاء فاتورة جديدة":
    # كود صفحة الفواتير
elif page == "📊 عرض الفواتير المحفوظة":
    # كود صفحة عرض البيانات
elif page == "🔍 تحليل ملفات Excel":
    # كود صفحة تحليل Excel
# ... باقي الصفحات
```

## 🔧 إعداد بيئة التطوير

### 1. متطلبات النظام
```bash
Python 3.8+
Git
Code Editor (VS Code مُوصى به)
```

### 2. إعداد البيئة الافتراضية
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة (Windows)
venv\Scripts\activate

# تفعيل البيئة (Linux/Mac)
source venv/bin/activate

# تثبيت المكتبات
pip install -r requirements.txt
```

### 3. إعداد VS Code
```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black"
}
```

## 📊 هيكل البيانات

### ملف الفواتير (invoices.csv)
```csv
customer_name,date,description,gold_change,usd_change,egp_change,timestamp
نوع البيانات: string,date,string,float,float,float,datetime
```

### مثال على البيانات
```csv
أحمد محمد,2024-01-15,خاتم ذهب عيار 21,-5.2,150.00,75.50,2024-01-15 14:30:25
فاطمة علي,2024-01-16,سلسلة ذهب,-3.8,120.00,60.00,2024-01-16 10:15:30
```

## 🎨 إرشادات التصميم

### ألوان النظام
```python
# الألوان الأساسية
PRIMARY_COLOR = "#FFD700"      # ذهبي
SECONDARY_COLOR = "#1E3A8A"    # أزرق داكن
SUCCESS_COLOR = "#10B981"      # أخضر
ERROR_COLOR = "#EF4444"        # أحمر
WARNING_COLOR = "#F59E0B"      # برتقالي
```

### أيقونات النظام
```python
ICONS = {
    "invoice": "📄",
    "customer": "👤", 
    "gold": "⚖️",
    "diamond": "💎",
    "money": "💰",
    "chart": "📊",
    "settings": "⚙️"
}
```

## 🔄 دورة التطوير

### 1. إضافة ميزة جديدة
```bash
# إنشاء branch جديد
git checkout -b feature/new-feature-name

# تطوير الميزة
# ... كتابة الكود

# اختبار الميزة
streamlit run invoice_app.py

# commit التغييرات
git add .
git commit -m "Add: new feature description"

# push للمستودع
git push origin feature/new-feature-name
```

### 2. إصلاح خطأ
```bash
# إنشاء branch للإصلاح
git checkout -b bugfix/bug-description

# إصلاح الخطأ
# ... كتابة الكود

# اختبار الإصلاح
# ... التأكد من عمل الإصلاح

# commit الإصلاح
git commit -m "Fix: bug description"
```

## 🧪 الاختبار

### اختبار يدوي
```bash
# تشغيل التطبيق
streamlit run invoice_app.py

# اختبار الوظائف:
# 1. إنشاء فاتورة جديدة
# 2. عرض الفواتير
# 3. تحليل ملفات Excel
# 4. التقارير والإحصائيات
# 5. الإعدادات
```

### اختبار البيانات
```python
# اختبار حفظ البيانات
def test_save_invoice():
    save_invoice_to_csv("عميل تجريبي", "2024-01-01", -1.0, 50.0, 25.0, "اختبار")
    df = load_invoices()
    assert len(df) > 0
    assert df.iloc[-1]['customer_name'] == "عميل تجريبي"
```

## 📈 تحسين الأداء

### نصائح للأداء
```python
# استخدام cache في Streamlit
@st.cache_data
def load_large_dataset():
    return pd.read_csv("large_file.csv")

# تحسين قراءة Excel
def read_excel_optimized(file_path):
    # قراءة أول 1000 صف فقط للمعاينة
    return pd.read_excel(file_path, nrows=1000)
```

## 🔒 الأمان

### حماية البيانات
```python
# تشفير البيانات الحساسة
import hashlib

def hash_customer_data(data):
    return hashlib.sha256(data.encode()).hexdigest()

# التحقق من صحة المدخلات
def validate_input(value, data_type):
    if data_type == "float":
        try:
            return float(value)
        except ValueError:
            return None
```

## 🚀 النشر

### نشر محلي
```bash
# تشغيل على منفذ مخصص
streamlit run invoice_app.py --server.port 8080

# تشغيل على شبكة محلية
streamlit run invoice_app.py --server.address 0.0.0.0
```

### نشر على السحابة
```bash
# Streamlit Cloud
# 1. رفع الكود على GitHub
# 2. ربط المستودع بـ Streamlit Cloud
# 3. نشر التطبيق

# Heroku
# 1. إنشاء Procfile
echo "web: streamlit run invoice_app.py --server.port=$PORT --server.address=0.0.0.0" > Procfile
# 2. نشر على Heroku
```

## 🐛 تتبع الأخطاء

### سجلات الأخطاء
```python
import logging

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

# استخدام السجلات
try:
    # كود قد يسبب خطأ
    pass
except Exception as e:
    logging.error(f"خطأ في الوظيفة: {str(e)}")
    st.error(f"حدث خطأ: {str(e)}")
```

## 📚 مصادر إضافية

### وثائق المكتبات
- [Streamlit Documentation](https://docs.streamlit.io/)
- [Pandas Documentation](https://pandas.pydata.org/docs/)
- [Plotly Documentation](https://plotly.com/python/)

### أدوات التطوير
- [VS Code](https://code.visualstudio.com/)
- [Git](https://git-scm.com/)
- [Python](https://www.python.org/)

## 👥 فريق التطوير

### أدوار الفريق
- **مطور رئيسي:** تطوير الميزات الأساسية
- **مطور واجهة:** تحسين تجربة المستخدم
- **محلل بيانات:** تطوير التقارير والإحصائيات
- **مختبر:** ضمان جودة التطبيق

### قواعد الكود
```python
# استخدام أسماء واضحة للمتغيرات
customer_name = "أحمد محمد"  # ✅ جيد
cn = "أحمد محمد"             # ❌ سيء

# إضافة تعليقات للكود المعقد
def complex_calculation(data):
    """
    حساب معقد للبيانات
    Args:
        data: البيانات المدخلة
    Returns:
        النتيجة المحسوبة
    """
    # خطوات الحساب...
    return result

# استخدام معالجة الأخطاء
try:
    result = risky_operation()
except Exception as e:
    st.error(f"خطأ: {str(e)}")
```

---

**آخر تحديث:** يناير 2024  
**للاستفسارات التقنية:** <EMAIL>
