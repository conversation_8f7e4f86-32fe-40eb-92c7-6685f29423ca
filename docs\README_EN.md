# 💎 Crestal Diamond Workshop Management System

## 📋 Overview

A comprehensive management system for gold and jewelry workshops that provides integrated solutions for invoice management, data analysis, and financial reporting. Built with Python and Streamlit to deliver an interactive and user-friendly interface.

## ✨ Key Features

### 📄 Invoice System
- Interactive customer invoice creation
- Gold and craftsmanship cost calculations
- Gemstone management (customer or workshop stones)
- Dual currency support (USD and EGP)
- Additional services (plating, hallmarking, repairs)
- Automatic invoice saving to CSV files

### 📊 Data Management
- View all saved invoices
- Search and filter by customer and date
- Comprehensive financial statistics
- Export data to Excel
- Data backup functionality

### 🔍 Excel File Analysis
- Read and analyze Excel & CSV files
- Preview file contents
- Display file statistics (rows, columns, sheets)
- Export data in different formats
- Error handling for corrupted files

### 📈 Reports & Analytics
- Interactive sales charts
- Customer performance analysis
- Monthly and financial statistics
- Gold consumption reports
- Key performance indicators

## 🛠️ Technologies Used

- **Python 3.8+** - Core programming language
- **Streamlit** - User interface framework
- **Pandas** - Data processing and analysis
- **Plotly** - Interactive charts
- **OpenPyXL** - Excel file reading
- **SQLAlchemy** - Database management

## 📦 Installation & Setup

### Prerequisites
```bash
Python 3.8 or newer
pip (package manager)
```

### Installation Steps

1. **Clone the project:**
```bash
git clone [repository-url]
cd "company app 1"
```

2. **Install required packages:**
```bash
pip install -r requirements.txt
```

Or manual installation:
```bash
pip install streamlit pandas openpyxl xlrd plotly matplotlib seaborn sqlalchemy
```

3. **Run the application:**
```bash
streamlit run invoice_app.py
```

## 🚀 Usage

### Running the Application
```bash
# Enhanced main application
streamlit run invoice_app.py

# Standalone Excel analyzer
streamlit run excel_analyzer.py

# Original simple application
streamlit run main_app.py
```

### Accessing the Application
Open your browser and navigate to: `http://localhost:8501`

## 📱 User Guide

### 📄 Creating New Invoice
1. Select "📄 Create New Invoice" from sidebar
2. Enter customer information and date
3. Calculate gold and craftsmanship costs
4. Add gemstone and additional service costs
5. Review final summary and save invoice

### 📊 Viewing Invoices
1. Select "📊 View Saved Invoices"
2. Use search and filtering as needed
3. Review quick statistics
4. Export data or delete invoices

### 🔍 Excel File Analysis
1. Select "🔍 Excel File Analysis"
2. Enter folder path containing files
3. Click "Analyze Files"
4. Review results and statistics
5. Use export and import options

### 📈 Reports & Statistics
1. Select "📈 Statistics & Reports"
2. Review general statistics
3. Interact with charts
4. Review top customers table

## 📁 Project Structure

```
company app 1/
├── invoice_app.py          # Enhanced main application
├── excel_analyzer.py       # Standalone Excel analyzer
├── main_app.py            # Original simple application
├── requirements.txt       # Required packages list
├── install_requirements.bat # Package installation script
├── invoices.csv          # Saved invoices file (auto-created)
└── README.md             # This file
```

## 💾 Data Management

### Data Files
- **`invoices.csv`** - Contains all saved invoices
- **Backups** - Can be created from settings page

### Data Format
```csv
customer_name,date,description,gold_change,usd_change,egp_change,timestamp
Ahmed Mohamed,2024-01-15,Gold Ring,5.2,150.00,75.50,2024-01-15 14:30:25
```

## 🔧 Settings & Customization

### Changing Default Paths
In `invoice_app.py`, you can modify:
```python
default_path = r"C:\Users\<USER>\OneDrive\Desktop\crestal diamond"
```

### Adding New Currencies
The system can be extended to support additional currencies by modifying calculation functions.

## 🐛 Troubleshooting

### Common Issues & Solutions

**Package installation error:**
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

**Excel file reading error:**
- Verify file path is correct
- Check that file is not open in another program
- Try converting file to CSV

**Data saving error:**
- Ensure write permissions in folder
- Check available disk space

## 🔄 Future Updates

### Planned Features
- [ ] Advanced database (PostgreSQL/MySQL)
- [ ] User authentication system
- [ ] Mobile companion app
- [ ] Accounting system integration
- [ ] Printable PDF reports
- [ ] Inventory alert system
- [ ] API interface

## 👥 Contributing

We welcome contributions! Please:
1. Fork the project
2. Create a new feature branch
3. Implement changes with documentation
4. Submit a Pull Request

## 📞 Support & Contact

For technical support or inquiries:
- **Email:** <EMAIL>
- **Phone:** +20 XXX XXX XXXX
- **Website:** www.crestal-diamond.com

## 📄 License

This project is licensed under the MIT License. See `LICENSE` file for details.

## 🙏 Acknowledgments

- Crestal Diamond Development Team
- Streamlit Community
- Arabic Python Community

---

**Developed by:** Crestal Diamond Team  
**Last Updated:** January 2024  
**Version:** 2.0
