"""
أداة تحليل ملفات Excel لمشروع Crestal Diamond
تقوم بقراءة وتحليل جميع ملفات Excel في المجلد المحدد
"""

import pandas as pd
import os
import streamlit as st
from pathlib import Path
import openpyxl
from typing import Dict, List, Any
import traceback

class ExcelAnalyzer:
    def __init__(self, folder_path: str):
        """
        تهيئة محلل ملفات Excel
        
        Args:
            folder_path: مسار المجلد الذي يحتوي على ملفات Excel
        """
        self.folder_path = Path(folder_path)
        self.excel_files = []
        self.analysis_results = {}
        
    def find_excel_files(self) -> List[str]:
        """البحث عن جميع ملفات Excel في المجلد"""
        excel_extensions = ['.xlsx', '.xls', '.csv']
        self.excel_files = []
        
        if self.folder_path.exists():
            for ext in excel_extensions:
                self.excel_files.extend(list(self.folder_path.glob(f'*{ext}')))
        
        return [str(file) for file in self.excel_files]
    
    def read_excel_file(self, file_path: str) -> Dict[str, Any]:
        """
        قراءة ملف Excel واستخراج المعلومات الأساسية
        
        Args:
            file_path: مسار الملف
            
        Returns:
            قاموس يحتوي على معلومات الملف
        """
        file_info = {
            'file_name': os.path.basename(file_path),
            'file_path': file_path,
            'sheets': [],
            'total_rows': 0,
            'total_columns': 0,
            'error': None,
            'data_preview': None
        }
        
        try:
            # تحديد نوع الملف
            if file_path.endswith('.csv'):
                # قراءة ملف CSV
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                file_info['sheets'] = ['Sheet1']
                file_info['total_rows'] = len(df)
                file_info['total_columns'] = len(df.columns)
                file_info['data_preview'] = df.head()
                file_info['columns'] = list(df.columns)
                
            else:
                # قراءة ملف Excel
                excel_file = pd.ExcelFile(file_path)
                file_info['sheets'] = excel_file.sheet_names
                
                # قراءة الورقة الأولى للمعاينة
                df = pd.read_excel(file_path, sheet_name=0)
                file_info['total_rows'] = len(df)
                file_info['total_columns'] = len(df.columns)
                file_info['data_preview'] = df.head()
                file_info['columns'] = list(df.columns)
                
        except Exception as e:
            file_info['error'] = str(e)
            st.error(f"خطأ في قراءة الملف {file_info['file_name']}: {str(e)}")
            
        return file_info
    
    def analyze_all_files(self) -> Dict[str, Any]:
        """تحليل جميع ملفات Excel في المجلد"""
        files = self.find_excel_files()
        
        if not files:
            st.warning("لم يتم العثور على ملفات Excel في المجلد المحدد")
            return {}
        
        st.info(f"تم العثور على {len(files)} ملف")
        
        for file_path in files:
            st.write(f"🔍 تحليل الملف: {os.path.basename(file_path)}")
            file_info = self.read_excel_file(file_path)
            self.analysis_results[file_info['file_name']] = file_info
            
        return self.analysis_results
    
    def display_analysis_results(self):
        """عرض نتائج التحليل في واجهة Streamlit"""
        if not self.analysis_results:
            st.warning("لا توجد نتائج تحليل للعرض")
            return
        
        st.header("📊 نتائج تحليل ملفات Excel")
        
        for file_name, file_info in self.analysis_results.items():
            with st.expander(f"📄 {file_name}"):
                if file_info['error']:
                    st.error(f"خطأ: {file_info['error']}")
                    continue
                
                # معلومات أساسية
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("عدد الصفوف", file_info['total_rows'])
                with col2:
                    st.metric("عدد الأعمدة", file_info['total_columns'])
                with col3:
                    st.metric("عدد الأوراق", len(file_info['sheets']))
                
                # أسماء الأعمدة
                if 'columns' in file_info:
                    st.subheader("أسماء الأعمدة:")
                    st.write(file_info['columns'])
                
                # معاينة البيانات
                if file_info['data_preview'] is not None:
                    st.subheader("معاينة البيانات (أول 5 صفوف):")
                    st.dataframe(file_info['data_preview'])
    
    def get_file_data(self, file_name: str, sheet_name: str = None) -> pd.DataFrame:
        """
        الحصول على بيانات ملف محدد
        
        Args:
            file_name: اسم الملف
            sheet_name: اسم الورقة (للملفات Excel فقط)
            
        Returns:
            DataFrame يحتوي على البيانات
        """
        if file_name not in self.analysis_results:
            raise ValueError(f"الملف {file_name} غير موجود في النتائج")
        
        file_info = self.analysis_results[file_name]
        file_path = file_info['file_path']
        
        try:
            if file_path.endswith('.csv'):
                return pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                return pd.read_excel(file_path, sheet_name=sheet_name or 0)
        except Exception as e:
            st.error(f"خطأ في قراءة البيانات: {str(e)}")
            return pd.DataFrame()


def main():
    """الدالة الرئيسية لتشغيل محلل ملفات Excel"""
    st.title("🔍 محلل ملفات Excel - Crestal Diamond")
    
    # مسار المجلد
    default_path = r"C:\Users\<USER>\OneDrive\Desktop\crestal diamond"
    folder_path = st.text_input("مسار مجلد ملفات Excel:", value=default_path)
    
    if st.button("تحليل الملفات"):
        if folder_path and os.path.exists(folder_path):
            analyzer = ExcelAnalyzer(folder_path)
            
            with st.spinner("جاري تحليل الملفات..."):
                results = analyzer.analyze_all_files()
            
            if results:
                analyzer.display_analysis_results()
                
                # حفظ النتائج في session state للاستخدام لاحقاً
                st.session_state['analyzer'] = analyzer
                st.session_state['analysis_results'] = results
                
                st.success("تم تحليل الملفات بنجاح!")
            else:
                st.error("فشل في تحليل الملفات")
        else:
            st.error("المسار المحدد غير صحيح أو غير موجود")


if __name__ == "__main__":
    main()
