#!/usr/bin/env python3
"""
سكريبت إصلاح البيانات
تنظيف وإصلاح البيانات في قاعدة البيانات
"""

import pandas as pd
import mysql.connector
from database.config import get_database_config

def fix_database_data():
    """إصلاح البيانات في قاعدة البيانات"""
    try:
        # الاتصال بقاعدة البيانات
        config = get_database_config()
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("🔄 بدء إصلاح البيانات...")
        
        # 1. إصلاح بيانات العملاء
        print("👥 إصلاح بيانات العملاء...")
        
        # إزالة المساحات الزائدة من أسماء العملاء
        cursor.execute("""
            UPDATE customers 
            SET name = TRIM(name)
            WHERE name != TRIM(name)
        """)
        
        cursor.execute("""
            UPDATE invoices 
            SET customer_name = TRIM(customer_name)
            WHERE customer_name != TRIM(customer_name)
        """)
        
        # 2. إصلاح بيانات الفواتير
        print("📄 إصلاح بيانات الفواتير...")
        
        # إصلاح العمود description إذا كان يحتوي على timestamp
        cursor.execute("""
            UPDATE invoices 
            SET description = CASE
                WHEN description REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2}' THEN 'خدمة عامة'
                ELSE description
            END
            WHERE description REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2}'
        """)
        
        # 3. إزالة الفواتير المكررة
        print("🗑️ إزالة الفواتير المكررة...")
        
        cursor.execute("""
            CREATE TEMPORARY TABLE temp_invoices AS
            SELECT id, customer_name, date, description, gold_change, usd_change, egp_change, 
                   MIN(timestamp) as timestamp
            FROM invoices
            GROUP BY customer_name, date, description, gold_change, usd_change, egp_change
        """)
        
        cursor.execute("DELETE FROM invoices")
        cursor.execute("""
            INSERT INTO invoices (id, customer_name, date, description, gold_change, usd_change, egp_change, timestamp)
            SELECT * FROM temp_invoices
        """)
        
        # 4. تحديث معرفات الفواتير
        cursor.execute("SET @count = 0")
        cursor.execute("UPDATE invoices SET id = @count:= @count + 1")
        
        # 5. التأكد من تطابق العملاء
        print("🔗 التأكد من تطابق العملاء...")
        
        cursor.execute("""
            INSERT IGNORE INTO customers (name, phone, email, address, notes, created_date)
            SELECT DISTINCT customer_name, '', '', '', 
                   CONCAT('تم إنشاؤه تلقائياً من الفاتورة: ', description), 
                   CURDATE()
            FROM invoices
            WHERE customer_name NOT IN (SELECT name FROM customers)
        """)
        
        # 6. إحصائيات النتائج
        cursor.execute("SELECT COUNT(*) FROM customers")
        customers_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM invoices")
        invoices_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT customer_name) FROM invoices")
        unique_customers = cursor.fetchone()[0]
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ تم إصلاح البيانات بنجاح!")
        print(f"📊 الإحصائيات النهائية:")
        print(f"   - العملاء: {customers_count}")
        print(f"   - الفواتير: {invoices_count}")
        print(f"   - العملاء الفريدون: {unique_customers}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    fix_database_data()