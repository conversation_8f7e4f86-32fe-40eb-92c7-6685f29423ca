"""
نظام إدارة فواتير الورشة - Crestal Diamond Workshop Invoice System
الملف الرئيسي المنظم - Main Application File

هذا الملف الرئيسي الجديد يستخدم البنية المنظمة مع فصل الصفحات
"""

import streamlit as st
from datetime import datetime
from src.core.database import DatabaseManager
from src.pages import (
    invoice_creation,
    invoice_list,
    customer_accounts,
    excel_analysis,
    reports,
    settings,
    ai_assistant
)

# --- الثوابت ---
PAGE_CREATE_INVOICE = "📄 إنشاء فاتورة جديدة"
PAGE_VIEW_INVOICES = "📊 عرض الفواتير المحفوظة"
PAGE_CUSTOMER_ACCOUNTS = "👥 حسابات العملاء"
PAGE_EXCEL_ANALYSIS = "🔍 تحليل ملفات Excel"
PAGE_STATISTICS = "📈 إحصائيات وتقارير"
PAGE_SETTINGS = "⚙️ الإعدادات"
PAGE_AI_ASSISTANT = "🤖 كريستال - المساعد الذكي"

# --- الإعدادات الأولية ---
st.set_page_config(
    layout="wide",
    page_title="نظام فواتير الورشة",
    page_icon="💎"
)

# تهيئة مدير قاعدة البيانات


@st.cache_resource
def init_database():
    """تهيئة مدير قاعدة البيانات"""
    return DatabaseManager()


db_manager = init_database()

# --- الشريط الجانبي للتنقل ---
st.sidebar.title("💎 نظام إدارة الورشة")
st.sidebar.markdown("---")

# قائمة التنقل
page = st.sidebar.selectbox(
    "اختر الصفحة:",
    [
        PAGE_AI_ASSISTANT,
        PAGE_CREATE_INVOICE,
        PAGE_VIEW_INVOICES,
        PAGE_CUSTOMER_ACCOUNTS,
        PAGE_EXCEL_ANALYSIS,
        PAGE_STATISTICS,
        PAGE_SETTINGS
    ]
)

# --- توجيه الصفحات ---
if page == PAGE_AI_ASSISTANT:
    ai_assistant.show_page(db_manager)

elif page == PAGE_CREATE_INVOICE:
    invoice_creation.show_page(db_manager)

elif page == PAGE_VIEW_INVOICES:
    invoice_list.show_page(db_manager)

elif page == PAGE_CUSTOMER_ACCOUNTS:
    customer_accounts.show_page(db_manager)

elif page == PAGE_EXCEL_ANALYSIS:
    excel_analysis.show_page(db_manager)

elif page == PAGE_STATISTICS:
    reports.show_page(db_manager)

elif page == PAGE_SETTINGS:
    settings.show_page(db_manager)

# تذييل الصفحة
st.sidebar.markdown("---")
st.sidebar.markdown("💎 **Crestal Diamond**")
st.sidebar.markdown("🔧 الإصدار 3.0")
st.sidebar.markdown(f"📅 {datetime.now().strftime('%Y-%m-%d')}")
