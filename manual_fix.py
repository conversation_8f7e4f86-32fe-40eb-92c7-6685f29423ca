#!/usr/bin/env python3
"""
سكريپت بديل لإصلاح البيانات يدوياً
"""

import pandas as pd

def manual_fix_csv_files():
    """إصلاح ملفات CSV يدوياً"""
    
    # إصلاح ملف العملاء
    customers_data = {
        'name': ['وفاء', 'عمرو', 'عاشور'],
        'phone': ['', '', ''],
        'email': ['', '', ''],
        'address': ['', '', ''],
        'notes': [
            'تم إنشاؤه تلقائياً من الفاتورة: كولية',
            'تم إنشاؤه تلقائياً من الفاتورة: خاتم', 
            'تم إنشاؤه تلقائياً من الفاتورة: خاتم'
        ],
        'created_date': ['2025-07-03', '2025-07-03', '2025-07-03']
    }
    
    customers_df = pd.DataFrame(customers_data)
    customers_df.to_csv('customers_clean.csv', index=False, encoding='utf-8-sig')
    
    # إصلاح ملف الفواتير (إزالة المكررات)
    invoices_data = {
        'customer_name': ['وفاء', 'وفاء', 'عمرو', 'عاشور'],
        'date': ['2025-07-03', '2025-07-03', '2025-07-03', '2025-07-03'],
        'description': ['كولية', 'كولية', 'خاتم', 'خاتم'],
        'gold_change': [-4.9, -5.0, -5.0, -5.0],
        'usd_change': [49, 50, 50, 50],
        'egp_change': [380.0, 380.0, 400.0, 400.0],
        'timestamp': [
            '2025-07-03 00:41:36',
            '2025-07-03 01:21:41', 
            '2025-07-03 01:36:44',
            '2025-07-03 01:54:15'
        ]
    }
    
    invoices_df = pd.DataFrame(invoices_data)
    invoices_df.to_csv('invoices_clean.csv', index=False, encoding='utf-8-sig')
    
    print("✅ تم إنشاء الملفات المصححة:")
    print("   - customers_clean.csv")
    print("   - invoices_clean.csv")

if __name__ == "__main__":
    manual_fix_csv_files()