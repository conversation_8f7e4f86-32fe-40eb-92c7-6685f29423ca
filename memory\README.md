# 🧠 نظام الذاكرة والذكاء الاصطناعي - Memory & AI System

## 📋 نظرة عامة

نظام ذكاء اصطناعي متطور مصمم خصيصاً لتطبيق إدارة فواتير ورشة الماس. يوفر النظام ذاكرة ذكية وتحليلات متقدمة ومساعدة في اتخاذ القرارات.

## 🎯 الميزات الرئيسية

### 🧠 الذكاء الاصطناعي الرئيسي
- **كريستال**: الوكيل الذكي الرئيسي الذي يفهم اللغة العربية
- معالجة طبيعية للنصوص والاستفسارات
- تعلم من التفاعلات وتحسين الأداء
- تقديم اقتراحات ذكية ومخصصة

### 💾 نظام الذاكرة المتقدم
- تخزين ذكي للبيانات والتفاعلات
- ذاكرة العملاء وتفضيلاتهم
- أنماط السلوك والاتجاهات
- تنظيف تلقائي للبيانات المنتهية الصلاحية

### 👥 مستشار العملاء الذكي
- توصيات مخصصة لكل عميل
- تحليل رضا العملاء
- اقتراحات البيع الإضافي
- تحديد مراحل دورة حياة العميل

### 📊 محلل البيانات الذكي
- تحليل اتجاهات المبيعات
- تحليل سلوك العملاء
- التنبؤ بالطلب المستقبلي
- تقارير الأداء التلقائية

### 📦 مدير المخزون الذكي
- مراقبة مستويات المخزون
- التنبؤ بالاحتياجات
- اقتراحات إعادة الطلب
- تحسين مستويات التخزين

### 💰 مساعد التسعير الذكي
- حساب أسعار الذهب والأحجار
- اقتراحات التسعير الأمثل
- حساب الخصومات التلقائي
- تحليل أداء التسعير

## 🏗️ البنية التقنية

```
memory/
├── core/                   # النواة الأساسية
│   ├── memory_manager.py   # مدير الذاكرة
│   └── ai_agent.py        # الوكيل الذكي الرئيسي
├── agents/                # الوكلاء المتخصصون
│   ├── data_analyst.py    # محلل البيانات
│   ├── customer_advisor.py # مستشار العملاء
│   ├── inventory_manager.py # مدير المخزون
│   └── pricing_assistant.py # مساعد التسعير
├── storage/               # نظام التخزين
│   └── memory.db         # قاعدة بيانات الذاكرة
├── api/                  # واجهات البرمجة
│   └── ai_interface.py   # الواجهة الرئيسية
└── README.md            # هذا الملف
```

## 🚀 كيفية الاستخدام

### 1. التهيئة الأساسية

```python
from memory.api.ai_interface import ai_interface

# التحدث مع الذكاء الاصطناعي
response = ai_interface.chat_with_ai("ما هو سعر الذهب اليوم؟")
print(response['message'])
```

### 2. الحصول على توصيات العملاء

```python
# توصيات مخصصة لعميل
recommendations = ai_interface.get_recommendations(
    'customer', 
    target_id=123,
    context={'occasion': 'زفاف', 'budget': 50000}
)
```

### 3. تحليل البيانات

```python
# تحليل اتجاهات المبيعات
insights = ai_interface.get_smart_insights('sales', sales_data)
print(insights['insights'])
```

### 4. إدارة المخزون

```python
# تحليل حالة المخزون
inventory_analysis = ai_interface.get_smart_insights('inventory', inventory_data)
print(inventory_analysis['recommendations'])
```

## 🎨 التكامل مع Streamlit

### إضافة المحادثة الذكية

```python
import streamlit as st
from memory.api.ai_interface import ai_interface

# واجهة المحادثة
if 'messages' not in st.session_state:
    st.session_state.messages = []

# عرض المحادثات السابقة
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# مربع إدخال المستخدم
if prompt := st.chat_input("اسأل كريستال..."):
    # إضافة رسالة المستخدم
    st.session_state.messages.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.markdown(prompt)
    
    # الحصول على رد الذكاء الاصطناعي
    response = ai_interface.chat_with_ai(prompt)
    
    # إضافة رد الذكاء الاصطناعي
    st.session_state.messages.append({"role": "assistant", "content": response['message']})
    with st.chat_message("assistant"):
        st.markdown(response['message'])
```

### لوحة تحكم الذكاء الاصطناعي

```python
# عرض إحصائيات الذكاء الاصطناعي
dashboard_data = ai_interface.get_ai_dashboard_data()

col1, col2, col3 = st.columns(3)
with col1:
    st.metric("إجمالي الذكريات", dashboard_data['memory_stats']['total_memories'])
with col2:
    st.metric("ذكريات العملاء", dashboard_data['memory_stats']['customer_memories'])
with col3:
    st.metric("أنماط السلوك", dashboard_data['memory_stats']['behavior_patterns'])
```

## 🔧 الإعدادات والتخصيص

### تخصيص شخصية الذكاء الاصطناعي

```python
# في ملف ai_agent.py
self.personality = {
    'name': 'كريستال',  # يمكن تغيير الاسم
    'role': 'مساعد ذكي لورشة الماس',
    'traits': ['مفيد', 'ودود', 'خبير في المجوهرات'],
    'communication_style': 'مهني وودود'
}
```

### إضافة قواعد تسعير جديدة

```python
# في ملف pricing_assistant.py
self.pricing_rules['new_category'] = {
    'base_price': 1000,
    'margin': 0.20,
    'discount_rules': {...}
}
```

## 📊 قاعدة البيانات

### جداول الذاكرة

1. **memories**: الذكريات العامة
2. **customer_memories**: ذكريات العملاء
3. **behavior_patterns**: أنماط السلوك
4. **interactions**: تفاعلات المستخدم

### مثال على استعلام

```sql
-- الحصول على أكثر الذكريات وصولاً
SELECT category, key_data, access_count 
FROM memories 
ORDER BY access_count DESC 
LIMIT 10;
```

## 🔍 المراقبة والتحليل

### مراقبة الأداء

```python
# الحصول على إحصائيات النظام
stats = ai_interface.memory_manager.get_memory_stats()
print(f"إجمالي الذكريات: {stats['total_memories']}")
print(f"أكثر الذكريات وصولاً: {stats['most_accessed']}")
```

### تصدير البيانات

```python
# تصدير الرؤى الذكية
insights_json = ai_interface.export_ai_insights('json')
with open('ai_insights.json', 'w', encoding='utf-8') as f:
    f.write(insights_json)
```

## 🛠️ الصيانة والتحديث

### تنظيف الذاكرة

```python
# تنظيف الذكريات المنتهية الصلاحية
deleted_count = ai_interface.memory_manager.cleanup_expired_memories()
print(f"تم حذف {deleted_count} ذكرى منتهية الصلاحية")
```

### النسخ الاحتياطي

```python
import shutil
from datetime import datetime

# نسخ احتياطي لقاعدة بيانات الذاكرة
backup_name = f"memory_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
shutil.copy('memory/storage/memory.db', f'backups/{backup_name}')
```

## 🔮 التطوير المستقبلي

### ميزات مخططة
- [ ] تحليل المشاعر المتقدم
- [ ] التعلم العميق للتنبؤات
- [ ] واجهة صوتية
- [ ] تكامل مع أنظمة خارجية
- [ ] تحليل الصور للمجوهرات

### تحسينات مقترحة
- [ ] تحسين خوارزميات التوصية
- [ ] إضافة المزيد من اللغات
- [ ] تحسين واجهة المستخدم
- [ ] تطوير تطبيق الهاتف المحمول

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات السجلات في `memory/logs/`
3. اتصل بفريق التطوير

## 📄 الترخيص

هذا النظام مطور خصيصاً لورشة الماس ومحمي بحقوق الطبع والنشر.

---

**تم تطوير هذا النظام بحب ❤️ لورشة الماس الكريستالية**
