"""
مستشار العملاء الذكي - Smart Customer Advisor
يقدم نصائح وتوصيات مخصصة للعملاء بناءً على تاريخهم وتفضيلاتهم
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from ..core.memory_manager import MemoryManager

class CustomerAdvisor:
    """مستشار العملاء الذكي"""
    
    def __init__(self, memory_manager: MemoryManager):
        """تهيئة مستشار العملاء"""
        self.memory = memory_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل قواعد التوصيات
        self._load_recommendation_rules()
    
    def _load_recommendation_rules(self):
        """تحميل قواعد التوصيات"""
        self.recommendation_rules = {
            'occasion_based': {
                'زفاف': {
                    'products': ['طقم ذهب كامل', 'خاتم زفاف', 'أقراط ماس'],
                    'karat_preference': [21, 18],
                    'budget_range': [20000, 100000],
                    'style': ['كلاسيكي', 'فاخر']
                },
                'خطوبة': {
                    'products': ['خاتم خطوبة', 'سوار', 'قلادة'],
                    'karat_preference': [18, 21],
                    'budget_range': [10000, 50000],
                    'style': ['عصري', 'رومانسي']
                },
                'هدية': {
                    'products': ['سوار', 'قلادة', 'أقراط'],
                    'karat_preference': [18],
                    'budget_range': [5000, 25000],
                    'style': ['عصري', 'بسيط']
                }
            },
            'age_based': {
                'شباب': ['تصاميم عصرية', 'قطع بسيطة', 'ألوان متنوعة'],
                'متوسط': ['تصاميم كلاسيكية', 'قطع متوسطة', 'جودة عالية'],
                'كبار': ['تصاميم تقليدية', 'قطع فاخرة', 'ذهب عالي العيار']
            },
            'budget_based': {
                'اقتصادي': {
                    'max_budget': 10000,
                    'recommendations': ['ذهب عيار 18', 'قطع بسيطة', 'تصاميم كلاسيكية']
                },
                'متوسط': {
                    'max_budget': 30000,
                    'recommendations': ['ذهب عيار 21', 'أحجار طبيعية', 'تصاميم متنوعة']
                },
                'فاخر': {
                    'max_budget': 100000,
                    'recommendations': ['ذهب عيار 21-22', 'ماس وأحجار كريمة', 'تصاميم حصرية']
                }
            }
        }
    
    def get_personalized_recommendations(self, customer_id: int, 
                                       context: Dict = None) -> Dict:
        """الحصول على توصيات مخصصة للعميل"""
        try:
            # استرجاع تاريخ العميل
            customer_history = self.memory.get_customer_memories(customer_id)
            
            # تحليل تفضيلات العميل
            preferences = self._analyze_customer_preferences(customer_history)
            
            # إنشاء التوصيات
            recommendations = self._generate_recommendations(preferences, context)
            
            # تخزين التوصيات في الذاكرة
            self.memory.store_customer_memory(
                customer_id, 'recommendations', 
                str(recommendations), context=str(context or {})
            )
            
            return {
                'customer_id': customer_id,
                'recommendations': recommendations,
                'preferences': preferences,
                'confidence_score': self._calculate_confidence(customer_history)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التوصيات للعميل {customer_id}: {e}")
            return {'error': str(e)}
    
    def _analyze_customer_preferences(self, customer_history: List[Dict]) -> Dict:
        """تحليل تفضيلات العميل من تاريخه"""
        preferences = {
            'preferred_products': [],
            'preferred_karat': None,
            'budget_range': None,
            'style_preference': None,
            'purchase_frequency': 'منخفض',
            'seasonal_patterns': []
        }
        
        if not customer_history:
            return preferences
        
        # تحليل المنتجات المفضلة
        product_mentions = {}
        karat_mentions = {}
        budget_values = []
        
        for memory in customer_history:
            content = memory['content'].lower()
            
            # البحث عن منتجات
            products = ['خاتم', 'سوار', 'قلادة', 'أقراط', 'طقم']
            for product in products:
                if product in content:
                    product_mentions[product] = product_mentions.get(product, 0) + 1
            
            # البحث عن عيارات
            if 'عيار' in content:
                for karat in [18, 21, 22, 24]:
                    if str(karat) in content:
                        karat_mentions[karat] = karat_mentions.get(karat, 0) + 1
            
            # البحث عن أرقام (ميزانية محتملة)
            import re
            numbers = re.findall(r'\d+', content)
            for num in numbers:
                if 1000 <= int(num) <= 200000:  # نطاق معقول للأسعار
                    budget_values.append(int(num))
        
        # تحديد التفضيلات
        if product_mentions:
            preferences['preferred_products'] = sorted(
                product_mentions.items(), key=lambda x: x[1], reverse=True
            )[:3]
        
        if karat_mentions:
            preferences['preferred_karat'] = max(karat_mentions, key=karat_mentions.get)
        
        if budget_values:
            avg_budget = sum(budget_values) / len(budget_values)
            if avg_budget < 10000:
                preferences['budget_range'] = 'اقتصادي'
            elif avg_budget < 30000:
                preferences['budget_range'] = 'متوسط'
            else:
                preferences['budget_range'] = 'فاخر'
        
        # تحديد تكرار الشراء
        if len(customer_history) > 5:
            preferences['purchase_frequency'] = 'عالي'
        elif len(customer_history) > 2:
            preferences['purchase_frequency'] = 'متوسط'
        
        return preferences
    
    def _generate_recommendations(self, preferences: Dict, context: Dict = None) -> List[Dict]:
        """إنشاء التوصيات بناءً على التفضيلات"""
        recommendations = []
        
        # توصيات بناءً على المناسبة
        if context and 'occasion' in context:
            occasion = context['occasion']
            if occasion in self.recommendation_rules['occasion_based']:
                occasion_rules = self.recommendation_rules['occasion_based'][occasion]
                
                for product in occasion_rules['products']:
                    recommendation = {
                        'type': 'product',
                        'item': product,
                        'reason': f'مناسب لمناسبة {occasion}',
                        'priority': 'عالي',
                        'estimated_price_range': occasion_rules['budget_range']
                    }
                    recommendations.append(recommendation)
        
        # توصيات بناءً على التفضيلات السابقة
        if preferences['preferred_products']:
            for product, frequency in preferences['preferred_products']:
                recommendation = {
                    'type': 'product',
                    'item': product,
                    'reason': f'منتج مفضل سابقاً (تم ذكره {frequency} مرات)',
                    'priority': 'متوسط'
                }
                recommendations.append(recommendation)
        
        # توصيات العيار
        if preferences['preferred_karat']:
            recommendation = {
                'type': 'specification',
                'item': f"ذهب عيار {preferences['preferred_karat']}",
                'reason': 'العيار المفضل حسب تاريخ المشتريات',
                'priority': 'متوسط'
            }
            recommendations.append(recommendation)
        
        # توصيات الميزانية
        if preferences['budget_range']:
            budget_rules = self.recommendation_rules['budget_based'][preferences['budget_range']]
            for rec in budget_rules['recommendations']:
                recommendation = {
                    'type': 'style',
                    'item': rec,
                    'reason': f'مناسب للميزانية {preferences["budget_range"]}',
                    'priority': 'منخفض'
                }
                recommendations.append(recommendation)
        
        # توصيات موسمية
        current_month = datetime.now().month
        if current_month in [11, 12, 1]:  # موسم الشتاء/الأعياد
            recommendation = {
                'type': 'seasonal',
                'item': 'مجوهرات الأعياد والمناسبات',
                'reason': 'موسم الأعياد والاحتفالات',
                'priority': 'عالي'
            }
            recommendations.append(recommendation)
        
        return recommendations[:5]  # أفضل 5 توصيات
    
    def _calculate_confidence(self, customer_history: List[Dict]) -> float:
        """حساب مستوى الثقة في التوصيات"""
        if not customer_history:
            return 0.3  # ثقة منخفضة للعملاء الجدد
        
        # عوامل الثقة
        history_factor = min(len(customer_history) / 10, 1.0)  # كلما زاد التاريخ زادت الثقة
        recency_factor = 1.0  # يمكن تحسينه بناءً على حداثة التفاعلات
        
        confidence = (history_factor * 0.7) + (recency_factor * 0.3)
        return round(confidence, 2)
    
    def suggest_upselling_opportunities(self, customer_id: int, 
                                      current_order: Dict) -> List[Dict]:
        """اقتراح فرص البيع الإضافي"""
        try:
            suggestions = []
            
            # تحليل الطلب الحالي
            current_value = current_order.get('total_amount', 0)
            current_items = current_order.get('items', [])
            
            # اقتراحات بناءً على قيمة الطلب
            if current_value > 20000:
                suggestions.append({
                    'type': 'service',
                    'item': 'خدمة التنظيف والصيانة السنوية',
                    'reason': 'للحفاظ على استثمارك في المجوهرات',
                    'estimated_value': 500
                })
            
            # اقتراحات المنتجات المكملة
            if any('خاتم' in str(item) for item in current_items):
                suggestions.append({
                    'type': 'product',
                    'item': 'سوار مطابق',
                    'reason': 'لتكوين طقم متناسق',
                    'estimated_value': current_value * 0.6
                })
            
            if any('قلادة' in str(item) for item in current_items):
                suggestions.append({
                    'type': 'product',
                    'item': 'أقراط مطابقة',
                    'reason': 'لإكمال الإطلالة',
                    'estimated_value': current_value * 0.4
                })
            
            # تخزين الاقتراحات
            self.memory.store_customer_memory(
                customer_id, 'upselling_suggestions',
                str(suggestions), context=str(current_order)
            )
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"خطأ في اقتراح فرص البيع الإضافي: {e}")
            return []
    
    def analyze_customer_satisfaction(self, customer_id: int) -> Dict:
        """تحليل رضا العميل"""
        try:
            # استرجاع تفاعلات العميل
            customer_memories = self.memory.get_customer_memories(customer_id)
            
            satisfaction_analysis = {
                'overall_score': 0.0,
                'factors': {
                    'positive': [],
                    'negative': [],
                    'neutral': []
                },
                'recommendations': []
            }
            
            if not customer_memories:
                return satisfaction_analysis
            
            # تحليل المشاعر من التفاعلات
            sentiment_scores = []
            for memory in customer_memories:
                if memory['sentiment'] is not None:
                    sentiment_scores.append(memory['sentiment'])
                    
                    # تصنيف التعليقات
                    if memory['sentiment'] > 0.3:
                        satisfaction_analysis['factors']['positive'].append(memory['content'][:100])
                    elif memory['sentiment'] < -0.3:
                        satisfaction_analysis['factors']['negative'].append(memory['content'][:100])
                    else:
                        satisfaction_analysis['factors']['neutral'].append(memory['content'][:100])
            
            # حساب النتيجة الإجمالية
            if sentiment_scores:
                satisfaction_analysis['overall_score'] = sum(sentiment_scores) / len(sentiment_scores)
            
            # إنشاء التوصيات
            if satisfaction_analysis['overall_score'] < 0:
                satisfaction_analysis['recommendations'].append("التواصل مع العميل لحل أي مشاكل")
                satisfaction_analysis['recommendations'].append("تقديم خصم أو خدمة مجانية")
            elif satisfaction_analysis['overall_score'] > 0.5:
                satisfaction_analysis['recommendations'].append("طلب مراجعة إيجابية")
                satisfaction_analysis['recommendations'].append("اقتراح برنامج الإحالة")
            
            return satisfaction_analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل رضا العميل: {e}")
            return {'error': str(e)}
    
    def get_customer_lifecycle_stage(self, customer_id: int) -> Dict:
        """تحديد مرحلة دورة حياة العميل"""
        try:
            customer_memories = self.memory.get_customer_memories(customer_id)
            
            # تحليل النشاط
            total_interactions = len(customer_memories)
            recent_interactions = len([
                m for m in customer_memories 
                if (datetime.now() - datetime.fromisoformat(m['created_at'])).days <= 30
            ])
            
            # تحديد المرحلة
            if total_interactions == 0:
                stage = 'جديد'
                description = 'عميل جديد لم يتفاعل بعد'
            elif total_interactions <= 2:
                stage = 'مبتدئ'
                description = 'عميل في بداية رحلته معنا'
            elif recent_interactions > 0:
                stage = 'نشط'
                description = 'عميل نشط ومتفاعل'
            elif total_interactions > 5:
                stage = 'مخلص'
                description = 'عميل مخلص مع تاريخ طويل'
            else:
                stage = 'خامل'
                description = 'عميل لم يتفاعل مؤخراً'
            
            return {
                'stage': stage,
                'description': description,
                'total_interactions': total_interactions,
                'recent_interactions': recent_interactions,
                'recommendations': self._get_stage_recommendations(stage)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديد مرحلة دورة حياة العميل: {e}")
            return {'error': str(e)}
    
    def _get_stage_recommendations(self, stage: str) -> List[str]:
        """الحصول على توصيات حسب مرحلة العميل"""
        stage_recommendations = {
            'جديد': [
                'إرسال رسالة ترحيب',
                'تقديم خصم للمرة الأولى',
                'شرح الخدمات المتاحة'
            ],
            'مبتدئ': [
                'متابعة تجربة الشراء الأولى',
                'تقديم نصائح للعناية بالمجوهرات',
                'اقتراح منتجات مكملة'
            ],
            'نشط': [
                'تقديم عروض حصرية',
                'دعوة لبرنامج الولاء',
                'طلب مراجعة أو إحالة'
            ],
            'مخلص': [
                'تقديم خدمات VIP',
                'إشراك في إطلاق منتجات جديدة',
                'تقديم خصومات خاصة'
            ],
            'خامل': [
                'إرسال عرض استرداد',
                'الاستفسار عن سبب عدم النشاط',
                'تقديم خصم كبير للعودة'
            ]
        }
        
        return stage_recommendations.get(stage, [])
