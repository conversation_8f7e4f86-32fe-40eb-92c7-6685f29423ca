"""
محلل البيانات الذكي - Smart Data Analyst
يحلل بيانات المبيعات والعملاء ويقدم رؤى ذكية
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from ..core.memory_manager import MemoryManager

class DataAnalyst:
    """محلل البيانات الذكي"""
    
    def __init__(self, memory_manager: MemoryManager):
        """تهيئة محلل البيانات"""
        self.memory = memory_manager
        self.logger = logging.getLogger(__name__)
    
    def analyze_sales_trends(self, data: List[Dict]) -> Dict:
        """تحليل اتجاهات المبيعات"""
        try:
            if not data:
                return {'error': 'لا توجد بيانات للتحليل'}
            
            df = pd.DataFrame(data)
            
            analysis = {
                'total_sales': df['total_amount'].sum() if 'total_amount' in df.columns else 0,
                'average_order': df['total_amount'].mean() if 'total_amount' in df.columns else 0,
                'total_orders': len(df),
                'trends': {},
                'insights': []
            }
            
            # تحليل الاتجاهات الزمنية
            if 'invoice_date' in df.columns:
                df['invoice_date'] = pd.to_datetime(df['invoice_date'])
                df['month'] = df['invoice_date'].dt.month
                df['day_of_week'] = df['invoice_date'].dt.dayofweek
                
                # المبيعات الشهرية
                monthly_sales = df.groupby('month')['total_amount'].sum()
                analysis['trends']['monthly'] = monthly_sales.to_dict()
                
                # أفضل أيام الأسبوع
                daily_sales = df.groupby('day_of_week')['total_amount'].mean()
                analysis['trends']['daily'] = daily_sales.to_dict()
            
            # تحليل المنتجات
            if 'gold_weight' in df.columns:
                gold_analysis = self._analyze_gold_sales(df)
                analysis['gold_insights'] = gold_analysis
            
            if 'stone_weight' in df.columns:
                stone_analysis = self._analyze_stone_sales(df)
                analysis['stone_insights'] = stone_analysis
            
            # إنشاء الرؤى
            analysis['insights'] = self._generate_insights(analysis)
            
            # تخزين النتائج في الذاكرة
            self.memory.store_memory(
                'analytics', 'sales_trends', analysis,
                importance=1.5, expires_in_days=7
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل اتجاهات المبيعات: {e}")
            return {'error': str(e)}
    
    def _analyze_gold_sales(self, df: pd.DataFrame) -> Dict:
        """تحليل مبيعات الذهب"""
        gold_data = df[df['gold_weight'] > 0] if 'gold_weight' in df.columns else pd.DataFrame()
        
        if gold_data.empty:
            return {'message': 'لا توجد مبيعات ذهب'}
        
        analysis = {
            'total_weight': gold_data['gold_weight'].sum(),
            'average_weight': gold_data['gold_weight'].mean(),
            'total_value': gold_data['gold_total'].sum() if 'gold_total' in gold_data.columns else 0,
            'popular_karats': {}
        }
        
        # تحليل العيارات الشائعة
        if 'gold_karat' in gold_data.columns:
            karat_counts = gold_data['gold_karat'].value_counts()
            analysis['popular_karats'] = karat_counts.to_dict()
        
        return analysis
    
    def _analyze_stone_sales(self, df: pd.DataFrame) -> Dict:
        """تحليل مبيعات الأحجار"""
        stone_data = df[df['stone_weight'] > 0] if 'stone_weight' in df.columns else pd.DataFrame()
        
        if stone_data.empty:
            return {'message': 'لا توجد مبيعات أحجار'}
        
        analysis = {
            'total_weight': stone_data['stone_weight'].sum(),
            'average_weight': stone_data['stone_weight'].mean(),
            'total_value': stone_data['stone_total'].sum() if 'stone_total' in stone_data.columns else 0
        }
        
        return analysis
    
    def _generate_insights(self, analysis: Dict) -> List[str]:
        """إنشاء رؤى ذكية من التحليل"""
        insights = []
        
        # رؤى المبيعات
        if analysis['total_orders'] > 0:
            avg_order = analysis['average_order']
            if avg_order > 10000:
                insights.append("متوسط قيمة الطلب مرتفع - عملاء ذوو قوة شرائية جيدة")
            elif avg_order < 5000:
                insights.append("متوسط قيمة الطلب منخفض - فرصة لتطوير منتجات متوسطة السعر")
        
        # رؤى الاتجاهات الشهرية
        if 'monthly' in analysis.get('trends', {}):
            monthly_data = analysis['trends']['monthly']
            best_month = max(monthly_data, key=monthly_data.get)
            insights.append(f"أفضل شهر للمبيعات: الشهر {best_month}")
        
        # رؤى الذهب
        if 'gold_insights' in analysis:
            gold_data = analysis['gold_insights']
            if 'popular_karats' in gold_data and gold_data['popular_karats']:
                popular_karat = max(gold_data['popular_karats'], key=gold_data['popular_karats'].get)
                insights.append(f"العيار الأكثر طلباً: {popular_karat} قيراط")
        
        return insights
    
    def analyze_customer_behavior(self, customer_data: List[Dict]) -> Dict:
        """تحليل سلوك العملاء"""
        try:
            if not customer_data:
                return {'error': 'لا توجد بيانات عملاء للتحليل'}
            
            df = pd.DataFrame(customer_data)
            
            analysis = {
                'total_customers': len(df),
                'segments': {},
                'patterns': {},
                'recommendations': []
            }
            
            # تجميع العملاء حسب قيمة المشتريات
            if 'total_spent' in df.columns:
                df['customer_segment'] = pd.cut(
                    df['total_spent'], 
                    bins=[0, 5000, 15000, 50000, float('inf')],
                    labels=['اقتصادي', 'متوسط', 'فاخر', 'VIP']
                )
                
                segment_counts = df['customer_segment'].value_counts()
                analysis['segments'] = segment_counts.to_dict()
            
            # تحليل أنماط الشراء
            if 'last_purchase' in df.columns:
                df['last_purchase'] = pd.to_datetime(df['last_purchase'])
                df['days_since_purchase'] = (datetime.now() - df['last_purchase']).dt.days
                
                # العملاء النشطون (آخر شراء خلال 30 يوم)
                active_customers = len(df[df['days_since_purchase'] <= 30])
                analysis['patterns']['active_customers'] = active_customers
                
                # العملاء المعرضون للفقدان (آخر شراء أكثر من 90 يوم)
                at_risk_customers = len(df[df['days_since_purchase'] > 90])
                analysis['patterns']['at_risk_customers'] = at_risk_customers
            
            # إنشاء التوصيات
            analysis['recommendations'] = self._generate_customer_recommendations(analysis)
            
            # تخزين النتائج
            self.memory.store_memory(
                'analytics', 'customer_behavior', analysis,
                importance=1.5, expires_in_days=7
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل سلوك العملاء: {e}")
            return {'error': str(e)}
    
    def _generate_customer_recommendations(self, analysis: Dict) -> List[str]:
        """إنشاء توصيات بناءً على تحليل العملاء"""
        recommendations = []
        
        # توصيات بناءً على الشرائح
        if 'segments' in analysis:
            segments = analysis['segments']
            
            if segments.get('VIP', 0) > 0:
                recommendations.append("إنشاء برنامج ولاء خاص لعملاء VIP")
            
            if segments.get('اقتصادي', 0) > segments.get('فاخر', 0):
                recommendations.append("تطوير مجموعة منتجات اقتصادية أكثر")
        
        # توصيات بناءً على الأنماط
        if 'patterns' in analysis:
            patterns = analysis['patterns']
            
            if patterns.get('at_risk_customers', 0) > 0:
                recommendations.append("إطلاق حملة استرداد للعملاء المعرضين للفقدان")
            
            if patterns.get('active_customers', 0) > 10:
                recommendations.append("تطوير برنامج إحالة للعملاء النشطين")
        
        return recommendations
    
    def predict_demand(self, historical_data: List[Dict], days_ahead: int = 30) -> Dict:
        """التنبؤ بالطلب المستقبلي"""
        try:
            if not historical_data:
                return {'error': 'لا توجد بيانات تاريخية للتنبؤ'}
            
            df = pd.DataFrame(historical_data)
            
            # تنبؤ بسيط بناءً على المتوسطات
            prediction = {
                'period': f'{days_ahead} يوم قادم',
                'predicted_orders': 0,
                'predicted_revenue': 0,
                'confidence': 'متوسط',
                'factors': []
            }
            
            if 'invoice_date' in df.columns and 'total_amount' in df.columns:
                df['invoice_date'] = pd.to_datetime(df['invoice_date'])
                
                # حساب المتوسط اليومي
                daily_avg_orders = len(df) / max(1, (df['invoice_date'].max() - df['invoice_date'].min()).days)
                daily_avg_revenue = df['total_amount'].sum() / max(1, (df['invoice_date'].max() - df['invoice_date'].min()).days)
                
                prediction['predicted_orders'] = int(daily_avg_orders * days_ahead)
                prediction['predicted_revenue'] = daily_avg_revenue * days_ahead
                
                # عوامل التأثير
                prediction['factors'] = [
                    'الاتجاه التاريخي للمبيعات',
                    'متوسط الطلبات اليومية',
                    'الموسمية (إن وجدت)'
                ]
            
            # تخزين التنبؤ
            self.memory.store_memory(
                'analytics', 'demand_prediction', prediction,
                importance=1.0, expires_in_days=3
            )
            
            return prediction
            
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ بالطلب: {e}")
            return {'error': str(e)}
    
    def generate_performance_report(self, period_data: Dict) -> Dict:
        """إنشاء تقرير الأداء"""
        try:
            report = {
                'period': period_data.get('period', 'غير محدد'),
                'summary': {},
                'highlights': [],
                'areas_for_improvement': [],
                'recommendations': []
            }
            
            # ملخص الأداء
            report['summary'] = {
                'total_revenue': period_data.get('total_revenue', 0),
                'total_orders': period_data.get('total_orders', 0),
                'average_order_value': period_data.get('average_order_value', 0),
                'customer_count': period_data.get('customer_count', 0)
            }
            
            # النقاط المضيئة
            if report['summary']['total_revenue'] > 100000:
                report['highlights'].append("تحقيق إيرادات ممتازة تزيد عن 100,000 جنيه")
            
            if report['summary']['average_order_value'] > 15000:
                report['highlights'].append("متوسط قيمة طلب مرتفع يدل على جودة العملاء")
            
            # مجالات التحسين
            if report['summary']['total_orders'] < 10:
                report['areas_for_improvement'].append("زيادة عدد الطلبات من خلال التسويق")
            
            # التوصيات
            report['recommendations'] = [
                "مراجعة استراتيجية التسعير",
                "تطوير خدمات جديدة",
                "تحسين تجربة العميل"
            ]
            
            return report
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الأداء: {e}")
            return {'error': str(e)}
