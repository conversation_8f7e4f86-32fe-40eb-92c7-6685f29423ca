"""
مدير المخزون الذكي - Smart Inventory Manager
يدير المخزون ويتنبأ بالاحتياجات ويقدم تحليلات ذكية
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from ..core.memory_manager import MemoryManager

class InventoryManager:
    """مدير المخزون الذكي"""
    
    def __init__(self, memory_manager: MemoryManager):
        """تهيئة مدير المخزون"""
        self.memory = memory_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل قواعد إدارة المخزون
        self._load_inventory_rules()
    
    def _load_inventory_rules(self):
        """تحميل قواعد إدارة المخزون"""
        self.inventory_rules = {
            'reorder_levels': {
                'gold_18k': {'min_stock': 100, 'max_stock': 500, 'unit': 'gram'},
                'gold_21k': {'min_stock': 150, 'max_stock': 600, 'unit': 'gram'},
                'gold_22k': {'min_stock': 50, 'max_stock': 200, 'unit': 'gram'},
                'diamonds': {'min_stock': 20, 'max_stock': 100, 'unit': 'piece'},
                'gemstones': {'min_stock': 30, 'max_stock': 150, 'unit': 'piece'}
            },
            'seasonal_factors': {
                'wedding_season': {'months': [5, 6, 9, 10], 'multiplier': 1.5},
                'holidays': {'months': [11, 12, 1], 'multiplier': 1.3},
                'ramadan': {'months': [3, 4], 'multiplier': 0.8}
            },
            'lead_times': {
                'gold': 7,  # أيام
                'diamonds': 14,
                'gemstones': 10,
                'tools': 5
            }
        }
    
    def analyze_inventory_status(self, current_inventory: Dict) -> Dict:
        """تحليل حالة المخزون الحالي"""
        try:
            analysis = {
                'overall_status': 'جيد',
                'items_status': {},
                'alerts': [],
                'recommendations': [],
                'reorder_suggestions': []
            }
            
            critical_items = 0
            low_items = 0
            
            for item_name, item_data in current_inventory.items():
                current_stock = item_data.get('quantity', 0)
                item_type = item_data.get('type', 'unknown')
                
                # الحصول على قواعد هذا النوع
                rules = self._get_item_rules(item_type)
                
                if rules:
                    min_stock = rules['min_stock']
                    max_stock = rules['max_stock']
                    
                    # تحديد حالة المخزون
                    if current_stock <= min_stock * 0.5:
                        status = 'حرج'
                        critical_items += 1
                        analysis['alerts'].append(f"مخزون {item_name} في حالة حرجة ({current_stock} {rules['unit']})")
                    elif current_stock <= min_stock:
                        status = 'منخفض'
                        low_items += 1
                        analysis['alerts'].append(f"مخزون {item_name} منخفض ({current_stock} {rules['unit']})")
                    elif current_stock >= max_stock:
                        status = 'مرتفع'
                        analysis['alerts'].append(f"مخزون {item_name} مرتفع جداً ({current_stock} {rules['unit']})")
                    else:
                        status = 'جيد'
                    
                    analysis['items_status'][item_name] = {
                        'current': current_stock,
                        'min_required': min_stock,
                        'max_capacity': max_stock,
                        'status': status,
                        'unit': rules['unit']
                    }
                    
                    # اقتراحات إعادة الطلب
                    if current_stock <= min_stock:
                        suggested_order = max_stock - current_stock
                        analysis['reorder_suggestions'].append({
                            'item': item_name,
                            'current_stock': current_stock,
                            'suggested_quantity': suggested_order,
                            'priority': 'عالي' if status == 'حرج' else 'متوسط',
                            'estimated_cost': self._estimate_cost(item_name, suggested_order)
                        })
            
            # تحديد الحالة العامة
            if critical_items > 0:
                analysis['overall_status'] = 'حرج'
            elif low_items > 2:
                analysis['overall_status'] = 'يحتاج انتباه'
            
            # إنشاء التوصيات
            analysis['recommendations'] = self._generate_inventory_recommendations(analysis)
            
            # تخزين التحليل
            self.memory.store_memory(
                'inventory', 'status_analysis', analysis,
                importance=1.5, expires_in_days=1
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل حالة المخزون: {e}")
            return {'error': str(e)}
    
    def _get_item_rules(self, item_type: str) -> Optional[Dict]:
        """الحصول على قواعد نوع المنتج"""
        type_mapping = {
            'gold': 'gold_21k',  # افتراضي
            'gold_18': 'gold_18k',
            'gold_21': 'gold_21k',
            'gold_22': 'gold_22k',
            'diamond': 'diamonds',
            'gemstone': 'gemstones'
        }
        
        mapped_type = type_mapping.get(item_type.lower(), item_type.lower())
        return self.inventory_rules['reorder_levels'].get(mapped_type)
    
    def _estimate_cost(self, item_name: str, quantity: float) -> float:
        """تقدير تكلفة الطلب"""
        # أسعار تقديرية - يمكن ربطها بقاعدة بيانات الأسعار
        estimated_prices = {
            'gold_18k': 2800,  # جنيه/جرام
            'gold_21k': 3200,
            'gold_22k': 3400,
            'diamonds': 5000,  # جنيه/قطعة
            'gemstones': 1500
        }
        
        # محاولة تحديد نوع المنتج من الاسم
        item_lower = item_name.lower()
        for item_type, price in estimated_prices.items():
            if any(keyword in item_lower for keyword in item_type.split('_')):
                return quantity * price
        
        return quantity * 1000  # سعر افتراضي
    
    def _generate_inventory_recommendations(self, analysis: Dict) -> List[str]:
        """إنشاء توصيات إدارة المخزون"""
        recommendations = []
        
        # توصيات بناءً على الحالة العامة
        if analysis['overall_status'] == 'حرج':
            recommendations.append("إجراء طلبيات عاجلة للمواد الحرجة")
            recommendations.append("مراجعة مستويات الحد الأدنى للمخزون")
        
        # توصيات بناءً على عدد التنبيهات
        if len(analysis['alerts']) > 5:
            recommendations.append("تحسين نظام إدارة المخزون")
            recommendations.append("زيادة تكرار مراجعة المخزون")
        
        # توصيات موسمية
        current_month = datetime.now().month
        for season, data in self.inventory_rules['seasonal_factors'].items():
            if current_month in data['months']:
                recommendations.append(f"زيادة المخزون للموسم: {season}")
                break
        
        # توصيات عامة
        recommendations.extend([
            "تطبيق نظام الجرد الدوري",
            "تحديث أسعار الموردين",
            "مراجعة مستويات الأمان"
        ])
        
        return recommendations[:5]
    
    def predict_demand(self, historical_sales: List[Dict], days_ahead: int = 30) -> Dict:
        """التنبؤ بالطلب على المواد"""
        try:
            prediction = {
                'period': f'{days_ahead} يوم قادم',
                'predicted_demand': {},
                'confidence_level': 'متوسط',
                'factors_considered': []
            }
            
            if not historical_sales:
                return prediction
            
            # تحليل البيانات التاريخية
            material_usage = {}
            for sale in historical_sales:
                # استخراج استخدام المواد من بيانات المبيعات
                if 'gold_weight' in sale and sale['gold_weight'] > 0:
                    gold_type = f"gold_{sale.get('gold_karat', 21)}k"
                    material_usage[gold_type] = material_usage.get(gold_type, 0) + sale['gold_weight']
                
                if 'stone_weight' in sale and sale['stone_weight'] > 0:
                    material_usage['gemstones'] = material_usage.get('gemstones', 0) + sale['stone_weight']
            
            # حساب المتوسط اليومي
            days_in_data = max(1, len(set(sale.get('invoice_date', '') for sale in historical_sales)))
            
            for material, total_used in material_usage.items():
                daily_average = total_used / days_in_data
                predicted_usage = daily_average * days_ahead
                
                # تطبيق العوامل الموسمية
                seasonal_factor = self._get_seasonal_factor()
                predicted_usage *= seasonal_factor
                
                prediction['predicted_demand'][material] = {
                    'quantity': round(predicted_usage, 2),
                    'daily_average': round(daily_average, 2),
                    'seasonal_factor': seasonal_factor
                }
            
            prediction['factors_considered'] = [
                'البيانات التاريخية للمبيعات',
                'المتوسط اليومي للاستخدام',
                'العوامل الموسمية',
                'اتجاهات السوق'
            ]
            
            # تخزين التنبؤ
            self.memory.store_memory(
                'inventory', 'demand_prediction', prediction,
                importance=1.0, expires_in_days=7
            )
            
            return prediction
            
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ بالطلب: {e}")
            return {'error': str(e)}
    
    def _get_seasonal_factor(self) -> float:
        """الحصول على العامل الموسمي الحالي"""
        current_month = datetime.now().month
        
        for season, data in self.inventory_rules['seasonal_factors'].items():
            if current_month in data['months']:
                return data['multiplier']
        
        return 1.0  # عامل محايد
    
    def optimize_inventory_levels(self, current_inventory: Dict, 
                                sales_data: List[Dict]) -> Dict:
        """تحسين مستويات المخزون"""
        try:
            optimization = {
                'current_levels': {},
                'optimized_levels': {},
                'adjustments': [],
                'expected_benefits': []
            }
            
            # تحليل معدل الدوران لكل منتج
            for item_name, item_data in current_inventory.items():
                current_stock = item_data.get('quantity', 0)
                
                # حساب معدل الاستخدام من بيانات المبيعات
                usage_rate = self._calculate_usage_rate(item_name, sales_data)
                
                # حساب المستوى الأمثل
                optimal_min = usage_rate * 7  # أسبوع من الاستخدام
                optimal_max = usage_rate * 30  # شهر من الاستخدام
                
                optimization['current_levels'][item_name] = current_stock
                optimization['optimized_levels'][item_name] = {
                    'min_stock': optimal_min,
                    'max_stock': optimal_max,
                    'recommended_stock': optimal_min * 1.5
                }
                
                # اقتراح التعديلات
                if current_stock < optimal_min:
                    adjustment = optimal_min * 1.5 - current_stock
                    optimization['adjustments'].append({
                        'item': item_name,
                        'action': 'زيادة',
                        'quantity': adjustment,
                        'reason': 'مستوى أقل من الأمثل'
                    })
                elif current_stock > optimal_max:
                    adjustment = current_stock - optimal_max
                    optimization['adjustments'].append({
                        'item': item_name,
                        'action': 'تقليل',
                        'quantity': adjustment,
                        'reason': 'مستوى أعلى من الأمثل'
                    })
            
            # الفوائد المتوقعة
            optimization['expected_benefits'] = [
                'تقليل تكاليف التخزين',
                'تحسين التدفق النقدي',
                'تقليل مخاطر نفاد المخزون',
                'زيادة كفاءة العمليات'
            ]
            
            return optimization
            
        except Exception as e:
            self.logger.error(f"خطأ في تحسين مستويات المخزون: {e}")
            return {'error': str(e)}
    
    def _calculate_usage_rate(self, item_name: str, sales_data: List[Dict]) -> float:
        """حساب معدل استخدام المنتج يومياً"""
        total_usage = 0
        days_count = 0
        
        # تحليل بيانات المبيعات لاستخراج الاستخدام
        for sale in sales_data:
            if 'gold' in item_name.lower() and 'gold_weight' in sale:
                total_usage += sale.get('gold_weight', 0)
                days_count += 1
            elif 'stone' in item_name.lower() and 'stone_weight' in sale:
                total_usage += sale.get('stone_weight', 0)
                days_count += 1
        
        if days_count == 0:
            return 1.0  # معدل افتراضي
        
        return total_usage / days_count
    
    def generate_purchase_order(self, reorder_suggestions: List[Dict]) -> Dict:
        """إنشاء أمر شراء بناءً على اقتراحات إعادة الطلب"""
        try:
            purchase_order = {
                'order_date': datetime.now().isoformat(),
                'order_number': f"PO-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                'items': [],
                'total_cost': 0,
                'priority_items': [],
                'delivery_schedule': {}
            }
            
            for suggestion in reorder_suggestions:
                item = {
                    'name': suggestion['item'],
                    'quantity': suggestion['suggested_quantity'],
                    'estimated_cost': suggestion['estimated_cost'],
                    'priority': suggestion['priority']
                }
                
                purchase_order['items'].append(item)
                purchase_order['total_cost'] += suggestion['estimated_cost']
                
                if suggestion['priority'] == 'عالي':
                    purchase_order['priority_items'].append(suggestion['item'])
                
                # تحديد موعد التسليم المتوقع
                lead_time = self._get_lead_time(suggestion['item'])
                delivery_date = datetime.now() + timedelta(days=lead_time)
                purchase_order['delivery_schedule'][suggestion['item']] = delivery_date.isoformat()
            
            # تخزين أمر الشراء
            self.memory.store_memory(
                'inventory', 'purchase_orders', purchase_order,
                importance=1.0, expires_in_days=30
            )
            
            return purchase_order
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء أمر الشراء: {e}")
            return {'error': str(e)}
    
    def _get_lead_time(self, item_name: str) -> int:
        """الحصول على وقت التسليم للمنتج"""
        item_lower = item_name.lower()
        
        for category, lead_time in self.inventory_rules['lead_times'].items():
            if category in item_lower:
                return lead_time
        
        return 10  # وقت افتراضي
