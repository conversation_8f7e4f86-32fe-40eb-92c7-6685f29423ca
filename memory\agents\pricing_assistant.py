"""
مساعد التسعير الذكي - Smart Pricing Assistant
يحلل الأسعار ويقدم اقتراحات تسعير ذكية ومنافسة
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from ..core.memory_manager import MemoryManager

class PricingAssistant:
    """مساعد التسعير الذكي"""
    
    def __init__(self, memory_manager: MemoryManager):
        """تهيئة مساعد التسعير"""
        self.memory = memory_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل قواعد التسعير
        self._load_pricing_rules()
    
    def _load_pricing_rules(self):
        """تحميل قواعد التسعير"""
        self.pricing_rules = {
            'gold_margins': {
                'retail': 0.15,  # هامش ربح 15% للبيع بالتجزئة
                'wholesale': 0.08,  # هامش ربح 8% للبيع بالجملة
                'custom_design': 0.25  # هامش ربح 25% للتصاميم المخصصة
            },
            'service_pricing': {
                'design': {'base_rate': 500, 'complexity_multiplier': [1.0, 1.5, 2.0, 3.0]},
                'repair': {'base_rate': 100, 'time_multiplier': 50},  # 50 جنيه/ساعة
                'cleaning': {'base_rate': 50, 'premium_multiplier': 1.5},
                'engraving': {'base_rate': 150, 'character_rate': 10}
            },
            'stone_pricing': {
                'diamond': {'base_price_per_carat': 8000, 'quality_multipliers': [0.5, 0.8, 1.0, 1.5, 2.0]},
                'emerald': {'base_price_per_carat': 3000, 'quality_multipliers': [0.4, 0.7, 1.0, 1.3, 1.8]},
                'ruby': {'base_price_per_carat': 2500, 'quality_multipliers': [0.4, 0.7, 1.0, 1.3, 1.8]},
                'sapphire': {'base_price_per_carat': 2000, 'quality_multipliers': [0.4, 0.7, 1.0, 1.3, 1.8]}
            },
            'discount_rules': {
                'volume': [
                    {'min_amount': 50000, 'discount': 0.05},
                    {'min_amount': 100000, 'discount': 0.10},
                    {'min_amount': 200000, 'discount': 0.15}
                ],
                'loyalty': [
                    {'min_purchases': 5, 'discount': 0.03},
                    {'min_purchases': 10, 'discount': 0.05},
                    {'min_purchases': 20, 'discount': 0.08}
                ],
                'seasonal': {
                    'ramadan': 0.10,
                    'wedding_season': 0.05,
                    'new_year': 0.08
                }
            }
        }
    
    def calculate_gold_price(self, weight: float, karat: int, 
                           current_gold_price: float, pricing_type: str = 'retail') -> Dict:
        """حساب سعر الذهب"""
        try:
            # تحويل العيار إلى نسبة نقاء
            purity_ratios = {18: 0.75, 21: 0.875, 22: 0.916, 24: 1.0}
            purity = purity_ratios.get(karat, 0.875)
            
            # حساب السعر الأساسي
            base_price = weight * current_gold_price * purity
            
            # إضافة هامش الربح
            margin = self.pricing_rules['gold_margins'].get(pricing_type, 0.15)
            final_price = base_price * (1 + margin)
            
            # تفاصيل الحساب
            calculation = {
                'weight': weight,
                'karat': karat,
                'purity': purity,
                'current_gold_price': current_gold_price,
                'base_price': round(base_price, 2),
                'margin_percentage': margin * 100,
                'margin_amount': round(final_price - base_price, 2),
                'final_price': round(final_price, 2),
                'price_per_gram': round(final_price / weight, 2) if weight > 0 else 0
            }
            
            # تخزين حساب السعر
            self.memory.store_memory(
                'pricing', 'gold_calculation', calculation,
                importance=0.8, expires_in_days=1
            )
            
            return calculation
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب سعر الذهب: {e}")
            return {'error': str(e)}
    
    def calculate_stone_price(self, stone_type: str, weight: float, 
                            quality_grade: int = 3) -> Dict:
        """حساب سعر الأحجار الكريمة"""
        try:
            stone_type_lower = stone_type.lower()
            
            # الحصول على قواعد التسعير للحجر
            stone_rules = None
            for stone_name, rules in self.pricing_rules['stone_pricing'].items():
                if stone_name in stone_type_lower:
                    stone_rules = rules
                    break
            
            if not stone_rules:
                # سعر افتراضي للأحجار غير المعروفة
                stone_rules = {'base_price_per_carat': 1000, 'quality_multipliers': [0.5, 0.8, 1.0, 1.3, 1.8]}
            
            # حساب السعر
            base_price_per_carat = stone_rules['base_price_per_carat']
            quality_multiplier = stone_rules['quality_multipliers'][min(quality_grade - 1, len(stone_rules['quality_multipliers']) - 1)]
            
            price_per_carat = base_price_per_carat * quality_multiplier
            total_price = weight * price_per_carat
            
            calculation = {
                'stone_type': stone_type,
                'weight': weight,
                'quality_grade': quality_grade,
                'base_price_per_carat': base_price_per_carat,
                'quality_multiplier': quality_multiplier,
                'price_per_carat': round(price_per_carat, 2),
                'total_price': round(total_price, 2)
            }
            
            return calculation
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب سعر الحجر: {e}")
            return {'error': str(e)}
    
    def calculate_service_price(self, service_type: str, parameters: Dict) -> Dict:
        """حساب سعر الخدمات"""
        try:
            service_type_lower = service_type.lower()
            calculation = {'service_type': service_type, 'total_price': 0}
            
            if 'design' in service_type_lower or 'تصميم' in service_type:
                complexity = parameters.get('complexity', 1)  # 1-4
                base_rate = self.pricing_rules['service_pricing']['design']['base_rate']
                multiplier = self.pricing_rules['service_pricing']['design']['complexity_multiplier'][complexity - 1]
                total_price = base_rate * multiplier
                
                calculation.update({
                    'base_rate': base_rate,
                    'complexity': complexity,
                    'complexity_multiplier': multiplier,
                    'total_price': total_price
                })
            
            elif 'repair' in service_type_lower or 'إصلاح' in service_type:
                estimated_hours = parameters.get('hours', 1)
                base_rate = self.pricing_rules['service_pricing']['repair']['base_rate']
                hourly_rate = self.pricing_rules['service_pricing']['repair']['time_multiplier']
                total_price = base_rate + (estimated_hours * hourly_rate)
                
                calculation.update({
                    'base_rate': base_rate,
                    'estimated_hours': estimated_hours,
                    'hourly_rate': hourly_rate,
                    'total_price': total_price
                })
            
            elif 'clean' in service_type_lower or 'تنظيف' in service_type:
                is_premium = parameters.get('premium', False)
                base_rate = self.pricing_rules['service_pricing']['cleaning']['base_rate']
                multiplier = self.pricing_rules['service_pricing']['cleaning']['premium_multiplier'] if is_premium else 1.0
                total_price = base_rate * multiplier
                
                calculation.update({
                    'base_rate': base_rate,
                    'is_premium': is_premium,
                    'premium_multiplier': multiplier,
                    'total_price': total_price
                })
            
            elif 'engrav' in service_type_lower or 'نقش' in service_type:
                character_count = parameters.get('characters', 10)
                base_rate = self.pricing_rules['service_pricing']['engraving']['base_rate']
                character_rate = self.pricing_rules['service_pricing']['engraving']['character_rate']
                total_price = base_rate + (character_count * character_rate)
                
                calculation.update({
                    'base_rate': base_rate,
                    'character_count': character_count,
                    'character_rate': character_rate,
                    'total_price': total_price
                })
            
            else:
                # خدمة عامة
                calculation['total_price'] = parameters.get('estimated_price', 100)
            
            return calculation
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب سعر الخدمة: {e}")
            return {'error': str(e)}
    
    def suggest_optimal_pricing(self, item_details: Dict, market_data: Dict = None) -> Dict:
        """اقتراح التسعير الأمثل"""
        try:
            suggestions = {
                'recommended_price': 0,
                'price_range': {'min': 0, 'max': 0},
                'factors_considered': [],
                'pricing_strategy': 'متوازن',
                'confidence_level': 'متوسط'
            }
            
            # حساب السعر الأساسي
            base_cost = item_details.get('cost', 0)
            
            # تحليل السوق
            if market_data:
                competitor_prices = market_data.get('competitor_prices', [])
                if competitor_prices:
                    avg_market_price = sum(competitor_prices) / len(competitor_prices)
                    suggestions['market_average'] = avg_market_price
                    suggestions['factors_considered'].append('متوسط أسعار السوق')
            
            # تحديد استراتيجية التسعير
            customer_segment = item_details.get('customer_segment', 'متوسط')
            
            if customer_segment == 'فاخر':
                margin = 0.30  # هامش ربح 30%
                suggestions['pricing_strategy'] = 'تسعير فاخر'
            elif customer_segment == 'اقتصادي':
                margin = 0.12  # هامش ربح 12%
                suggestions['pricing_strategy'] = 'تسعير تنافسي'
            else:
                margin = 0.20  # هامش ربح 20%
                suggestions['pricing_strategy'] = 'تسعير متوازن'
            
            # حساب السعر المقترح
            recommended_price = base_cost * (1 + margin)
            
            # تحديد النطاق السعري
            price_range_min = recommended_price * 0.9
            price_range_max = recommended_price * 1.15
            
            suggestions.update({
                'recommended_price': round(recommended_price, 2),
                'price_range': {
                    'min': round(price_range_min, 2),
                    'max': round(price_range_max, 2)
                },
                'margin_percentage': margin * 100,
                'base_cost': base_cost
            })
            
            # إضافة عوامل أخرى
            suggestions['factors_considered'].extend([
                'تكلفة المواد الخام',
                'هامش الربح المستهدف',
                'شريحة العملاء المستهدفة',
                'استراتيجية التسعير'
            ])
            
            # تحديد مستوى الثقة
            if len(suggestions['factors_considered']) >= 4:
                suggestions['confidence_level'] = 'عالي'
            elif len(suggestions['factors_considered']) >= 2:
                suggestions['confidence_level'] = 'متوسط'
            else:
                suggestions['confidence_level'] = 'منخفض'
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"خطأ في اقتراح التسعير الأمثل: {e}")
            return {'error': str(e)}
    
    def calculate_discounts(self, customer_data: Dict, order_amount: float) -> Dict:
        """حساب الخصومات المتاحة"""
        try:
            available_discounts = {
                'total_discount_percentage': 0,
                'total_discount_amount': 0,
                'applied_discounts': [],
                'potential_discounts': []
            }
            
            # خصم الكمية
            for volume_rule in self.pricing_rules['discount_rules']['volume']:
                if order_amount >= volume_rule['min_amount']:
                    discount = volume_rule['discount']
                    available_discounts['applied_discounts'].append({
                        'type': 'خصم الكمية',
                        'percentage': discount * 100,
                        'amount': order_amount * discount,
                        'reason': f'طلب بقيمة {volume_rule["min_amount"]} جنيه أو أكثر'
                    })
                    available_discounts['total_discount_percentage'] += discount
            
            # خصم الولاء
            customer_purchases = customer_data.get('total_purchases', 0)
            for loyalty_rule in self.pricing_rules['discount_rules']['loyalty']:
                if customer_purchases >= loyalty_rule['min_purchases']:
                    discount = loyalty_rule['discount']
                    available_discounts['applied_discounts'].append({
                        'type': 'خصم الولاء',
                        'percentage': discount * 100,
                        'amount': order_amount * discount,
                        'reason': f'{loyalty_rule["min_purchases"]} مشتريات أو أكثر'
                    })
                    available_discounts['total_discount_percentage'] += discount
            
            # الخصومات الموسمية
            current_season = self._get_current_season()
            if current_season and current_season in self.pricing_rules['discount_rules']['seasonal']:
                discount = self.pricing_rules['discount_rules']['seasonal'][current_season]
                available_discounts['applied_discounts'].append({
                    'type': 'خصم موسمي',
                    'percentage': discount * 100,
                    'amount': order_amount * discount,
                    'reason': f'خصم {current_season}'
                })
                available_discounts['total_discount_percentage'] += discount
            
            # حساب إجمالي الخصم
            total_discount_amount = order_amount * available_discounts['total_discount_percentage']
            available_discounts['total_discount_amount'] = round(total_discount_amount, 2)
            
            # اقتراح خصومات محتملة
            if not available_discounts['applied_discounts']:
                available_discounts['potential_discounts'] = [
                    'زيادة قيمة الطلب للحصول على خصم الكمية',
                    'التسجيل في برنامج الولاء',
                    'انتظار العروض الموسمية'
                ]
            
            return available_discounts
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب الخصومات: {e}")
            return {'error': str(e)}
    
    def _get_current_season(self) -> Optional[str]:
        """تحديد الموسم الحالي"""
        current_month = datetime.now().month
        
        # رمضان (تقريبي - يتغير كل سنة)
        if current_month in [3, 4]:
            return 'ramadan'
        
        # موسم الزفاف
        elif current_month in [5, 6, 9, 10]:
            return 'wedding_season'
        
        # رأس السنة والأعياد
        elif current_month in [11, 12, 1]:
            return 'new_year'
        
        return None
    
    def analyze_pricing_performance(self, sales_data: List[Dict]) -> Dict:
        """تحليل أداء التسعير"""
        try:
            analysis = {
                'average_margin': 0,
                'price_acceptance_rate': 0,
                'revenue_trends': {},
                'recommendations': []
            }
            
            if not sales_data:
                return analysis
            
            # حساب متوسط الهامش
            total_revenue = sum(sale.get('total_amount', 0) for sale in sales_data)
            total_cost = sum(sale.get('cost', sale.get('total_amount', 0) * 0.7) for sale in sales_data)
            
            if total_cost > 0:
                analysis['average_margin'] = ((total_revenue - total_cost) / total_cost) * 100
            
            # معدل قبول الأسعار (مبسط)
            successful_sales = len([sale for sale in sales_data if sale.get('status') == 'completed'])
            total_quotes = len(sales_data)
            
            if total_quotes > 0:
                analysis['price_acceptance_rate'] = (successful_sales / total_quotes) * 100
            
            # اتجاهات الإيرادات
            monthly_revenue = {}
            for sale in sales_data:
                if 'invoice_date' in sale:
                    month = sale['invoice_date'][:7]  # YYYY-MM
                    monthly_revenue[month] = monthly_revenue.get(month, 0) + sale.get('total_amount', 0)
            
            analysis['revenue_trends'] = monthly_revenue
            
            # التوصيات
            if analysis['average_margin'] < 15:
                analysis['recommendations'].append('زيادة هوامش الربح')
            
            if analysis['price_acceptance_rate'] < 70:
                analysis['recommendations'].append('مراجعة استراتيجية التسعير')
            
            if len(monthly_revenue) > 1:
                revenues = list(monthly_revenue.values())
                if revenues[-1] < revenues[0]:
                    analysis['recommendations'].append('تحسين الأسعار التنافسية')
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل أداء التسعير: {e}")
            return {'error': str(e)}
