"""
واجهة الذكاء الاصطناعي الرئيسية - Main AI Interface
نقطة الدخول الرئيسية للتفاعل مع نظام الذكاء الاصطناعي
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

try:
    import streamlit as st
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False
    # إنشاء mock session_state للاختبار
    class MockSessionState:
        def __init__(self):
            self._state = {}

        def __contains__(self, key):
            return key in self._state

        def __getitem__(self, key):
            return self._state.get(key)

        def __setitem__(self, key, value):
            self._state[key] = value

    class MockST:
        session_state = MockSessionState()

    st = MockST()

from ..core.memory_manager import MemoryManager
from ..core.ai_agent import AIAgent
from ..agents.data_analyst import DataAnalyst
from ..agents.customer_advisor import CustomerAdvisor
from ..agents.inventory_manager import InventoryManager
from ..agents.pricing_assistant import PricingAssistant

class AIInterface:
    """واجهة الذكاء الاصطناعي الرئيسية"""
    
    def __init__(self):
        """تهيئة واجهة الذكاء الاصطناعي"""
        self.logger = logging.getLogger(__name__)
        
        # تهيئة المكونات الأساسية
        self.memory_manager = MemoryManager()
        self.ai_agent = AIAgent(self.memory_manager)
        
        # تهيئة الوكلاء المتخصصين
        self.data_analyst = DataAnalyst(self.memory_manager)
        self.customer_advisor = CustomerAdvisor(self.memory_manager)
        self.inventory_manager = InventoryManager(self.memory_manager)
        self.pricing_assistant = PricingAssistant(self.memory_manager)
        
        # تهيئة حالة الجلسة
        self._init_session_state()
    
    def _init_session_state(self):
        """تهيئة حالة الجلسة في Streamlit"""
        if 'ai_chat_history' not in st.session_state:
            st.session_state.ai_chat_history = []
        
        if 'ai_context' not in st.session_state:
            st.session_state.ai_context = {}
        
        if 'ai_suggestions' not in st.session_state:
            st.session_state.ai_suggestions = []
    
    def chat_with_ai(self, user_message: str, context: Dict = None) -> Dict:
        """التحدث مع الذكاء الاصطناعي"""
        try:
            # معالجة الرسالة
            response = self.ai_agent.process_user_input(
                user_message, 
                context or st.session_state.ai_context
            )
            
            # إضافة إلى تاريخ المحادثة
            st.session_state.ai_chat_history.append({
                'user': user_message,
                'ai': response['message'],
                'type': response.get('type', 'general'),
                'timestamp': str(datetime.now())
            })
            
            # تحديث الاقتراحات
            if 'suggestions' in response:
                st.session_state.ai_suggestions = response['suggestions']
            
            return response
            
        except Exception as e:
            self.logger.error(f"خطأ في المحادثة مع الذكاء الاصطناعي: {e}")
            return {
                'message': 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.',
                'type': 'error'
            }
    
    def get_smart_insights(self, data_type: str, data: Any) -> Dict:
        """الحصول على رؤى ذكية"""
        try:
            insights = {}
            
            if data_type == 'sales':
                insights = self.data_analyst.analyze_sales_trends(data)
            elif data_type == 'customers':
                insights = self.data_analyst.analyze_customer_behavior(data)
            elif data_type == 'inventory':
                insights = self.inventory_manager.analyze_inventory_status(data)
            elif data_type == 'pricing':
                insights = self.pricing_assistant.analyze_pricing_performance(data)
            
            return insights
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الرؤى الذكية: {e}")
            return {'error': str(e)}
    
    def get_recommendations(self, recommendation_type: str, 
                          target_id: int = None, context: Dict = None) -> List[Dict]:
        """الحصول على توصيات ذكية"""
        try:
            recommendations = []
            
            if recommendation_type == 'customer' and target_id:
                result = self.customer_advisor.get_personalized_recommendations(
                    target_id, context
                )
                recommendations = result.get('recommendations', [])
            
            elif recommendation_type == 'pricing':
                result = self.pricing_assistant.suggest_optimal_pricing(
                    context or {}, context.get('market_data', {})
                )
                recommendations = [result] if 'error' not in result else []
            
            elif recommendation_type == 'inventory':
                result = self.inventory_manager.predict_demand(
                    context.get('historical_data', [])
                )
                recommendations = [result] if 'error' not in result else []
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على التوصيات: {e}")
            return []
    
    def analyze_business_performance(self, period_data: Dict) -> Dict:
        """تحليل أداء العمل"""
        try:
            # تحليل شامل للأداء
            performance_analysis = {
                'sales_analysis': {},
                'customer_analysis': {},
                'inventory_analysis': {},
                'pricing_analysis': {},
                'overall_score': 0,
                'key_insights': [],
                'action_items': []
            }
            
            # تحليل المبيعات
            if 'sales_data' in period_data:
                performance_analysis['sales_analysis'] = self.data_analyst.analyze_sales_trends(
                    period_data['sales_data']
                )
            
            # تحليل العملاء
            if 'customer_data' in period_data:
                performance_analysis['customer_analysis'] = self.data_analyst.analyze_customer_behavior(
                    period_data['customer_data']
                )
            
            # تحليل المخزون
            if 'inventory_data' in period_data:
                performance_analysis['inventory_analysis'] = self.inventory_manager.analyze_inventory_status(
                    period_data['inventory_data']
                )
            
            # تحليل التسعير
            if 'pricing_data' in period_data:
                performance_analysis['pricing_analysis'] = self.pricing_assistant.analyze_pricing_performance(
                    period_data['pricing_data']
                )
            
            # حساب النتيجة الإجمالية
            performance_analysis['overall_score'] = self._calculate_overall_score(performance_analysis)
            
            # استخراج الرؤى الرئيسية
            performance_analysis['key_insights'] = self._extract_key_insights(performance_analysis)
            
            # إنشاء عناصر العمل
            performance_analysis['action_items'] = self._generate_action_items(performance_analysis)
            
            return performance_analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل أداء العمل: {e}")
            return {'error': str(e)}
    
    def _calculate_overall_score(self, analysis: Dict) -> float:
        """حساب النتيجة الإجمالية للأداء"""
        scores = []
        
        # نتيجة المبيعات
        sales_data = analysis.get('sales_analysis', {})
        if 'total_sales' in sales_data and sales_data['total_sales'] > 0:
            scores.append(min(sales_data['total_sales'] / 100000, 1.0) * 100)
        
        # نتيجة العملاء
        customer_data = analysis.get('customer_analysis', {})
        if 'total_customers' in customer_data:
            scores.append(min(customer_data['total_customers'] / 50, 1.0) * 100)
        
        # نتيجة المخزون
        inventory_data = analysis.get('inventory_analysis', {})
        if inventory_data.get('overall_status') == 'جيد':
            scores.append(85)
        elif inventory_data.get('overall_status') == 'يحتاج انتباه':
            scores.append(65)
        elif inventory_data.get('overall_status') == 'حرج':
            scores.append(40)
        
        return sum(scores) / len(scores) if scores else 0
    
    def _extract_key_insights(self, analysis: Dict) -> List[str]:
        """استخراج الرؤى الرئيسية"""
        insights = []
        
        # رؤى المبيعات
        sales_insights = analysis.get('sales_analysis', {}).get('insights', [])
        insights.extend(sales_insights[:2])
        
        # رؤى العملاء
        customer_recommendations = analysis.get('customer_analysis', {}).get('recommendations', [])
        insights.extend(customer_recommendations[:2])
        
        # رؤى المخزون
        inventory_recommendations = analysis.get('inventory_analysis', {}).get('recommendations', [])
        insights.extend(inventory_recommendations[:2])
        
        return insights[:5]  # أفضل 5 رؤى
    
    def _generate_action_items(self, analysis: Dict) -> List[Dict]:
        """إنشاء عناصر العمل"""
        action_items = []
        
        # عناصر عمل المبيعات
        sales_data = analysis.get('sales_analysis', {})
        if sales_data.get('total_orders', 0) < 10:
            action_items.append({
                'category': 'مبيعات',
                'action': 'زيادة الجهود التسويقية',
                'priority': 'عالي',
                'timeline': 'أسبوع واحد'
            })
        
        # عناصر عمل المخزون
        inventory_data = analysis.get('inventory_analysis', {})
        if inventory_data.get('overall_status') == 'حرج':
            action_items.append({
                'category': 'مخزون',
                'action': 'طلب مواد عاجل',
                'priority': 'عاجل',
                'timeline': '3 أيام'
            })
        
        # عناصر عمل العملاء
        customer_data = analysis.get('customer_analysis', {})
        at_risk_customers = customer_data.get('patterns', {}).get('at_risk_customers', 0)
        if at_risk_customers > 0:
            action_items.append({
                'category': 'عملاء',
                'action': 'حملة استرداد العملاء',
                'priority': 'متوسط',
                'timeline': 'أسبوعين'
            })
        
        return action_items
    
    def get_ai_dashboard_data(self) -> Dict:
        """الحصول على بيانات لوحة تحكم الذكاء الاصطناعي"""
        try:
            dashboard_data = {
                'memory_stats': self.memory_manager.get_memory_stats(),
                'recent_interactions': st.session_state.ai_chat_history[-5:],
                'smart_suggestions': self.ai_agent.get_smart_suggestions(),
                'system_status': 'نشط',
                'learning_progress': self._get_learning_progress()
            }
            
            return dashboard_data
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على بيانات لوحة التحكم: {e}")
            return {'error': str(e)}
    
    def _get_learning_progress(self) -> Dict:
        """الحصول على تقدم التعلم"""
        stats = self.memory_manager.get_memory_stats()
        
        return {
            'total_memories': stats.get('total_memories', 0),
            'customer_insights': stats.get('customer_memories', 0),
            'behavior_patterns': stats.get('behavior_patterns', 0),
            'learning_rate': 'متوسط'  # يمكن تحسينه لاحقاً
        }
    
    def export_ai_insights(self, format_type: str = 'json') -> str:
        """تصدير الرؤى الذكية"""
        try:
            insights_data = {
                'export_date': str(datetime.now()),
                'memory_stats': self.memory_manager.get_memory_stats(),
                'business_trends': self.ai_agent.analyze_business_trends(),
                'chat_history': st.session_state.ai_chat_history
            }
            
            if format_type == 'json':
                import json
                return json.dumps(insights_data, ensure_ascii=False, indent=2)
            
            # يمكن إضافة تنسيقات أخرى لاحقاً
            return str(insights_data)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير الرؤى: {e}")
            return f"خطأ في التصدير: {e}"

# إنشاء مثيل عام للواجهة
ai_interface = AIInterface()
