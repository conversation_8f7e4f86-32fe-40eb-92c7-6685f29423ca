"""
الوكيل الذكي الرئيسي - Main AI Agent
عقل التطبيق الذي يدير جميع العمليات الذكية
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
from .memory_manager import MemoryManager

class AIAgent:
    """الوكيل الذكي الرئيسي - عقل التطبيق"""
    
    def __init__(self, memory_manager: MemoryManager = None):
        """تهيئة الوكيل الذكي"""
        self.memory = memory_manager or MemoryManager()
        self.logger = logging.getLogger(__name__)
        
        # تهيئة المعرفة الأساسية
        self._init_base_knowledge()
        
        # تحميل الشخصية والسلوك
        self._load_personality()
    
    def _init_base_knowledge(self):
        """تهيئة المعرفة الأساسية للوكيل"""
        base_knowledge = {
            'jewelry_types': [
                'خواتم', 'أساور', 'قلائد', 'أقراط', 'دبابيس', 'ساعات'
            ],
            'gold_karats': [18, 21, 22, 24],
            'stone_types': [
                'ماس', 'زمرد', 'ياقوت', 'صفير', 'لؤلؤ', 'عقيق'
            ],
            'service_categories': [
                'تصميم', 'صيانة', 'إصلاح', 'تركيب', 'تعديل', 'نقش'
            ],
            'customer_preferences': {
                'style': ['كلاسيكي', 'عصري', 'تقليدي', 'فاخر'],
                'occasion': ['زفاف', 'خطوبة', 'مناسبة خاصة', 'يومي'],
                'budget_range': ['اقتصادي', 'متوسط', 'فاخر', 'حصري']
            }
        }
        
        # تخزين المعرفة الأساسية
        for category, data in base_knowledge.items():
            self.memory.store_memory('knowledge', category, data, importance=2.0)
    
    def _load_personality(self):
        """تحميل شخصية الوكيل"""
        self.personality = {
            'name': 'كريستال',
            'role': 'مساعد ذكي لورشة الماس',
            'traits': [
                'مفيد', 'ودود', 'خبير في المجوهرات', 'دقيق في التفاصيل'
            ],
            'communication_style': 'مهني وودود',
            'expertise': [
                'تقييم المجوهرات', 'تحليل الاتجاهات', 'خدمة العملاء',
                'إدارة المخزون', 'التسعير الذكي'
            ]
        }
    
    def process_user_input(self, user_input: str, context: Dict = None) -> Dict:
        """معالجة مدخلات المستخدم وتقديم رد ذكي"""
        try:
            # تحليل المدخل
            analysis = self._analyze_input(user_input)
            
            # تحديد نوع الطلب
            request_type = self._classify_request(user_input, analysis)
            
            # إنشاء الرد المناسب
            response = self._generate_response(request_type, user_input, analysis, context)
            
            # تخزين التفاعل
            self.memory.store_interaction(
                request_type, user_input, response['message'], 
                json.dumps(context or {}, ensure_ascii=False)
            )
            
            # تعلم من التفاعل
            self._learn_from_interaction(user_input, analysis, context)
            
            return response
            
        except Exception as e:
            self.logger.error(f"خطأ في معالجة المدخل: {e}")
            return {
                'message': 'عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.',
                'type': 'error',
                'suggestions': []
            }
    
    def _analyze_input(self, text: str) -> Dict:
        """تحليل النص المدخل"""
        analysis = {
            'keywords': [],
            'entities': {},
            'intent': None,
            'sentiment': 0.0,
            'language': 'ar'
        }
        
        # استخراج الكلمات المفتاحية
        keywords = self._extract_keywords(text)
        analysis['keywords'] = keywords
        
        # استخراج الكيانات (أرقام، تواريخ، أسماء)
        analysis['entities'] = self._extract_entities(text)
        
        # تحليل المشاعر البسيط
        analysis['sentiment'] = self._analyze_sentiment(text)
        
        return analysis
    
    def _extract_keywords(self, text: str) -> List[str]:
        """استخراج الكلمات المفتاحية"""
        # كلمات مفتاحية مهمة في مجال المجوهرات
        jewelry_keywords = [
            'ذهب', 'فضة', 'ماس', 'خاتم', 'سوار', 'قلادة', 'أقراط',
            'عيار', 'قيراط', 'جرام', 'سعر', 'تصميم', 'إصلاح', 'تنظيف',
            'زفاف', 'خطوبة', 'هدية', 'مناسبة', 'فاتورة', 'عميل'
        ]
        
        found_keywords = []
        text_lower = text.lower()
        
        for keyword in jewelry_keywords:
            if keyword in text_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _extract_entities(self, text: str) -> Dict:
        """استخراج الكيانات من النص"""
        entities = {
            'numbers': [],
            'prices': [],
            'weights': [],
            'dates': []
        }
        
        # استخراج الأرقام
        numbers = re.findall(r'\d+\.?\d*', text)
        entities['numbers'] = [float(n) for n in numbers]
        
        # استخراج الأسعار
        price_patterns = [r'(\d+\.?\d*)\s*(جنيه|ريال|دولار)', r'(\d+\.?\d*)\s*ج\.م']
        for pattern in price_patterns:
            matches = re.findall(pattern, text)
            entities['prices'].extend([float(m[0]) for m in matches])
        
        # استخراج الأوزان
        weight_patterns = [r'(\d+\.?\d*)\s*(جرام|قيراط|أونصة)']
        for pattern in weight_patterns:
            matches = re.findall(pattern, text)
            entities['weights'].extend([float(m[0]) for m in matches])
        
        return entities
    
    def _analyze_sentiment(self, text: str) -> float:
        """تحليل المشاعر البسيط"""
        positive_words = [
            'ممتاز', 'رائع', 'جميل', 'مذهل', 'أحب', 'سعيد', 'راضي', 'شكراً'
        ]
        negative_words = [
            'سيء', 'مشكلة', 'خطأ', 'غاضب', 'مستاء', 'لا أحب', 'صعب'
        ]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count + negative_count == 0:
            return 0.0
        
        return (positive_count - negative_count) / (positive_count + negative_count)
    
    def _classify_request(self, text: str, analysis: Dict) -> str:
        """تصنيف نوع الطلب"""
        keywords = analysis['keywords']
        text_lower = text.lower()
        
        # أنواع الطلبات المختلفة
        if any(word in text_lower for word in ['سعر', 'كم', 'تكلفة', 'ثمن']):
            return 'pricing_inquiry'
        elif any(word in text_lower for word in ['فاتورة', 'حساب', 'إيصال']):
            return 'invoice_request'
        elif any(word in text_lower for word in ['عميل', 'زبون', 'معلومات']):
            return 'customer_inquiry'
        elif any(word in text_lower for word in ['تصميم', 'اقتراح', 'نصيحة']):
            return 'design_consultation'
        elif any(word in text_lower for word in ['إصلاح', 'صيانة', 'تنظيف']):
            return 'service_request'
        elif any(word in text_lower for word in ['تقرير', 'إحصائية', 'تحليل']):
            return 'analytics_request'
        elif any(word in text_lower for word in ['مساعدة', 'كيف', 'شرح']):
            return 'help_request'
        else:
            return 'general_inquiry'
    
    def _generate_response(self, request_type: str, user_input: str, 
                          analysis: Dict, context: Dict = None) -> Dict:
        """إنشاء رد مناسب حسب نوع الطلب"""
        
        response_generators = {
            'pricing_inquiry': self._handle_pricing_inquiry,
            'invoice_request': self._handle_invoice_request,
            'customer_inquiry': self._handle_customer_inquiry,
            'design_consultation': self._handle_design_consultation,
            'service_request': self._handle_service_request,
            'analytics_request': self._handle_analytics_request,
            'help_request': self._handle_help_request,
            'general_inquiry': self._handle_general_inquiry
        }
        
        handler = response_generators.get(request_type, self._handle_general_inquiry)
        return handler(user_input, analysis, context)
    
    def _handle_pricing_inquiry(self, user_input: str, analysis: Dict, context: Dict) -> Dict:
        """التعامل مع استفسارات الأسعار"""
        keywords = analysis['keywords']

        # البحث عن معلومات الأسعار في الذاكرة
        price_data = self.memory.retrieve_memory('pricing', 'current_rates')

        suggestions = [
            "عرض قائمة الأسعار الحالية",
            "حساب تكلفة قطعة مخصصة",
            "مقارنة الأسعار حسب العيار"
        ]

        if 'ذهب' in keywords:
            message = "يمكنني مساعدتك في معرفة أسعار الذهب الحالية. أسعار اليوم تختلف حسب العيار:"
            if price_data:
                message += f"\n• عيار 18: {price_data.get('gold_18', 'غير متوفر')} جنيه/جرام"
                message += f"\n• عيار 21: {price_data.get('gold_21', 'غير متوفر')} جنيه/جرام"
        else:
            message = "أهلاً بك! يمكنني مساعدتك في معرفة أسعار المجوهرات والخدمات. ما نوع القطعة التي تستفسر عنها؟"

        return {
            'message': message,
            'type': 'pricing',
            'suggestions': suggestions
        }

    def _handle_invoice_request(self, user_input: str, analysis: Dict, context: Dict) -> Dict:
        """التعامل مع طلبات الفواتير"""
        suggestions = [
            "إنشاء فاتورة جديدة",
            "البحث عن فاتورة موجودة",
            "طباعة فاتورة"
        ]

        message = "يمكنني مساعدتك في إدارة الفواتير. هل تريد إنشاء فاتورة جديدة أم البحث عن فاتورة موجودة؟"

        return {
            'message': message,
            'type': 'invoice',
            'suggestions': suggestions
        }

    def _handle_service_request(self, user_input: str, analysis: Dict, context: Dict) -> Dict:
        """التعامل مع طلبات الخدمات"""
        suggestions = [
            "خدمات الإصلاح المتاحة",
            "حجز موعد للصيانة",
            "تقدير تكلفة الخدمة"
        ]

        message = "نقدم مجموعة متنوعة من الخدمات: الإصلاح، التنظيف، التصميم، والنقش. أي خدمة تحتاجها؟"

        return {
            'message': message,
            'type': 'service',
            'suggestions': suggestions
        }

    def _handle_analytics_request(self, user_input: str, analysis: Dict, context: Dict) -> Dict:
        """التعامل مع طلبات التحليلات"""
        suggestions = [
            "تقرير المبيعات الشهري",
            "تحليل أداء العملاء",
            "إحصائيات المخزون"
        ]

        message = "يمكنني إنشاء تقارير وتحليلات مختلفة لمساعدتك في اتخاذ القرارات. أي نوع من التقارير تحتاج؟"

        return {
            'message': message,
            'type': 'analytics',
            'suggestions': suggestions
        }

    def _handle_help_request(self, user_input: str, analysis: Dict, context: Dict) -> Dict:
        """التعامل مع طلبات المساعدة"""
        suggestions = [
            "كيفية إضافة عميل جديد",
            "كيفية إنشاء فاتورة",
            "كيفية إدارة المخزون"
        ]

        message = "أنا هنا لمساعدتك! يمكنني شرح كيفية استخدام النظام أو الإجابة على أي استفسارات لديك."

        return {
            'message': message,
            'type': 'help',
            'suggestions': suggestions
        }
    
    def _handle_customer_inquiry(self, user_input: str, analysis: Dict, context: Dict) -> Dict:
        """التعامل مع استفسارات العملاء"""
        suggestions = [
            "إضافة عميل جديد",
            "البحث عن عميل",
            "عرض تاريخ العميل"
        ]
        
        message = "يمكنني مساعدتك في إدارة معلومات العملاء. هل تريد إضافة عميل جديد أم البحث عن عميل موجود؟"
        
        return {
            'message': message,
            'type': 'customer',
            'suggestions': suggestions
        }
    
    def _handle_design_consultation(self, user_input: str, analysis: Dict, context: Dict) -> Dict:
        """التعامل مع استشارات التصميم"""
        suggestions = [
            "اقتراح تصاميم حسب المناسبة",
            "نصائح اختيار الأحجار",
            "توصيات العيار المناسب"
        ]
        
        message = "سأكون سعيداً لمساعدتك في اختيار التصميم المناسب! أخبرني عن المناسبة والميزانية المتاحة."
        
        return {
            'message': message,
            'type': 'design',
            'suggestions': suggestions
        }
    
    def _handle_general_inquiry(self, user_input: str, analysis: Dict, context: Dict) -> Dict:
        """التعامل مع الاستفسارات العامة"""
        suggestions = [
            "عرض الخدمات المتاحة",
            "معلومات عن الورشة",
            "طرق التواصل"
        ]
        
        message = f"أهلاً بك في ورشة الماس! أنا {self.personality['name']}، مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟"
        
        return {
            'message': message,
            'type': 'general',
            'suggestions': suggestions
        }
    
    def _learn_from_interaction(self, user_input: str, analysis: Dict, context: Dict):
        """التعلم من التفاعل مع المستخدم"""
        # تخزين الكلمات المفتاحية الجديدة
        for keyword in analysis['keywords']:
            self.memory.store_behavior_pattern(
                'keyword_usage', 
                {'keyword': keyword, 'context': context or {}},
                confidence=0.7
            )
        
        # تخزين أنماط الاستفسار
        if analysis['entities']['numbers']:
            self.memory.store_behavior_pattern(
                'numeric_inquiry',
                {'numbers': analysis['entities']['numbers'], 'input': user_input[:50]},
                confidence=0.6
            )
    
    def get_smart_suggestions(self, context: Dict = None) -> List[str]:
        """الحصول على اقتراحات ذكية"""
        suggestions = []
        
        # اقتراحات بناءً على الوقت
        current_hour = datetime.now().hour
        if 9 <= current_hour <= 12:
            suggestions.append("مراجعة فواتير الصباح")
        elif 13 <= current_hour <= 17:
            suggestions.append("متابعة طلبات العملاء")
        
        # اقتراحات بناءً على أنماط السلوك
        patterns = self.memory.get_behavior_patterns(min_confidence=0.5)
        for pattern in patterns[:3]:
            if pattern['type'] == 'keyword_usage':
                keyword = pattern['data'].get('keyword')
                suggestions.append(f"مراجعة معلومات {keyword}")
        
        return suggestions[:5]
    
    def analyze_business_trends(self) -> Dict:
        """تحليل اتجاهات العمل"""
        # هذه دالة مبسطة - يمكن توسيعها لاحقاً
        return {
            'popular_services': ['تصميم مجوهرات', 'تنظيف وتلميع'],
            'peak_hours': ['10:00-12:00', '15:00-17:00'],
            'customer_preferences': ['ذهب عيار 21', 'تصاميم عصرية'],
            'recommendations': [
                'زيادة مخزون الذهب عيار 21',
                'تطوير خدمات التصميم المخصص',
                'تحسين خدمة العملاء في ساعات الذروة'
            ]
        }
