"""
مدير الذاكرة - Memory Manager
يدير تخزين واسترجاع الذكريات والبيانات التاريخية
"""

import json
import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

class MemoryManager:
    """مدير الذاكرة الرئيسي"""
    
    def __init__(self, memory_db_path: str = "memory/storage/memory.db"):
        """تهيئة مدير الذاكرة"""
        self.db_path = memory_db_path
        self.logger = logging.getLogger(__name__)
        
        # إنشاء مجلد التخزين إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(memory_db_path), exist_ok=True)
        
        # تهيئة قاعدة البيانات
        self._init_database()
    
    def _init_database(self):
        """تهيئة قاعدة بيانات الذاكرة"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # جدول الذكريات العامة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT NOT NULL,
                    subcategory TEXT,
                    key_data TEXT NOT NULL,
                    value_data TEXT NOT NULL,
                    metadata TEXT,
                    importance_score REAL DEFAULT 1.0,
                    access_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP
                )
            """)
            
            # جدول ذكريات العملاء
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS customer_memories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    context TEXT,
                    sentiment REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول أنماط السلوك
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS behavior_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_type TEXT NOT NULL,
                    pattern_data TEXT NOT NULL,
                    frequency INTEGER DEFAULT 1,
                    confidence_score REAL DEFAULT 0.5,
                    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول التفاعلات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS interactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    interaction_type TEXT NOT NULL,
                    user_input TEXT,
                    ai_response TEXT,
                    context TEXT,
                    satisfaction_score REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    def store_memory(self, category: str, key: str, value: Any, 
                    subcategory: str = None, metadata: Dict = None, 
                    importance: float = 1.0, expires_in_days: int = None) -> bool:
        """تخزين ذكرى جديدة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # تحديد تاريخ الانتهاء
                expires_at = None
                if expires_in_days:
                    expires_at = datetime.now() + timedelta(days=expires_in_days)
                
                cursor.execute("""
                    INSERT OR REPLACE INTO memories
                    (category, subcategory, key_data, value_data, metadata,
                     importance_score, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    category, subcategory, key, json.dumps(value, ensure_ascii=False, default=str),
                    json.dumps(metadata or {}, ensure_ascii=False, default=str), importance, expires_at
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في تخزين الذكرى: {e}")
            return False
    
    def retrieve_memory(self, category: str, key: str = None, 
                       subcategory: str = None) -> Optional[Any]:
        """استرجاع ذكرى"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # بناء الاستعلام
                query = "SELECT value_data, access_count FROM memories WHERE category = ?"
                params = [category]
                
                if key:
                    query += " AND key_data = ?"
                    params.append(key)
                
                if subcategory:
                    query += " AND subcategory = ?"
                    params.append(subcategory)
                
                query += " AND (expires_at IS NULL OR expires_at > datetime('now'))"
                query += " ORDER BY importance_score DESC, updated_at DESC LIMIT 1"
                
                cursor.execute(query, params)
                result = cursor.fetchone()
                
                if result:
                    # تحديث عداد الوصول
                    cursor.execute("""
                        UPDATE memories SET access_count = access_count + 1,
                        updated_at = CURRENT_TIMESTAMP
                        WHERE category = ? AND key_data = ?
                    """, (category, key or ''))
                    
                    conn.commit()
                    return json.loads(result[0])
                
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في استرجاع الذكرى: {e}")
            return None
    
    def store_customer_memory(self, customer_id: int, memory_type: str, 
                            content: str, context: str = None, 
                            sentiment: float = None) -> bool:
        """تخزين ذكرى خاصة بعميل"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO customer_memories 
                    (customer_id, memory_type, content, context, sentiment)
                    VALUES (?, ?, ?, ?, ?)
                """, (customer_id, memory_type, content, context, sentiment))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في تخزين ذكرى العميل: {e}")
            return False
    
    def get_customer_memories(self, customer_id: int, 
                            memory_type: str = None) -> List[Dict]:
        """الحصول على ذكريات عميل معين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = """
                    SELECT memory_type, content, context, sentiment, created_at
                    FROM customer_memories WHERE customer_id = ?
                """
                params = [customer_id]
                
                if memory_type:
                    query += " AND memory_type = ?"
                    params.append(memory_type)
                
                query += " ORDER BY created_at DESC"
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                return [
                    {
                        'type': row[0],
                        'content': row[1],
                        'context': row[2],
                        'sentiment': row[3],
                        'created_at': row[4]
                    }
                    for row in results
                ]
                
        except Exception as e:
            self.logger.error(f"خطأ في استرجاع ذكريات العميل: {e}")
            return []
    
    def store_behavior_pattern(self, pattern_type: str, pattern_data: Dict, 
                             confidence: float = 0.5) -> bool:
        """تخزين نمط سلوك"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # البحث عن نمط مشابه
                cursor.execute("""
                    SELECT id, frequency FROM behavior_patterns 
                    WHERE pattern_type = ? AND pattern_data = ?
                """, (pattern_type, json.dumps(pattern_data, ensure_ascii=False)))
                
                existing = cursor.fetchone()
                
                if existing:
                    # تحديث النمط الموجود
                    cursor.execute("""
                        UPDATE behavior_patterns 
                        SET frequency = frequency + 1, 
                            confidence_score = ?,
                            last_seen = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (confidence, existing[0]))
                else:
                    # إدراج نمط جديد
                    cursor.execute("""
                        INSERT INTO behavior_patterns 
                        (pattern_type, pattern_data, confidence_score)
                        VALUES (?, ?, ?)
                    """, (pattern_type, json.dumps(pattern_data, ensure_ascii=False), confidence))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في تخزين نمط السلوك: {e}")
            return False
    
    def get_behavior_patterns(self, pattern_type: str = None, 
                            min_confidence: float = 0.3) -> List[Dict]:
        """الحصول على أنماط السلوك"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = """
                    SELECT pattern_type, pattern_data, frequency, confidence_score, last_seen
                    FROM behavior_patterns 
                    WHERE confidence_score >= ?
                """
                params = [min_confidence]
                
                if pattern_type:
                    query += " AND pattern_type = ?"
                    params.append(pattern_type)
                
                query += " ORDER BY confidence_score DESC, frequency DESC"
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                return [
                    {
                        'type': row[0],
                        'data': json.loads(row[1]),
                        'frequency': row[2],
                        'confidence': row[3],
                        'last_seen': row[4]
                    }
                    for row in results
                ]
                
        except Exception as e:
            self.logger.error(f"خطأ في استرجاع أنماط السلوك: {e}")
            return []
    
    def store_interaction(self, interaction_type: str, user_input: str, 
                         ai_response: str, context: str = None, 
                         satisfaction: float = None) -> bool:
        """تخزين تفاعل مع المستخدم"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO interactions 
                    (interaction_type, user_input, ai_response, context, satisfaction_score)
                    VALUES (?, ?, ?, ?, ?)
                """, (interaction_type, user_input, ai_response, context, satisfaction))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في تخزين التفاعل: {e}")
            return False
    
    def cleanup_expired_memories(self) -> int:
        """تنظيف الذكريات المنتهية الصلاحية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    DELETE FROM memories 
                    WHERE expires_at IS NOT NULL AND expires_at <= datetime('now')
                """)
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                self.logger.info(f"تم حذف {deleted_count} ذكرى منتهية الصلاحية")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الذكريات: {e}")
            return 0
    
    def get_memory_stats(self) -> Dict:
        """الحصول على إحصائيات الذاكرة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # عدد الذكريات
                cursor.execute("SELECT COUNT(*) FROM memories")
                stats['total_memories'] = cursor.fetchone()[0]
                
                # عدد ذكريات العملاء
                cursor.execute("SELECT COUNT(*) FROM customer_memories")
                stats['customer_memories'] = cursor.fetchone()[0]
                
                # عدد أنماط السلوك
                cursor.execute("SELECT COUNT(*) FROM behavior_patterns")
                stats['behavior_patterns'] = cursor.fetchone()[0]
                
                # عدد التفاعلات
                cursor.execute("SELECT COUNT(*) FROM interactions")
                stats['interactions'] = cursor.fetchone()[0]
                
                # الذكريات الأكثر وصولاً
                cursor.execute("""
                    SELECT category, key_data, access_count 
                    FROM memories 
                    ORDER BY access_count DESC LIMIT 5
                """)
                stats['most_accessed'] = cursor.fetchall()
                
                return stats
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات الذاكرة: {e}")
            return {}
