#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لنظام الذكاء الاصطناعي
Quick Start for AI System
"""

import os
import sys

# إضافة المسار للوصول للمكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def quick_demo():
    """عرض توضيحي سريع للنظام"""
    print("🚀 عرض توضيحي سريع لنظام الذكاء الاصطناعي")
    print("=" * 60)
    
    try:
        # استيراد المكونات
        from memory.core.memory_manager import MemoryManager
        from memory.core.ai_agent import AIAgent
        from memory.agents.pricing_assistant import PricingAssistant
        
        print("✅ تم تحميل جميع المكونات بنجاح")
        
        # تهيئة النظام
        memory = MemoryManager()
        agent = AIAgent(memory)
        pricing = PricingAssistant(memory)
        
        print("✅ تم تهيئة النظام بنجاح")
        
        # عرض توضيحي للمحادثة
        print("\n🤖 عرض توضيحي للمحادثة مع كريستال:")
        print("-" * 40)
        
        test_messages = [
            "مرحبا كريستال",
            "ما هو سعر الذهب اليوم؟",
            "كيف يمكنني إضافة عميل جديد؟",
            "اقترح لي تصميم خاتم للزفاف"
        ]
        
        for message in test_messages:
            print(f"👤 المستخدم: {message}")
            response = agent.process_user_input(message)
            print(f"🤖 كريستال: {response['message'][:100]}...")
            print()
        
        # عرض توضيحي للتسعير
        print("\n💰 عرض توضيحي لحاسبة التسعير:")
        print("-" * 40)
        
        gold_calc = pricing.calculate_gold_price(
            weight=10.5, 
            karat=21, 
            current_gold_price=3200
        )
        
        print(f"حساب سعر الذهب:")
        print(f"الوزن: {gold_calc['weight']} جرام")
        print(f"العيار: {gold_calc['karat']} قيراط")
        print(f"السعر النهائي: {gold_calc['final_price']:.2f} جنيه")
        
        # عرض إحصائيات الذاكرة
        print("\n📊 إحصائيات الذاكرة:")
        print("-" * 40)
        
        stats = memory.get_memory_stats()
        print(f"إجمالي الذكريات: {stats.get('total_memories', 0)}")
        print(f"ذكريات العملاء: {stats.get('customer_memories', 0)}")
        print(f"أنماط السلوك: {stats.get('behavior_patterns', 0)}")
        print(f"التفاعلات: {stats.get('interactions', 0)}")
        
        print("\n🎉 العرض التوضيحي اكتمل بنجاح!")
        print("النظام جاهز للاستخدام في تطبيق Streamlit")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")
        return False

def interactive_chat():
    """محادثة تفاعلية مع النظام"""
    print("\n💬 محادثة تفاعلية مع كريستال")
    print("اكتب 'خروج' للإنهاء")
    print("-" * 40)
    
    try:
        from memory.core.memory_manager import MemoryManager
        from memory.core.ai_agent import AIAgent
        
        memory = MemoryManager()
        agent = AIAgent(memory)
        
        while True:
            user_input = input("\n👤 أنت: ").strip()
            
            if user_input.lower() in ['خروج', 'exit', 'quit']:
                print("👋 وداعاً!")
                break
            
            if not user_input:
                continue
            
            response = agent.process_user_input(user_input)
            print(f"🤖 كريستال: {response['message']}")
            
            if 'suggestions' in response and response['suggestions']:
                print("\n💡 اقتراحات:")
                for i, suggestion in enumerate(response['suggestions'][:3], 1):
                    print(f"   {i}. {suggestion}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في المحادثة التفاعلية: {e}")
        return False

def pricing_demo():
    """عرض توضيحي لحاسبة التسعير"""
    print("\n💰 عرض توضيحي لحاسبة التسعير")
    print("-" * 40)
    
    try:
        from memory.core.memory_manager import MemoryManager
        from memory.agents.pricing_assistant import PricingAssistant
        
        memory = MemoryManager()
        pricing = PricingAssistant(memory)
        
        # حساب أسعار مختلفة
        calculations = [
            {
                'type': 'ذهب',
                'params': {'weight': 15.0, 'karat': 21, 'current_gold_price': 3200},
                'func': pricing.calculate_gold_price
            },
            {
                'type': 'حجر ماس',
                'params': {'stone_type': 'diamond', 'weight': 2.0, 'quality_grade': 4},
                'func': pricing.calculate_stone_price
            },
            {
                'type': 'خدمة تصميم',
                'params': {'service_type': 'design', 'parameters': {'complexity': 3}},
                'func': pricing.calculate_service_price
            }
        ]
        
        for calc in calculations:
            print(f"\n📋 حساب سعر {calc['type']}:")
            result = calc['func'](**calc['params'])
            
            if 'error' not in result:
                if 'final_price' in result:
                    print(f"   السعر النهائي: {result['final_price']:.2f} جنيه")
                elif 'total_price' in result:
                    print(f"   السعر الإجمالي: {result['total_price']:.2f} جنيه")
                else:
                    print(f"   النتيجة: {result}")
            else:
                print(f"   خطأ: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض التسعير: {e}")
        return False

def main():
    """القائمة الرئيسية"""
    print("🧠 نظام الذاكرة والذكاء الاصطناعي - ورشة الماس")
    print("=" * 60)
    
    while True:
        print("\nاختر من القائمة:")
        print("1. عرض توضيحي سريع")
        print("2. محادثة تفاعلية")
        print("3. عرض حاسبة التسعير")
        print("4. اختبار النظام")
        print("5. خروج")
        
        choice = input("\nاختيارك (1-5): ").strip()
        
        if choice == '1':
            quick_demo()
        elif choice == '2':
            interactive_chat()
        elif choice == '3':
            pricing_demo()
        elif choice == '4':
            print("\n🧪 تشغيل اختبار النظام...")
            os.system("python memory/simple_test.py")
        elif choice == '5':
            print("👋 شكراً لاستخدام النظام!")
            break
        else:
            print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إنهاء البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    sys.exit(0)
