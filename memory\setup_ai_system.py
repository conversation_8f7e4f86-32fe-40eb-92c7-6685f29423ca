#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد نظام الذاكرة والذكاء الاصطناعي
Setup Memory and AI System
"""

import os
import sys
import json
from datetime import datetime

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'memory/storage',
        'memory/logs',
        'memory/backups'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")

def initialize_memory_database():
    """تهيئة قاعدة بيانات الذاكرة"""
    try:
        from memory.core.memory_manager import MemoryManager
        
        memory = MemoryManager()
        
        # إضافة بيانات أولية
        initial_data = {
            'knowledge': {
                'jewelry_types': ['خواتم', 'أساور', 'قلائد', 'أقراط'],
                'gold_karats': [18, 21, 22, 24],
                'stone_types': ['ماس', 'زمرد', 'ياقوت', 'صفير']
            },
            'pricing': {
                'gold_18': 2800,
                'gold_21': 3200,
                'gold_22': 3400,
                'gold_24': 3600
            },
            'services': {
                'design': 'خدمة التصميم المخصص',
                'repair': 'خدمة الإصلاح والصيانة',
                'cleaning': 'خدمة التنظيف والتلميع'
            }
        }
        
        for category, data in initial_data.items():
            for key, value in data.items():
                memory.store_memory(category, key, value, importance=2.0)
        
        print("✅ تم تهيئة قاعدة بيانات الذاكرة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

def test_system_components():
    """اختبار مكونات النظام"""
    print("\n🧪 اختبار مكونات النظام...")
    
    components = [
        ('مدير الذاكرة', 'memory.core.memory_manager', 'MemoryManager'),
        ('الوكيل الذكي', 'memory.core.ai_agent', 'AIAgent'),
        ('محلل البيانات', 'memory.agents.data_analyst', 'DataAnalyst'),
        ('مستشار العملاء', 'memory.agents.customer_advisor', 'CustomerAdvisor'),
        ('مدير المخزون', 'memory.agents.inventory_manager', 'InventoryManager'),
        ('مساعد التسعير', 'memory.agents.pricing_assistant', 'PricingAssistant')
    ]
    
    success_count = 0
    
    for name, module_path, class_name in components:
        try:
            module = __import__(module_path, fromlist=[class_name])
            component_class = getattr(module, class_name)
            
            # اختبار إنشاء المكون
            if class_name == 'MemoryManager':
                instance = component_class()
            else:
                from memory.core.memory_manager import MemoryManager
                memory = MemoryManager()
                instance = component_class(memory)
            
            print(f"✅ {name}: يعمل بشكل صحيح")
            success_count += 1
            
        except Exception as e:
            print(f"❌ {name}: خطأ - {e}")
    
    print(f"\n📊 النتيجة: {success_count}/{len(components)} مكون يعمل بشكل صحيح")
    return success_count == len(components)

def create_config_file():
    """إنشاء ملف الإعدادات"""
    config = {
        "ai_system": {
            "name": "كريستال",
            "version": "1.0.0",
            "language": "ar",
            "memory_db_path": "memory/storage/memory.db",
            "log_level": "INFO"
        },
        "memory": {
            "cleanup_interval_days": 30,
            "max_memories": 10000,
            "backup_enabled": True,
            "backup_interval_hours": 24
        },
        "agents": {
            "data_analyst": {
                "enabled": True,
                "confidence_threshold": 0.7
            },
            "customer_advisor": {
                "enabled": True,
                "recommendation_limit": 5
            },
            "inventory_manager": {
                "enabled": True,
                "reorder_threshold": 0.2
            },
            "pricing_assistant": {
                "enabled": True,
                "default_margin": 0.15
            }
        },
        "setup": {
            "installed": True,
            "installation_date": datetime.now().isoformat(),
            "version": "1.0.0"
        }
    }
    
    config_path = 'memory/config.json'
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ تم إنشاء ملف الإعدادات: {config_path}")

def create_startup_script():
    """إنشاء سكريبت بدء التشغيل"""
    startup_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بدء تشغيل نظام الذكاء الاصطناعي
AI System Startup Script
"""

import sys
import os

# إضافة المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def start_ai_system():
    """بدء تشغيل النظام"""
    try:
        from memory.core.memory_manager import MemoryManager
        from memory.core.ai_agent import AIAgent
        
        print("🚀 بدء تشغيل نظام الذكاء الاصطناعي...")
        
        # تهيئة المكونات
        memory = MemoryManager()
        agent = AIAgent(memory)
        
        print("✅ تم تشغيل النظام بنجاح!")
        print("يمكنك الآن استخدام النظام في تطبيق Streamlit")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

if __name__ == "__main__":
    start_ai_system()
'''
    
    with open('memory/start_ai.py', 'w', encoding='utf-8') as f:
        f.write(startup_script)
    
    print("✅ تم إنشاء سكريبت بدء التشغيل: memory/start_ai.py")

def main():
    """الدالة الرئيسية للإعداد"""
    print("🔧 إعداد نظام الذاكرة والذكاء الاصطناعي")
    print("=" * 60)
    
    steps = [
        ("إنشاء المجلدات المطلوبة", create_directories),
        ("تهيئة قاعدة بيانات الذاكرة", initialize_memory_database),
        ("اختبار مكونات النظام", test_system_components),
        ("إنشاء ملف الإعدادات", create_config_file),
        ("إنشاء سكريبت بدء التشغيل", create_startup_script)
    ]
    
    success_count = 0
    
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_function():
                success_count += 1
            else:
                print(f"⚠️ {step_name} اكتمل مع تحذيرات")
        except Exception as e:
            print(f"❌ فشل في {step_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتيجة الإعداد: {success_count}/{len(steps)} خطوة نجحت")
    
    if success_count == len(steps):
        print("🎉 تم إعداد النظام بنجاح!")
        print("\nالخطوات التالية:")
        print("1. تشغيل: python memory/quick_start.py للتجربة")
        print("2. استخدام النظام في تطبيق Streamlit")
        print("3. مراجعة ملف memory/README.md للتوثيق")
        return True
    else:
        print("⚠️ الإعداد اكتمل مع بعض المشاكل")
        print("يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    # إضافة المسار للوصول للمكتبات
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الإعداد بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع في الإعداد: {e}")
        sys.exit(1)
