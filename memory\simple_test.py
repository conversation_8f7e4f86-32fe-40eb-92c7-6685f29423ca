#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لنظام الذاكرة والذكاء الاصطناعي
"""

import os
import sys

# إضافة المسار للوصول للمكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_memory_manager():
    """اختبار مدير الذاكرة"""
    try:
        from memory.core.memory_manager import MemoryManager
        
        memory = MemoryManager()
        
        # اختبار تخزين واسترجاع
        success = memory.store_memory('test', 'key1', {'value': 'test'})
        if not success:
            return False
        
        retrieved = memory.retrieve_memory('test', 'key1')
        if not retrieved or retrieved['value'] != 'test':
            return False
        
        print("✅ مدير الذاكرة يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مدير الذاكرة: {e}")
        return False

def test_ai_agent():
    """اختبار الوكيل الذكي"""
    try:
        from memory.core.memory_manager import MemoryManager
        from memory.core.ai_agent import AIAgent
        
        memory = MemoryManager()
        agent = AIAgent(memory)
        
        # اختبار معالجة مدخل بسيط
        response = agent.process_user_input("مرحبا")
        if not response or 'message' not in response:
            return False
        
        print("✅ الوكيل الذكي يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الوكيل الذكي: {e}")
        return False

def test_pricing_assistant():
    """اختبار مساعد التسعير"""
    try:
        from memory.core.memory_manager import MemoryManager
        from memory.agents.pricing_assistant import PricingAssistant
        
        memory = MemoryManager()
        pricing = PricingAssistant(memory)
        
        # اختبار حساب سعر الذهب
        result = pricing.calculate_gold_price(10, 21, 3200)
        if 'error' in result:
            return False
        
        print("✅ مساعد التسعير يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مساعد التسعير: {e}")
        return False

def main():
    """تشغيل الاختبارات المبسطة"""
    print("🧪 اختبار مبسط لنظام الذكاء الاصطناعي")
    print("=" * 50)
    
    tests = [
        ("مدير الذاكرة", test_memory_manager),
        ("الوكيل الذكي", test_ai_agent),
        ("مساعد التسعير", test_pricing_assistant)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}...")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 النتيجة: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
