#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بدء تشغيل نظام الذكاء الاصطناعي
AI System Startup Script
"""

import sys
import os

# إضافة المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def start_ai_system():
    """بدء تشغيل النظام"""
    try:
        from memory.core.memory_manager import MemoryManager
        from memory.core.ai_agent import AIAgent
        
        print("🚀 بدء تشغيل نظام الذكاء الاصطناعي...")
        
        # تهيئة المكونات
        memory = MemoryManager()
        agent = AIAgent(memory)
        
        print("✅ تم تشغيل النظام بنجاح!")
        print("يمكنك الآن استخدام النظام في تطبيق Streamlit")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

if __name__ == "__main__":
    start_ai_system()
