"""
تكامل نظام الذكاء الاصطناعي مع Streamlit
Streamlit Integration for AI System
"""

import streamlit as st
import pandas as pd
from datetime import datetime
import json

# استيراد مكونات النظام
try:
    from memory.core.memory_manager import MemoryManager
    from memory.core.ai_agent import AIAgent
    from memory.agents.data_analyst import DataAnalyst
    from memory.agents.customer_advisor import CustomerAdvisor
    from memory.agents.inventory_manager import InventoryManager
    from memory.agents.pricing_assistant import PricingAssistant
except ImportError as e:
    st.error(f"خطأ في استيراد مكونات النظام: {e}")
    st.stop()

def init_ai_system():
    """تهيئة نظام الذكاء الاصطناعي"""
    if 'ai_system' not in st.session_state:
        try:
            memory_manager = MemoryManager()
            ai_agent = AIAgent(memory_manager)
            
            st.session_state.ai_system = {
                'memory_manager': memory_manager,
                'ai_agent': ai_agent,
                'data_analyst': <PERSON><PERSON><PERSON><PERSON><PERSON>(memory_manager),
                'customer_advisor': Customer<PERSON>d<PERSON>(memory_manager),
                'inventory_manager': InventoryManager(memory_manager),
                'pricing_assistant': PricingAssistant(memory_manager)
            }
            
            # تهيئة تاريخ المحادثة
            if 'ai_chat_history' not in st.session_state:
                st.session_state.ai_chat_history = []
            
            return True
        except Exception as e:
            st.error(f"فشل في تهيئة نظام الذكاء الاصطناعي: {e}")
            return False
    return True

def ai_chat_interface():
    """واجهة المحادثة مع الذكاء الاصطناعي"""
    st.subheader("🤖 كريستال - المساعد الذكي")
    
    if not init_ai_system():
        return
    
    ai_agent = st.session_state.ai_system['ai_agent']
    
    # عرض تاريخ المحادثة
    chat_container = st.container()
    
    with chat_container:
        for message in st.session_state.ai_chat_history:
            with st.chat_message("user"):
                st.write(message['user'])
            with st.chat_message("assistant"):
                st.write(message['ai'])
    
    # مربع إدخال المستخدم
    if prompt := st.chat_input("اسأل كريستال..."):
        # إضافة رسالة المستخدم
        with st.chat_message("user"):
            st.write(prompt)
        
        # الحصول على رد الذكاء الاصطناعي
        with st.chat_message("assistant"):
            with st.spinner("كريستال تفكر..."):
                response = ai_agent.process_user_input(prompt)
                st.write(response['message'])
                
                # عرض الاقتراحات إن وجدت
                if 'suggestions' in response and response['suggestions']:
                    st.write("**اقتراحات:**")
                    for suggestion in response['suggestions']:
                        st.write(f"• {suggestion}")
        
        # حفظ المحادثة
        st.session_state.ai_chat_history.append({
            'user': prompt,
            'ai': response['message'],
            'timestamp': datetime.now().isoformat()
        })

def ai_insights_dashboard():
    """لوحة تحكم الرؤى الذكية"""
    st.subheader("📊 لوحة تحكم الذكاء الاصطناعي")
    
    if not init_ai_system():
        return
    
    memory_manager = st.session_state.ai_system['memory_manager']
    
    # إحصائيات الذاكرة
    stats = memory_manager.get_memory_stats()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("إجمالي الذكريات", stats.get('total_memories', 0))
    
    with col2:
        st.metric("ذكريات العملاء", stats.get('customer_memories', 0))
    
    with col3:
        st.metric("أنماط السلوك", stats.get('behavior_patterns', 0))
    
    with col4:
        st.metric("التفاعلات", stats.get('interactions', 0))
    
    # الذكريات الأكثر وصولاً
    if stats.get('most_accessed'):
        st.subheader("🔥 الذكريات الأكثر وصولاً")
        most_accessed_df = pd.DataFrame(
            stats['most_accessed'], 
            columns=['الفئة', 'المفتاح', 'عدد الوصول']
        )
        st.dataframe(most_accessed_df)

def ai_pricing_calculator():
    """حاسبة التسعير الذكية"""
    st.subheader("💰 حاسبة التسعير الذكية")
    
    if not init_ai_system():
        return
    
    pricing_assistant = st.session_state.ai_system['pricing_assistant']
    
    # اختيار نوع الحساب
    calc_type = st.selectbox(
        "نوع الحساب",
        ["حساب سعر الذهب", "حساب سعر الأحجار", "حساب سعر الخدمات"]
    )
    
    if calc_type == "حساب سعر الذهب":
        col1, col2, col3 = st.columns(3)
        
        with col1:
            weight = st.number_input("الوزن (جرام)", min_value=0.1, value=10.0, step=0.1)
        
        with col2:
            karat = st.selectbox("العيار", [18, 21, 22, 24])
        
        with col3:
            gold_price = st.number_input("سعر الذهب الحالي", min_value=1000, value=3200, step=50)
        
        if st.button("حساب السعر"):
            result = pricing_assistant.calculate_gold_price(weight, karat, gold_price)
            
            if 'error' not in result:
                st.success(f"السعر النهائي: {result['final_price']:.2f} جنيه")
                
                # تفاصيل الحساب
                with st.expander("تفاصيل الحساب"):
                    st.write(f"الوزن: {result['weight']} جرام")
                    st.write(f"العيار: {result['karat']} قيراط")
                    st.write(f"النقاء: {result['purity']:.1%}")
                    st.write(f"السعر الأساسي: {result['base_price']:.2f} جنيه")
                    st.write(f"هامش الربح: {result['margin_percentage']:.1f}%")
                    st.write(f"مبلغ الهامش: {result['margin_amount']:.2f} جنيه")
            else:
                st.error(f"خطأ في الحساب: {result['error']}")
    
    elif calc_type == "حساب سعر الأحجار":
        col1, col2, col3 = st.columns(3)
        
        with col1:
            stone_type = st.selectbox("نوع الحجر", ["diamond", "emerald", "ruby", "sapphire"])
        
        with col2:
            stone_weight = st.number_input("الوزن (قيراط)", min_value=0.1, value=1.0, step=0.1)
        
        with col3:
            quality = st.selectbox("درجة الجودة", [1, 2, 3, 4, 5])
        
        if st.button("حساب سعر الحجر"):
            result = pricing_assistant.calculate_stone_price(stone_type, stone_weight, quality)
            
            if 'error' not in result:
                st.success(f"السعر الإجمالي: {result['total_price']:.2f} جنيه")
                
                with st.expander("تفاصيل الحساب"):
                    st.write(f"نوع الحجر: {result['stone_type']}")
                    st.write(f"الوزن: {result['weight']} قيراط")
                    st.write(f"درجة الجودة: {result['quality_grade']}")
                    st.write(f"السعر لكل قيراط: {result['price_per_carat']:.2f} جنيه")
            else:
                st.error(f"خطأ في الحساب: {result['error']}")

def ai_customer_insights():
    """رؤى العملاء الذكية"""
    st.subheader("👥 رؤى العملاء الذكية")
    
    if not init_ai_system():
        return
    
    customer_advisor = st.session_state.ai_system['customer_advisor']
    memory_manager = st.session_state.ai_system['memory_manager']
    
    # إدخال معرف العميل
    customer_id = st.number_input("معرف العميل", min_value=1, value=1)
    
    if st.button("تحليل العميل"):
        # الحصول على ذكريات العميل
        memories = memory_manager.get_customer_memories(customer_id)
        
        if memories:
            st.write(f"**عدد التفاعلات:** {len(memories)}")
            
            # عرض الذكريات
            with st.expander("تاريخ التفاعلات"):
                for memory in memories[:5]:  # أحدث 5 تفاعلات
                    st.write(f"**{memory['type']}:** {memory['content']}")
                    st.write(f"*{memory['created_at']}*")
                    st.divider()
            
            # تحليل مرحلة دورة الحياة
            lifecycle = customer_advisor.get_customer_lifecycle_stage(customer_id)
            if 'error' not in lifecycle:
                st.info(f"**مرحلة العميل:** {lifecycle['stage']}")
                st.write(lifecycle['description'])
                
                if lifecycle['recommendations']:
                    st.write("**التوصيات:**")
                    for rec in lifecycle['recommendations']:
                        st.write(f"• {rec}")
        else:
            st.warning("لا توجد بيانات لهذا العميل")

def ai_system_status():
    """حالة النظام"""
    st.subheader("⚙️ حالة نظام الذكاء الاصطناعي")
    
    if not init_ai_system():
        return
    
    # فحص المكونات
    components = {
        "مدير الذاكرة": st.session_state.ai_system['memory_manager'],
        "الوكيل الذكي": st.session_state.ai_system['ai_agent'],
        "محلل البيانات": st.session_state.ai_system['data_analyst'],
        "مستشار العملاء": st.session_state.ai_system['customer_advisor'],
        "مدير المخزون": st.session_state.ai_system['inventory_manager'],
        "مساعد التسعير": st.session_state.ai_system['pricing_assistant']
    }
    
    st.write("**حالة المكونات:**")
    for name, component in components.items():
        status = "🟢 نشط" if component else "🔴 غير نشط"
        st.write(f"{name}: {status}")
    
    # إحصائيات الأداء
    memory_manager = st.session_state.ai_system['memory_manager']
    stats = memory_manager.get_memory_stats()
    
    st.write("**إحصائيات الأداء:**")
    st.json(stats)

# دوال مساعدة للتكامل مع التطبيق الرئيسي
def get_ai_recommendations(context: dict):
    """الحصول على توصيات ذكية"""
    if not init_ai_system():
        return []
    
    ai_agent = st.session_state.ai_system['ai_agent']
    return ai_agent.get_smart_suggestions(context)

def analyze_sales_data(sales_data: list):
    """تحليل بيانات المبيعات"""
    if not init_ai_system():
        return {}
    
    data_analyst = st.session_state.ai_system['data_analyst']
    return data_analyst.analyze_sales_trends(sales_data)

def get_pricing_suggestion(item_details: dict):
    """الحصول على اقتراح تسعير"""
    if not init_ai_system():
        return {}
    
    pricing_assistant = st.session_state.ai_system['pricing_assistant']
    return pricing_assistant.suggest_optimal_pricing(item_details)

# تصدير الدوال للاستخدام في التطبيق الرئيسي
__all__ = [
    'ai_chat_interface',
    'ai_insights_dashboard', 
    'ai_pricing_calculator',
    'ai_customer_insights',
    'ai_system_status',
    'get_ai_recommendations',
    'analyze_sales_data',
    'get_pricing_suggestion'
]
