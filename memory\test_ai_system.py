#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الذاكرة والذكاء الاصطناعي
Test Memory and AI System
"""

import os
import sys
import json
from datetime import datetime

# إضافة المسار للوصول للمكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from memory.core.memory_manager import MemoryManager
from memory.core.ai_agent import AIAgent
from memory.agents.data_analyst import DataAnalyst
from memory.agents.customer_advisor import CustomerAdvisor
from memory.agents.inventory_manager import InventoryManager
from memory.agents.pricing_assistant import PricingAssistant
from memory.api.ai_interface import AIInterface

class AISystemTester:
    """فئة اختبار نظام الذكاء الاصطناعي"""
    
    def __init__(self):
        """تهيئة المختبر"""
        self.test_results = []
        print("🧪 بدء اختبار نظام الذاكرة والذكاء الاصطناعي")
        print("=" * 60)
    
    def run_test(self, test_name: str, test_function):
        """تشغيل اختبار واحد"""
        print(f"\n🔍 {test_name}...")
        try:
            result = test_function()
            if result:
                print(f"✅ {test_name} - نجح")
                self.test_results.append((test_name, True, None))
                return True
            else:
                print(f"❌ {test_name} - فشل")
                self.test_results.append((test_name, False, "Test returned False"))
                return False
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
            self.test_results.append((test_name, False, str(e)))
            return False
    
    def test_memory_manager(self):
        """اختبار مدير الذاكرة"""
        try:
            memory = MemoryManager()
            
            # اختبار تخزين الذكريات
            success = memory.store_memory(
                'test', 'sample_key', 
                {'data': 'test_value', 'number': 123},
                importance=1.5
            )
            if not success:
                return False
            
            # اختبار استرجاع الذكريات
            retrieved = memory.retrieve_memory('test', 'sample_key')
            if not retrieved or retrieved['data'] != 'test_value':
                return False
            
            # اختبار ذكريات العملاء
            customer_success = memory.store_customer_memory(
                1, 'preference', 'يفضل الذهب عيار 21', 
                context='اختبار', sentiment=0.8
            )
            if not customer_success:
                return False
            
            # اختبار أنماط السلوك
            pattern_success = memory.store_behavior_pattern(
                'purchase_pattern', 
                {'item': 'خاتم', 'frequency': 'شهري'},
                confidence=0.7
            )
            
            print("  📊 تم اختبار جميع وظائف مدير الذاكرة بنجاح")
            return pattern_success
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار مدير الذاكرة: {e}")
            return False
    
    def test_ai_agent(self):
        """اختبار الوكيل الذكي الرئيسي"""
        try:
            memory = MemoryManager()
            agent = AIAgent(memory)
            
            # اختبار معالجة المدخلات المختلفة
            test_inputs = [
                "ما هو سعر الذهب اليوم؟",
                "أريد إنشاء فاتورة جديدة",
                "كيف يمكنني إضافة عميل؟",
                "اقترح لي تصميم خاتم للزفاف"
            ]
            
            for user_input in test_inputs:
                response = agent.process_user_input(user_input)
                if not response or 'message' not in response:
                    return False
                print(f"  💬 '{user_input[:30]}...' -> {response['type']}")
            
            # اختبار الاقتراحات الذكية
            suggestions = agent.get_smart_suggestions()
            if not isinstance(suggestions, list):
                return False
            
            # اختبار تحليل الاتجاهات
            trends = agent.analyze_business_trends()
            if not isinstance(trends, dict):
                return False
            
            print("  🤖 تم اختبار جميع وظائف الوكيل الذكي بنجاح")
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار الوكيل الذكي: {e}")
            return False
    
    def test_data_analyst(self):
        """اختبار محلل البيانات"""
        try:
            memory = MemoryManager()
            analyst = DataAnalyst(memory)
            
            # بيانات تجريبية للمبيعات
            sample_sales = [
                {
                    'invoice_date': '2024-01-15',
                    'total_amount': 15000,
                    'gold_weight': 10.5,
                    'gold_karat': 21,
                    'stone_weight': 2.0
                },
                {
                    'invoice_date': '2024-01-20',
                    'total_amount': 25000,
                    'gold_weight': 15.0,
                    'gold_karat': 18,
                    'stone_weight': 1.5
                }
            ]
            
            # اختبار تحليل اتجاهات المبيعات
            sales_analysis = analyst.analyze_sales_trends(sample_sales)
            if 'error' in sales_analysis:
                return False
            
            # اختبار التنبؤ بالطلب
            demand_prediction = analyst.predict_demand(sample_sales, 30)
            if 'error' in demand_prediction:
                return False
            
            print("  📈 تم اختبار جميع وظائف محلل البيانات بنجاح")
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار محلل البيانات: {e}")
            return False
    
    def test_customer_advisor(self):
        """اختبار مستشار العملاء"""
        try:
            memory = MemoryManager()
            advisor = CustomerAdvisor(memory)
            
            # إضافة بعض ذكريات العملاء للاختبار
            memory.store_customer_memory(
                1, 'preference', 'يحب الذهب عيار 21', 
                context='محادثة', sentiment=0.7
            )
            memory.store_customer_memory(
                1, 'purchase', 'اشترى خاتم زفاف', 
                context='فاتورة', sentiment=0.9
            )
            
            # اختبار التوصيات المخصصة
            recommendations = advisor.get_personalized_recommendations(
                1, {'occasion': 'زفاف', 'budget': 50000}
            )
            if 'error' in recommendations:
                return False
            
            # اختبار تحليل رضا العميل
            satisfaction = advisor.analyze_customer_satisfaction(1)
            if 'error' in satisfaction:
                return False
            
            # اختبار مرحلة دورة حياة العميل
            lifecycle = advisor.get_customer_lifecycle_stage(1)
            if 'error' in lifecycle:
                return False
            
            print("  👥 تم اختبار جميع وظائف مستشار العملاء بنجاح")
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار مستشار العملاء: {e}")
            return False
    
    def test_inventory_manager(self):
        """اختبار مدير المخزون"""
        try:
            memory = MemoryManager()
            inventory_mgr = InventoryManager(memory)
            
            # بيانات مخزون تجريبية
            sample_inventory = {
                'gold_21k': {'quantity': 150, 'type': 'gold_21'},
                'gold_18k': {'quantity': 80, 'type': 'gold_18'},
                'diamonds': {'quantity': 25, 'type': 'diamond'}
            }
            
            # اختبار تحليل حالة المخزون
            inventory_analysis = inventory_mgr.analyze_inventory_status(sample_inventory)
            if 'error' in inventory_analysis:
                return False
            
            # اختبار التنبؤ بالطلب
            sample_sales = [
                {'gold_weight': 10, 'gold_karat': 21, 'stone_weight': 1},
                {'gold_weight': 15, 'gold_karat': 18, 'stone_weight': 2}
            ]
            demand_prediction = inventory_mgr.predict_demand(sample_sales, 30)
            if 'error' in demand_prediction:
                return False
            
            print("  📦 تم اختبار جميع وظائف مدير المخزون بنجاح")
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار مدير المخزون: {e}")
            return False
    
    def test_pricing_assistant(self):
        """اختبار مساعد التسعير"""
        try:
            memory = MemoryManager()
            pricing = PricingAssistant(memory)
            
            # اختبار حساب سعر الذهب
            gold_price = pricing.calculate_gold_price(
                weight=10.5, karat=21, 
                current_gold_price=3200, pricing_type='retail'
            )
            if 'error' in gold_price:
                return False
            
            # اختبار حساب سعر الأحجار
            stone_price = pricing.calculate_stone_price(
                'diamond', weight=2.0, quality_grade=4
            )
            if 'error' in stone_price:
                return False
            
            # اختبار حساب سعر الخدمات
            service_price = pricing.calculate_service_price(
                'design', {'complexity': 3}
            )
            if 'error' in service_price:
                return False
            
            # اختبار حساب الخصومات
            discounts = pricing.calculate_discounts(
                {'total_purchases': 10}, order_amount=75000
            )
            if 'error' in discounts:
                return False
            
            print("  💰 تم اختبار جميع وظائف مساعد التسعير بنجاح")
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار مساعد التسعير: {e}")
            return False
    
    def test_ai_interface(self):
        """اختبار واجهة الذكاء الاصطناعي"""
        try:
            # ملاحظة: هذا الاختبار مبسط لأن AIInterface يتطلب Streamlit
            from memory.api.ai_interface import AIInterface
            
            # اختبار إنشاء الواجهة
            interface = AIInterface()
            if not interface:
                return False
            
            # اختبار المكونات الأساسية
            if not interface.memory_manager:
                return False
            if not interface.ai_agent:
                return False
            if not interface.data_analyst:
                return False
            
            print("  🌐 تم اختبار واجهة الذكاء الاصطناعي بنجاح")
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار واجهة الذكاء الاصطناعي: {e}")
            return False
    
    def test_integration(self):
        """اختبار التكامل بين المكونات"""
        try:
            memory = MemoryManager()
            agent = AIAgent(memory)
            
            # اختبار سيناريو متكامل
            # 1. إضافة ذكرى عميل
            memory.store_customer_memory(
                123, 'inquiry', 'سأل عن أسعار الذهب عيار 21'
            )
            
            # 2. معالجة استفسار
            response = agent.process_user_input(
                "ما هو أفضل عيار ذهب للزفاف؟",
                context={'customer_id': 123}
            )
            
            # 3. التحقق من الرد
            if not response or 'message' not in response:
                return False
            
            # 4. التحقق من تخزين التفاعل
            stats = memory.get_memory_stats()
            if stats['interactions'] == 0:
                return False
            
            print("  🔗 تم اختبار التكامل بين المكونات بنجاح")
            return True
            
        except Exception as e:
            print(f"  ❌ خطأ في اختبار التكامل: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        tests = [
            ("اختبار مدير الذاكرة", self.test_memory_manager),
            ("اختبار الوكيل الذكي", self.test_ai_agent),
            ("اختبار محلل البيانات", self.test_data_analyst),
            ("اختبار مستشار العملاء", self.test_customer_advisor),
            ("اختبار مدير المخزون", self.test_inventory_manager),
            ("اختبار مساعد التسعير", self.test_pricing_assistant),
            ("اختبار واجهة الذكاء الاصطناعي", self.test_ai_interface),
            ("اختبار التكامل", self.test_integration)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            if self.run_test(test_name, test_func):
                passed += 1
        
        # عرض النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 نتائج اختبار نظام الذكاء الاصطناعي")
        print("=" * 60)
        
        for test_name, success, error in self.test_results:
            status = "✅ نجح" if success else "❌ فشل"
            print(f"{status} - {test_name}")
            if error:
                print(f"    خطأ: {error}")
        
        print("\n" + "=" * 60)
        print(f"📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
        
        if passed == total:
            print("🎉 جميع الاختبارات نجحت! نظام الذكاء الاصطناعي جاهز للاستخدام")
            return True
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
            return False

if __name__ == "__main__":
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    os.makedirs('memory/logs', exist_ok=True)
    
    # تشغيل جميع الاختبارات
    tester = AISystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🚀 نظام الذكاء الاصطناعي جاهز للتشغيل!")
    else:
        print("\n🔧 يحتاج النظام إلى مراجعة وإصلاح")
    
    sys.exit(0 if success else 1)
