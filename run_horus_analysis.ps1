# 🤖 سكريبت تشغيل HORUS لفحص مشروع Crestal Diamond
# HORUS Analysis Script for Crestal Diamond Project

param(
    [switch]$StartHorus,
    [switch]$AnalyzeOnly,
    [switch]$FullAnalysis,
    [string]$OutputDir = "horus_analysis_results"
)

# إعداد الألوان
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Cyan = "Cyan"
$Blue = "Blue"

# مسارات المشروع
$HorusPath = "C:\Users\<USER>\HORUS-PROJECT\nodejs-version"
$ProjectPath = "C:\Users\<USER>\company app 1"
$HorusUrl = "http://localhost:8000"

# إنشاء مجلد النتائج
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
    Write-Host "📁 تم إنشاء مجلد النتائج: $OutputDir" -ForegroundColor $Green
}

function Start-HorusServer {
    Write-Host "🚀 بدء تشغيل HORUS..." -ForegroundColor $Green
    
    # التحقق من وجود مجلد HORUS
    if (!(Test-Path $HorusPath)) {
        Write-Host "❌ مجلد HORUS غير موجود: $HorusPath" -ForegroundColor $Red
        return $false
    }
    
    # تشغيل HORUS في نافذة منفصلة
    $process = Start-Process -FilePath "powershell" -ArgumentList "-Command", "cd '$HorusPath'; npm start" -PassThru -WindowStyle Normal
    
    Write-Host "⏳ انتظار تشغيل الخادم..." -ForegroundColor $Yellow
    Start-Sleep -Seconds 15
    
    # فحص حالة الخادم
    try {
        $response = Invoke-RestMethod -Uri "$HorusUrl/api/status" -Method GET -TimeoutSec 5
        if ($response.status -eq "ok") {
            Write-Host "✅ HORUS جاهز للعمل!" -ForegroundColor $Green
            return $true
        }
    }
    catch {
        Write-Host "❌ فشل في الاتصال بـ HORUS. تأكد من تشغيل Ollama وتوفر نموذج Mistral" -ForegroundColor $Red
        return $false
    }
    
    return $false
}

function Test-HorusConnection {
    try {
        $response = Invoke-RestMethod -Uri "$HorusUrl/api/status" -Method GET -TimeoutSec 5
        return $response.status -eq "ok"
    }
    catch {
        return $false
    }
}

function Invoke-HorusFileAnalysis {
    param([string]$FilePath, [string]$OutputFile)
    
    Write-Host "🔍 تحليل ملف: $FilePath" -ForegroundColor $Cyan
    
    $body = @{
        filePath = $FilePath
        includeAI = $true
    } | ConvertTo-Json
    
    try {
        $result = Invoke-RestMethod -Uri "$HorusUrl/api/analyze/path" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 60
        $result | ConvertTo-Json -Depth 10 | Out-File -FilePath "$OutputDir\$OutputFile" -Encoding UTF8
        Write-Host "✅ تم حفظ التحليل في: $OutputDir\$OutputFile" -ForegroundColor $Green
        return $true
    }
    catch {
        Write-Host "❌ فشل في تحليل الملف: $FilePath" -ForegroundColor $Red
        Write-Host "الخطأ: $($_.Exception.Message)" -ForegroundColor $Red
        return $false
    }
}

function Invoke-HorusDirectoryAnalysis {
    param([string]$DirectoryPath, [string]$OutputFile, [int]$MaxFiles = 20)
    
    Write-Host "📂 تحليل مجلد: $DirectoryPath" -ForegroundColor $Cyan
    
    $body = @{
        directoryPath = $DirectoryPath
        recursive = $true
        maxFiles = $MaxFiles
        includeAI = $true
    } | ConvertTo-Json
    
    try {
        $result = Invoke-RestMethod -Uri "$HorusUrl/api/analyze/directory" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 120
        $result | ConvertTo-Json -Depth 10 | Out-File -FilePath "$OutputDir\$OutputFile" -Encoding UTF8
        Write-Host "✅ تم حفظ التحليل في: $OutputDir\$OutputFile" -ForegroundColor $Green
        return $true
    }
    catch {
        Write-Host "❌ فشل في تحليل المجلد: $DirectoryPath" -ForegroundColor $Red
        Write-Host "الخطأ: $($_.Exception.Message)" -ForegroundColor $Red
        return $false
    }
}

function Invoke-HorusChat {
    param([string]$Message, [string]$SessionId, [string]$OutputFile)
    
    Write-Host "💬 استعلام HORUS: $SessionId" -ForegroundColor $Blue
    
    $body = @{
        message = $Message
        sessionId = $SessionId
        model = "mistral:7b"
        stream = $false
    } | ConvertTo-Json
    
    try {
        $result = Invoke-RestMethod -Uri "$HorusUrl/api/chat" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 180
        $result | ConvertTo-Json -Depth 10 | Out-File -FilePath "$OutputDir\$OutputFile" -Encoding UTF8
        Write-Host "✅ تم حفظ الاستعلام في: $OutputDir\$OutputFile" -ForegroundColor $Green
        return $true
    }
    catch {
        Write-Host "❌ فشل في الاستعلام: $SessionId" -ForegroundColor $Red
        Write-Host "الخطأ: $($_.Exception.Message)" -ForegroundColor $Red
        return $false
    }
}

# البدء في التنفيذ
Write-Host "🤖 سكريبت تحليل HORUS لمشروع Crestal Diamond" -ForegroundColor $Green
Write-Host "=" * 60 -ForegroundColor $Green

# تشغيل HORUS إذا طُلب ذلك
if ($StartHorus) {
    if (!(Start-HorusServer)) {
        Write-Host "❌ فشل في تشغيل HORUS. توقف التنفيذ." -ForegroundColor $Red
        exit 1
    }
}

# التحقق من الاتصال
if (!(Test-HorusConnection)) {
    Write-Host "❌ HORUS غير متصل. تأكد من تشغيله أولاً باستخدام: .\run_horus_analysis.ps1 -StartHorus" -ForegroundColor $Red
    exit 1
}

Write-Host "🔍 بدء التحليل الشامل..." -ForegroundColor $Cyan

# تحليل الملفات الرئيسية
$mainFiles = @(
    @{ Path = "$ProjectPath\main.py"; Output = "main_py_analysis.json" },
    @{ Path = "$ProjectPath\src\invoice_app.py"; Output = "invoice_app_analysis.json" }
)

foreach ($file in $mainFiles) {
    Invoke-HorusFileAnalysis -FilePath $file.Path -OutputFile $file.Output
    Start-Sleep -Seconds 2
}

# تحليل المجلدات الرئيسية
$directories = @(
    @{ Path = "$ProjectPath\src\pages"; Output = "pages_directory_analysis.json"; MaxFiles = 20 },
    @{ Path = "$ProjectPath\database"; Output = "database_directory_analysis.json"; MaxFiles = 15 },
    @{ Path = "$ProjectPath\memory"; Output = "memory_directory_analysis.json"; MaxFiles = 25 },
    @{ Path = "$ProjectPath\config"; Output = "config_directory_analysis.json"; MaxFiles = 10 }
)

foreach ($dir in $directories) {
    Invoke-HorusDirectoryAnalysis -DirectoryPath $dir.Path -OutputFile $dir.Output -MaxFiles $dir.MaxFiles
    Start-Sleep -Seconds 3
}

# الاستعلامات المتخصصة
if ($FullAnalysis) {
    Write-Host "🔒 بدء التحليل المتخصص..." -ForegroundColor $Yellow
    
    $queries = @(
        @{
            Message = "قم بتحليل الأمان في مشروع Crestal Diamond. ركز على: 1) حماية البيانات المالية 2) أمان قاعدة البيانات 3) حماية معلومات العملاء 4) الثغرات الأمنية المحتملة"
            SessionId = "security-analysis"
            Output = "security_analysis.json"
        },
        @{
            Message = "حلل أداء مشروع Crestal Diamond. ركز على: 1) سرعة تحميل الصفحات 2) كفاءة استعلامات قاعدة البيانات 3) استهلاك الذاكرة 4) الاختناقات المحتملة"
            SessionId = "performance-analysis"
            Output = "performance_analysis.json"
        },
        @{
            Message = "قيم جودة الكود في مشروع Crestal Diamond. ركز على: 1) بنية الكود وتنظيمه 2) التعليقات والتوثيق 3) معالجة الأخطاء 4) قابلية الصيانة والتوسع"
            SessionId = "code-quality-analysis"
            Output = "code_quality_analysis.json"
        }
    )
    
    foreach ($query in $queries) {
        Invoke-HorusChat -Message $query.Message -SessionId $query.SessionId -OutputFile $query.Output
        Start-Sleep -Seconds 5
    }
}

# إنشاء تقرير ملخص
$summaryReport = @"
# 📊 ملخص تحليل HORUS لمشروع Crestal Diamond

## 📅 تاريخ التحليل: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

## 📁 الملفات المحللة:
$(Get-ChildItem $OutputDir -Filter "*.json" | ForEach-Object { "- $($_.Name)" } | Out-String)

## 🔍 نوع التحليل:
- تحليل الملفات الرئيسية: ✅
- تحليل المجلدات: ✅
- التحليل المتخصص: $(if($FullAnalysis){'✅'}else{'❌'})

## 📊 إحصائيات:
- عدد الملفات المحللة: $($mainFiles.Count)
- عدد المجلدات المحللة: $($directories.Count)
- عدد الاستعلامات المتخصصة: $(if($FullAnalysis){$queries.Count}else{0})

## 📞 للمراجعة:
راجع الملفات في مجلد: $OutputDir
"@

$summaryReport | Out-File -FilePath "$OutputDir\analysis_summary.md" -Encoding UTF8

Write-Host "🎉 تم الانتهاء من التحليل!" -ForegroundColor $Green
Write-Host "📁 النتائج محفوظة في: $OutputDir" -ForegroundColor $Green
Write-Host "📊 راجع ملف الملخص: $OutputDir\analysis_summary.md" -ForegroundColor $Cyan
