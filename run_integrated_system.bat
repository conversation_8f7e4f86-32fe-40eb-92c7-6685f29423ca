@echo off
echo 🚀 تشغيل النظام المتكامل مع الذكاء الاصطناعي
echo ================================================

echo 🧪 فحص النظام الأساسي...
python test_main.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 🧠 فحص نظام الذكاء الاصطناعي...
    python memory/simple_test.py
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✅ جميع الأنظمة جاهزة!
        echo 🚀 تشغيل النظام المتكامل...
        echo.
        echo 🌐 سيتم فتح المتصفح على:
        echo    http://localhost:8501
        echo.
        echo 📋 الصفحات المتاحة:
        echo    1. 🤖 كريستال - المساعد الذكي
        echo    2. 📄 إنشاء فاتورة جديدة (مع ميزات ذكية)
        echo    3. 📊 عرض الفواتير المحفوظة
        echo    4. 👥 حسابات العملاء (مع تحليل ذكي)
        echo    5. 🔍 تحليل ملفات Excel
        echo    6. 📈 إحصائيات وتقارير (مع رؤى ذكية)
        echo    7. ⚙️ الإعدادات (مع إعدادات الذكاء الاصطناعي)
        echo.
        streamlit run main.py
    ) else (
        echo.
        echo ❌ مشكلة في نظام الذكاء الاصطناعي
        echo 🔧 محاولة إعداد النظام...
        python memory/setup_ai_system.py
        
        if %ERRORLEVEL% EQU 0 (
            echo ✅ تم إعداد النظام بنجاح!
            echo 🚀 تشغيل النظام...
            streamlit run main.py
        ) else (
            echo ❌ فشل في إعداد نظام الذكاء الاصطناعي
            echo 📞 يرجى مراجعة التوثيق
            pause
        )
    )
) else (
    echo.
    echo ❌ مشكلة في النظام الأساسي
    echo 📞 يرجى مراجعة الأخطاء أعلاه
    pause
)

pause
