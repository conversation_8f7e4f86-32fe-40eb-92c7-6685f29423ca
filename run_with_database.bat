@echo off
chcp 65001 >nul
echo ========================================
echo 💎 تشغيل نظام إدارة فواتير الورشة مع قاعدة البيانات
echo 💎 Running Workshop Invoice System with Database
echo ========================================

echo.
echo 🔍 فحص المتطلبات...
echo 🔍 Checking requirements...

:: فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo ❌ Python is not installed
    pause
    exit /b 1
)

:: فحص Streamlit
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo ❌ Streamlit غير مثبت
    echo ❌ Streamlit is not installed
    echo 💡 قم بتشغيل: pip install streamlit
    pause
    exit /b 1
)

:: فحص مكتبات قاعدة البيانات
python -c "import mysql.connector" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ مكتبات MySQL غير مثبتة
    echo ⚠️ MySQL libraries not installed
    echo 🔄 سيتم استخدام نظام CSV
    echo 🔄 Will use CSV system
    goto :run_app
)

echo ✅ جميع المتطلبات متوفرة
echo ✅ All requirements available

echo.
echo 🧪 اختبار قاعدة البيانات...
echo 🧪 Testing database...

:: اختبار سريع لقاعدة البيانات
python -c "
try:
    from database_manager import DatabaseManager
    db = DatabaseManager(use_mysql=True)
    if db.use_mysql and db.test_connection():
        print('✅ قاعدة البيانات متصلة')
        print('✅ Database connected')
    else:
        print('⚠️ سيتم استخدام نظام CSV')
        print('⚠️ Will use CSV system')
    db.close_connection()
except Exception as e:
    print('⚠️ خطأ في قاعدة البيانات، سيتم استخدام CSV')
    print('⚠️ Database error, will use CSV')
"

:run_app
echo.
echo ========================================
echo 🚀 تشغيل التطبيق...
echo 🚀 Starting application...
echo ========================================

echo.
echo 📱 سيتم فتح التطبيق في المتصفح تلقائياً...
echo 📱 Application will open in browser automatically...
echo.
echo 🔗 أو افتح الرابط يدوياً: http://localhost:8501
echo 🔗 Or open manually: http://localhost:8501
echo.

:: تشغيل التطبيق
streamlit run invoice_app.py

echo.
echo ========================================
echo 👋 تم إغلاق التطبيق
echo 👋 Application closed
echo ========================================

pause
