#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إصلاح بيانات الفواتير
Fix Invoice Data Script

يقوم هذا السكريبت بإصلاح البيانات المختلطة في ملفات الفواتير
"""

import sys
import os
import pandas as pd
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def fix_invoice_data():
    """إصلاح بيانات الفواتير المختلطة"""
    print("🔧 بدء إصلاح بيانات الفواتير...")
    
    # مسارات الملفات
    main_file = "invoices.csv"
    data_file = "data/invoices.csv"
    backup_file = f"data/backups/invoices_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    try:
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        os.makedirs("data/backups", exist_ok=True)
        
        # قراءة الملف الرئيسي
        if os.path.exists(main_file):
            print(f"📖 قراءة الملف: {main_file}")
            df = pd.read_csv(main_file, encoding='utf-8-sig')
            
            # إنشاء نسخة احتياطية
            df.to_csv(backup_file, index=False, encoding='utf-8-sig')
            print(f"💾 تم إنشاء نسخة احتياطية: {backup_file}")
            
            # فحص البيانات
            print(f"📊 عدد الصفوف: {len(df)}")
            print(f"📊 الأعمدة: {list(df.columns)}")
            
            # التحقق من وجود مشاكل في البيانات
            problems_found = False
            
            # فحص العمود الثالث (description)
            if 'description' in df.columns:
                # البحث عن تواريخ في عمود description
                date_pattern = r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
                date_in_desc = df['description'].str.contains(date_pattern, na=False)
                
                if date_in_desc.any():
                    print("⚠️ تم العثور على تواريخ في عمود البيان!")
                    problems_found = True
                    
                    # إصلاح البيانات
                    print("🔧 إصلاح البيانات...")
                    
                    for idx, row in df.iterrows():
                        if pd.notna(row['description']) and pd.to_datetime(row['description'], errors='coerce') is not None:
                            # هذا timestamp في مكان خاطئ
                            # نحتاج لإعادة ترتيب البيانات
                            print(f"🔧 إصلاح الصف {idx + 1}")
                            
                            # إذا كان description يحتوي على timestamp
                            if len(str(row['description']).split()) == 2:  # تاريخ ووقت
                                # نقل timestamp إلى مكانه الصحيح
                                df.at[idx, 'timestamp'] = row['description']
                                # وضع وصف افتراضي
                                df.at[idx, 'description'] = f"فاتورة {row['customer_name']}"
            
            # التحقق من التواريخ
            if 'date' in df.columns:
                # التأكد من صحة التواريخ
                df['date'] = pd.to_datetime(df['date'], errors='coerce').dt.strftime('%Y-%m-%d')
            
            if 'timestamp' in df.columns:
                # التأكد من صحة timestamps
                df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # ترتيب الأعمدة بالشكل الصحيح
            correct_columns = ['customer_name', 'date', 'description', 'gold_change', 'usd_change', 'egp_change', 'timestamp']
            df = df.reindex(columns=correct_columns)
            
            # حفظ البيانات المصححة
            df.to_csv(main_file, index=False, encoding='utf-8-sig')
            print(f"✅ تم حفظ البيانات المصححة في: {main_file}")
            
            # نسخ إلى مجلد data
            df.to_csv(data_file, index=False, encoding='utf-8-sig')
            print(f"✅ تم نسخ البيانات إلى: {data_file}")
            
            # عرض عينة من البيانات المصححة
            print("\n📋 عينة من البيانات المصححة:")
            print(df.head().to_string(index=False))
            
            if problems_found:
                print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
            else:
                print("\n✅ لم يتم العثور على مشاكل في البيانات")
                
        else:
            print(f"❌ الملف غير موجود: {main_file}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {str(e)}")
        return False

def validate_data():
    """التحقق من صحة البيانات بعد الإصلاح"""
    print("\n🔍 التحقق من صحة البيانات...")
    
    try:
        df = pd.read_csv("invoices.csv", encoding='utf-8-sig')
        
        # فحص الأعمدة المطلوبة
        required_columns = ['customer_name', 'date', 'description', 'gold_change', 'usd_change', 'egp_change', 'timestamp']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ أعمدة مفقودة: {missing_columns}")
            return False
        
        # فحص البيانات الفارغة
        empty_data = df.isnull().sum()
        if empty_data.any():
            print("⚠️ بيانات فارغة موجودة:")
            for col, count in empty_data.items():
                if count > 0:
                    print(f"  - {col}: {count} صف فارغ")
        
        # فحص التواريخ
        try:
            pd.to_datetime(df['date'])
            print("✅ تواريخ الفواتير صحيحة")
        except:
            print("❌ مشكلة في تواريخ الفواتير")
            return False
        
        try:
            pd.to_datetime(df['timestamp'])
            print("✅ timestamps صحيحة")
        except:
            print("❌ مشكلة في timestamps")
            return False
        
        # فحص الأرقام
        numeric_columns = ['gold_change', 'usd_change', 'egp_change']
        for col in numeric_columns:
            try:
                pd.to_numeric(df[col])
                print(f"✅ {col} صحيح")
            except:
                print(f"❌ مشكلة في {col}")
                return False
        
        print("🎉 جميع البيانات صحيحة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 سكريبت إصلاح بيانات الفواتير")
    print("=" * 50)
    
    # إصلاح البيانات
    if fix_invoice_data():
        # التحقق من صحة البيانات
        if validate_data():
            print("\n🎉 تم إصلاح البيانات بنجاح!")
            print("💡 يمكنك الآن تشغيل التطبيق بأمان")
        else:
            print("\n⚠️ تم الإصلاح ولكن هناك تحذيرات")
    else:
        print("\n❌ فشل في إصلاح البيانات")
    
    print("\n📞 للمساعدة، راجع README.md أو اتصل بالدعم")
