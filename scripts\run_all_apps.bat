@echo off
echo ========================================
echo تشغيل جميع تطبيقات Crestal Diamond
echo ========================================

echo.
echo جاري تشغيل التطبيقات...

echo.
echo 1. التطبيق الرئيسي الكامل (المنفذ 8501)
start "التطبيق الرئيسي" cmd /k "streamlit run invoice_app.py --server.port 8501"

timeout /t 3 /nobreak >nul

echo 2. محلل ملفات Excel المنفصل (المنفذ 8503)
start "محلل Excel" cmd /k "streamlit run excel_analyzer.py --server.port 8503"

echo.
echo ========================================
echo تم تشغيل جميع التطبيقات بنجاح!
echo ========================================

echo.
echo الروابط:
echo 1. التطبيق الرئيسي الكامل (جميع الصفحات): http://localhost:8501
echo 2. محلل Excel المنفصل: http://localhost:8503
echo.
echo ملاحظة: التطبيق الرئيسي يحتوي على جميع الصفحات بما في ذلك حسابات العملاء

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
