@echo off
echo ========================================
echo 🚀 تشغيل تطبيق إدارة الورشة المنظم
echo ========================================
echo.

REM التحقق من وجود البيئة الافتراضية
if exist ".venv\Scripts\activate.bat" (
    echo ✅ تم العثور على البيئة الافتراضية
    call .venv\Scripts\activate.bat
) else (
    echo ⚠️  لم يتم العثور على البيئة الافتراضية
    echo يرجى تشغيل: python -m venv .venv
    echo ثم: .venv\Scripts\activate.bat
    echo ثم: pip install -r config\requirements.txt
    pause
    exit /b 1
)

echo.
echo 📂 التحقق من هيكل المشروع المنظم...

REM التحقق من وجود الملفات الأساسية
if not exist "main.py" (
    echo ❌ الملف الرئيسي main.py غير موجود
    pause
    exit /b 1
)

if not exist "src\" (
    echo ❌ مجلد src غير موجود
    pause
    exit /b 1
)

if not exist "data\" (
    echo ⚠️  مجلد data غير موجود - سيتم إنشاؤه
    mkdir data
    mkdir data\backups
    mkdir data\customers
    mkdir data\exports
    mkdir data\invoices
)

if not exist "logs\" (
    echo ⚠️  مجلد logs غير موجود - سيتم إنشاؤه
    mkdir logs
)

echo ✅ هيكل المشروع صحيح
echo.

echo 🔄 بدء تشغيل التطبيق...
echo يمكنك الوصول للتطبيق على: http://localhost:8501
echo.
echo للإيقاف: اضغط Ctrl+C
echo ========================================

REM تشغيل التطبيق
streamlit run main.py

echo.
echo 👋 تم إيقاف التطبيق
pause
