@echo off
echo 🧠 تشغيل نظام ورشة الماس مع الذكاء الاصطناعي
echo ================================================

echo 🔧 فحص نظام الذكاء الاصطناعي...
python memory/simple_test.py

if %ERRORLEVEL% EQU 0 (
    echo ✅ نظام الذكاء الاصطناعي جاهز!
    echo 🚀 تشغيل التطبيق الرئيسي...
    streamlit run invoice_app.py
) else (
    echo ❌ مشكلة في نظام الذكاء الاصطناعي
    echo 🔧 محاولة إعداد النظام...
    python memory/setup_ai_system.py
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم إعداد النظام بنجاح!
        echo 🚀 تشغيل التطبيق الرئيسي...
        streamlit run invoice_app.py
    ) else (
        echo ❌ فشل في إعداد النظام
        echo 📞 يرجى مراجعة التوثيق أو الاتصال بالدعم
        pause
    )
)

pause
