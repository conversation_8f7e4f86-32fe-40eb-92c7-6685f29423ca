@echo off
chcp 65001 >nul
echo ========================================
echo 🗄️ إعداد قاعدة البيانات لنظام إدارة الفواتير
echo 🗄️ Database Setup for Invoice Management System
echo ========================================

echo.
echo الخطوة 1: تثبيت المكتبات المطلوبة...
echo Step 1: Installing required libraries...
echo.

call install_mysql_requirements.bat

echo.
echo ========================================
echo الخطوة 2: إنشاء ملف الإعدادات...
echo Step 2: Creating configuration file...
echo ========================================

python database_config.py

echo.
echo ========================================
echo الخطوات التالية يدوياً:
echo Next manual steps:
echo ========================================

echo.
echo 📋 يرجى اتباع الخطوات التالية:
echo 📋 Please follow these steps:
echo.
echo 1. افتح MySQL Workbench
echo    Open MySQL Workbench
echo.
echo 2. اتصل بخادم MySQL المحلي
echo    Connect to local MySQL server
echo.
echo 3. افتح ملف database_setup.sql وقم بتشغيله
echo    Open database_setup.sql file and execute it
echo.
echo 4. قم بتحديث كلمة المرور في ملف .env
echo    Update password in .env file
echo.
echo 5. قم بتشغيل التطبيق للاختبار:
echo    Run the application to test:
echo    streamlit run invoice_app.py
echo.

echo ========================================
echo 📖 للمزيد من التفاصيل، راجع:
echo 📖 For more details, check:
echo DATABASE_SETUP_GUIDE.md
echo ========================================

pause
