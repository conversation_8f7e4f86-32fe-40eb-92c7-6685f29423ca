"""
مدير قاعدة البيانات - Database Manager
"""

import os
import pandas as pd
from datetime import datetime
from typing import Dict, Any

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'backup_enabled': True,
    'backup_folder': 'backups',
    'encoding': 'utf-8-sig'
}

# إعدادات التصدير
EXPORT_CONFIG = {
    'datetime_format': '%Y-%m-%d %H:%M:%S',
    'filename_format': '{type}_{timestamp}'
}


class DatabaseManager:
    """مدير قاعدة البيانات الموحد"""

    def __init__(self):
        self.invoices_file = 'invoices.csv'
        self.customers_file = 'customers.csv'
        self.backups_dir = 'backups'
        self.encoding = 'utf-8-sig'

        # إنشاء الملفات إذا لم تكن موجودة
        self._initialize_files()

    def _initialize_files(self):
        """تهيئة ملفات قاعدة البيانات"""
        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs(self.backups_dir, exist_ok=True)

        # إنشاء ملف الفواتير
        if not os.path.exists(self.invoices_file):
            invoice_columns = [
                "customer_name", "date", "description",
                "gold_change", "usd_change", "egp_change", "timestamp"
            ]
            pd.DataFrame(columns=invoice_columns).to_csv(
                self.invoices_file, index=False, encoding=self.encoding
            )

        # إنشاء ملف العملاء
        if not os.path.exists(self.customers_file):
            customer_columns = [
                "name", "phone", "email", "address", "notes", "created_date"
            ]
            pd.DataFrame(columns=customer_columns).to_csv(
                self.customers_file, index=False, encoding=self.encoding
            )
    
    def save_invoice(self, invoice_data: Dict[str, Any]) -> bool:
        """حفظ فاتورة جديدة"""
        try:
            # التحقق من صحة البيانات المطلوبة
            required_fields = ['customer_name', 'date', 'description', 'gold_change', 'usd_change', 'egp_change']
            for field in required_fields:
                if field not in invoice_data:
                    print(f"خطأ: الحقل المطلوب '{field}' غير موجود")
                    return False

            # إضافة timestamp إذا لم يكن موجوداً
            if 'timestamp' not in invoice_data:
                invoice_data['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # التأكد من ترتيب الأعمدة الصحيح
            ordered_data = {
                'customer_name': invoice_data['customer_name'],
                'date': invoice_data['date'],
                'description': invoice_data['description'],
                'gold_change': invoice_data['gold_change'],
                'usd_change': invoice_data['usd_change'],
                'egp_change': invoice_data['egp_change'],
                'timestamp': invoice_data['timestamp']
            }

            # إنشاء DataFrame للفاتورة الجديدة
            new_data = pd.DataFrame([ordered_data])

            # حفظ الفاتورة
            file_exists = os.path.exists(self.invoices_file)
            file_empty = not file_exists or os.path.getsize(self.invoices_file) == 0

            if file_empty:
                new_data.to_csv(self.invoices_file, index=False,
                                encoding=self.encoding)
            else:
                new_data.to_csv(self.invoices_file, mode='a', header=False,
                                index=False, encoding=self.encoding)

            # إنشاء نسخة احتياطية إذا كانت مفعلة
            if DATABASE_CONFIG['backup_enabled']:
                self.create_backup('invoices')

            return True
        except Exception as e:
            print(f"خطأ في حفظ الفاتورة: {str(e)}")
            return False
    
    def load_invoices(self) -> pd.DataFrame:
        """تحميل جميع الفواتير"""
        try:
            if os.path.exists(self.invoices_file):
                df = pd.read_csv(self.invoices_file, encoding=self.encoding)
                return df if not df.empty else pd.DataFrame()
            return pd.DataFrame()
        except Exception as e:
            print(f"خطأ في تحميل الفواتير: {str(e)}")
            return pd.DataFrame()
    
    def save_customer(self, customer_data: Dict[str, Any]) -> bool:
        """حفظ عميل جديد"""
        try:
            # إضافة تاريخ الإنشاء إذا لم يكن موجوداً
            if 'created_date' not in customer_data:
                customer_data['created_date'] = datetime.now().strftime("%Y-%m-%d")
            
            # تحميل العملاء الحاليين
            df = self.load_customers()
            
            # التحقق من عدم وجود العميل مسبقاً
            if not df.empty and customer_data['name'] in df['name'].values:
                return False  # العميل موجود مسبقاً
            
            # إضافة العميل الجديد
            new_customer_df = pd.DataFrame([customer_data])
            
            if df.empty:
                new_customer_df.to_csv(self.customers_file, index=False, encoding=self.encoding)
            else:
                df = pd.concat([df, new_customer_df], ignore_index=True)
                df.to_csv(self.customers_file, index=False, encoding=self.encoding)
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ العميل: {str(e)}")
            return False
    
    def load_customers(self) -> pd.DataFrame:
        """تحميل جميع العملاء"""
        try:
            if os.path.exists(self.customers_file):
                df = pd.read_csv(self.customers_file, encoding=self.encoding)
                return df if not df.empty else pd.DataFrame()
            return pd.DataFrame()
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {str(e)}")
            return pd.DataFrame()
    
    def get_customer_invoices(self, customer_name: str) -> pd.DataFrame:
        """الحصول على فواتير عميل محدد"""
        df = self.load_invoices()
        if not df.empty:
            return df[df['customer_name'] == customer_name]
        return pd.DataFrame()
    
    def delete_invoice(self, index: int) -> bool:
        """حذف فاتورة"""
        try:
            df = self.load_invoices()
            if not df.empty and index < len(df):
                df = df.drop(index).reset_index(drop=True)
                df.to_csv(self.invoices_file, index=False, encoding=self.encoding)
                return True
            return False
        except Exception as e:
            print(f"خطأ في حذف الفاتورة: {str(e)}")
            return False
    
    def create_backup(self, data_type: str = 'all') -> str:
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if data_type in ['invoices', 'all']:
                if os.path.exists(self.invoices_file):
                    backup_file = os.path.join(
                        self.backups_dir, f"backup_invoices_{timestamp}.csv")
                    df = pd.read_csv(self.invoices_file, encoding=self.encoding)
                    df.to_csv(backup_file, index=False, encoding=self.encoding)

            if data_type in ['customers', 'all']:
                if os.path.exists(self.customers_file):
                    backup_file = os.path.join(
                        self.backups_dir, f"backup_customers_{timestamp}.csv")
                    df = pd.read_csv(self.customers_file, encoding=self.encoding)
                    df.to_csv(backup_file, index=False, encoding=self.encoding)
            
            return f"backup_{timestamp}"
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            return ""
    
    def export_data(self, data_type: str, file_format: str = 'csv') -> str:
        """تصدير البيانات"""
        try:
            timestamp = datetime.now().strftime(EXPORT_CONFIG['datetime_format'])
            filename_base = EXPORT_CONFIG['filename_format'].format(
                type=data_type, timestamp=timestamp.replace(':', '-').replace(' ', '_')
            )
            
            if data_type == 'invoices':
                df = self.load_invoices()
                export_file = f"{filename_base}.{file_format}"
            elif data_type == 'customers':
                df = self.load_customers()
                export_file = f"{filename_base}.{file_format}"
            else:
                return ""
            
            if file_format == 'csv':
                df.to_csv(export_file, index=False, encoding=self.encoding)
            elif file_format == 'excel':
                df.to_excel(export_file, index=False)
            
            return export_file
        except Exception as e:
            print(f"خطأ في تصدير البيانات: {str(e)}")
            return ""
    
    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات شاملة"""
        invoices_df = self.load_invoices()
        customers_df = self.load_customers()
        
        stats = {
            'invoices': {
                'total': len(invoices_df),
                'total_usd': pd.to_numeric(invoices_df['usd_change'], errors='coerce').sum() if not invoices_df.empty else 0,
                'total_egp': pd.to_numeric(invoices_df['egp_change'], errors='coerce').sum() if not invoices_df.empty else 0,
                'total_gold': abs(pd.to_numeric(invoices_df['gold_change'], errors='coerce').sum()) if not invoices_df.empty else 0
            },
            'customers': {
                'total': len(customers_df),
                'unique_customers': invoices_df['customer_name'].nunique() if not invoices_df.empty else 0
            }
        }
        
        return stats
