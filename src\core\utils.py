"""
الأدوات المساعدة - Utility Functions
"""

import os
import shutil
import pandas as pd
from datetime import datetime
from typing import Union, Optional


def format_currency(amount: float, currency: str = "USD", decimal_places: int = 2) -> str:
    """
    تنسيق المبلغ المالي
    
    Args:
        amount (float): المبلغ
        currency (str): نوع العملة
        decimal_places (int): عدد الخانات العشرية
        
    Returns:
        str: المبلغ منسق
    """
    if currency.upper() == "USD":
        return f"${amount:.{decimal_places}f}"
    elif currency.upper() == "EGP":
        return f"{amount:.{decimal_places}f} ج.م"
    else:
        return f"{amount:.{decimal_places}f} {currency}"


def format_date(date_input: Union[str, datetime], format_type: str = "display") -> str:
    """
    تنسيق التاريخ
    
    Args:
        date_input: التاريخ (نص أو datetime)
        format_type: نوع التنسيق ("display", "filename", "database")
        
    Returns:
        str: التاريخ منسق
    """
    if isinstance(date_input, str):
        try:
            date_obj = pd.to_datetime(date_input)
        except:
            return str(date_input)
    else:
        date_obj = date_input
    
    if format_type == "display":
        return date_obj.strftime("%Y-%m-%d")
    elif format_type == "filename":
        return date_obj.strftime("%Y%m%d_%H%M%S")
    elif format_type == "database":
        return date_obj.strftime("%Y-%m-%d %H:%M:%S")
    else:
        return date_obj.strftime("%Y-%m-%d")


def create_backup(source_file: str, backup_dir: str = "backups") -> Optional[str]:
    """
    إنشاء نسخة احتياطية من ملف
    
    Args:
        source_file (str): مسار الملف المصدر
        backup_dir (str): مجلد النسخ الاحتياطية
        
    Returns:
        Optional[str]: مسار النسخة الاحتياطية أو None في حالة الفشل
    """
    try:
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        os.makedirs(backup_dir, exist_ok=True)
        
        # تحديد اسم النسخة الاحتياطية
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = os.path.basename(source_file)
        name, ext = os.path.splitext(file_name)
        backup_name = f"{name}_backup_{timestamp}{ext}"
        backup_path = os.path.join(backup_dir, backup_name)
        
        # نسخ الملف
        shutil.copy2(source_file, backup_path)
        return backup_path
        
    except Exception:
        return None


def validate_file_path(file_path: str) -> bool:
    """
    التحقق من صحة مسار الملف
    
    Args:
        file_path (str): مسار الملف
        
    Returns:
        bool: True إذا كان المسار صحيح
    """
    return os.path.exists(file_path) and os.path.isfile(file_path)


def validate_directory_path(dir_path: str) -> bool:
    """
    التحقق من صحة مسار المجلد
    
    Args:
        dir_path (str): مسار المجلد
        
    Returns:
        bool: True إذا كان المسار صحيح
    """
    return os.path.exists(dir_path) and os.path.isdir(dir_path)


def get_file_size(file_path: str) -> int:
    """
    الحصول على حجم الملف بالبايت
    
    Args:
        file_path (str): مسار الملف
        
    Returns:
        int: حجم الملف بالبايت
    """
    try:
        return os.path.getsize(file_path)
    except:
        return 0


def format_file_size(size_bytes: int) -> str:
    """
    تنسيق حجم الملف
    
    Args:
        size_bytes (int): حجم الملف بالبايت
        
    Returns:
        str: حجم الملف منسق
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def clean_filename(filename: str) -> str:
    """
    تنظيف اسم الملف من الأحرف غير المسموحة
    
    Args:
        filename (str): اسم الملف
        
    Returns:
        str: اسم الملف منظف
    """
    # الأحرف غير المسموحة في أسماء الملفات
    invalid_chars = '<>:"/\\|?*'
    
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    return filename.strip()


def ensure_directory_exists(dir_path: str) -> bool:
    """
    التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً
    
    Args:
        dir_path (str): مسار المجلد
        
    Returns:
        bool: True إذا تم إنشاء المجلد أو كان موجوداً
    """
    try:
        os.makedirs(dir_path, exist_ok=True)
        return True
    except:
        return False


def get_unique_filename(file_path: str) -> str:
    """
    الحصول على اسم ملف فريد (إضافة رقم إذا كان الملف موجوداً)
    
    Args:
        file_path (str): مسار الملف المطلوب
        
    Returns:
        str: مسار الملف الفريد
    """
    if not os.path.exists(file_path):
        return file_path
    
    directory = os.path.dirname(file_path)
    filename = os.path.basename(file_path)
    name, ext = os.path.splitext(filename)
    
    counter = 1
    while True:
        new_filename = f"{name}_{counter}{ext}"
        new_path = os.path.join(directory, new_filename)
        if not os.path.exists(new_path):
            return new_path
        counter += 1
