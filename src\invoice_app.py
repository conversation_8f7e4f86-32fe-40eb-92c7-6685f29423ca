"""
نظام إدارة فواتير الورشة - Crestal Diamond Workshop Invoice System
نظام شامل لإدارة الفواتير وحسابات العملاء وتحليل ملفات Excel
"""

import math
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

import pandas as pd
import plotly.express as px
import streamlit as st

# استيراد مدير قاعدة البيانات
try:
    from database.manager import DatabaseManager
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    st.warning("⚠️ مكتبات قاعدة البيانات غير متوفرة. سيتم استخدام نظام CSV.")

# --- الثوابت ---
INVOICE_FILE = 'invoices.csv'
PAGE_CREATE_INVOICE = "📄 إنشاء فاتورة جديدة"
PAGE_VIEW_INVOICES = "📊 عرض الفواتير المحفوظة"
PAGE_CUSTOMER_ACCOUNTS = "👥 حسابات العملاء"
PAGE_EXCEL_ANALYSIS = "🔍 تحليل ملفات Excel"
PAGE_STATISTICS = "📈 إحصائيات وتقارير"
PAGE_SETTINGS = "⚙️ الإعدادات"

# أسماء الأعمدة
COL_CUSTOMER_NAME = 'customer_name'
COL_DATE = 'date'
COL_DESCRIPTION = 'description'
COL_GOLD_CHANGE = 'gold_change'
COL_USD_CHANGE = 'usd_change'
COL_EGP_CHANGE = 'egp_change'
COL_TIMESTAMP = 'timestamp'

# أسماء الأعمدة للعرض
DISPLAY_COL_DESCRIPTION = "📝 البيان"
DISPLAY_COL_GOLD_CHANGE = "⚖️ التغير بالذهب (جرام)"
DISPLAY_COL_USD_CHANGE = "💵 التغير بالدولار ($)"
DISPLAY_COL_EGP_CHANGE = "💰 التغير بالجنيه (ج.م)"

# أسماء الحقول للملخص
FIELD_INVOICE_COUNT = 'عدد الفواتير'
FIELD_TOTAL_USD = 'إجمالي الدولار'
FIELD_TOTAL_EGP = 'إجمالي الجنيه'
FIELD_TOTAL_GOLD = 'إجمالي الذهب'

# إعدادات العرض
COLS_PER_ROW = 4
DATE_LABEL = 'التاريخ'

# --- الإعدادات الأولية ---
st.set_page_config(
    layout="wide",
    page_title="نظام فواتير الورشة",
    page_icon="💎"
)

# --- تهيئة قاعدة البيانات ---
@st.cache_resource
def init_database():
    """تهيئة مدير قاعدة البيانات"""
    if DATABASE_AVAILABLE:
        return DatabaseManager(use_mysql=True)
    else:
        return None

# إنشاء مثيل من مدير قاعدة البيانات
db_manager = init_database()

# --- الشريط الجانبي للتنقل ---
st.sidebar.title("💎 نظام إدارة الورشة")

# عرض حالة قاعدة البيانات
if db_manager:
    if db_manager.use_mysql:
        if db_manager.test_connection():
            st.sidebar.success("🟢 متصل بقاعدة البيانات MySQL")
        else:
            st.sidebar.error("🔴 خطأ في الاتصال بقاعدة البيانات")
    else:
        st.sidebar.info("🟡 يعمل بنظام CSV")
else:
    st.sidebar.warning("⚠️ مكتبات قاعدة البيانات غير متوفرة")

st.sidebar.markdown("---")

# قائمة التنقل
page = st.sidebar.selectbox(
    "اختر الصفحة:",
    [
        PAGE_CREATE_INVOICE,
        PAGE_VIEW_INVOICES,
        PAGE_CUSTOMER_ACCOUNTS,
        PAGE_EXCEL_ANALYSIS,
        PAGE_STATISTICS,
        PAGE_SETTINGS
    ]
)


def save_invoice(customer_name, invoice_date, gold_amount,
                usd_amount, egp_amount, description):
    """
    حفظ بيانات الفاتورة في قاعدة البيانات أو ملف CSV

    Args:
        customer_name (str): اسم العميل
        invoice_date (str): تاريخ الفاتورة
        gold_amount (float): كمية الذهب
        usd_amount (float): المبلغ بالدولار
        egp_amount (float): المبلغ بالجنيه
        description (str): وصف الفاتورة

    Returns:
        bool: True إذا تم الحفظ بنجاح، False إذا فشل
    """
    invoice_data = {
        'customer_name': customer_name,
        'invoice_date': invoice_date,
        'description': description,
        'gold_change': gold_amount,
        'usd_change': usd_amount,
        'egp_change': egp_amount
    }

    if db_manager:
        return db_manager.add_invoice(invoice_data)
    else:
        # استخدام النظام القديم كبديل
        return save_invoice_to_csv_fallback(customer_name, invoice_date,
                                          gold_amount, usd_amount,
                                          egp_amount, description)


def save_invoice_to_csv_fallback(customer_name, invoice_date, gold_amount,
                                usd_amount, egp_amount, description):
    """
    حفظ بيانات الفاتورة في ملف CSV (نظام احتياطي)
    """
    try:
        # تحديد الأعمدة التي نريدها في الملف
        columns = [
            COL_CUSTOMER_NAME, COL_DATE, COL_DESCRIPTION,
            COL_GOLD_CHANGE, COL_USD_CHANGE, COL_EGP_CHANGE, COL_TIMESTAMP
        ]

        # إنشاء صف جديد بالبيانات
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        new_data = pd.DataFrame(
            [[customer_name, invoice_date, description, gold_amount,
              usd_amount, egp_amount, timestamp]],
            columns=columns
        )

        # التحقق إذا كان الملف موجودًا أم لا
        if not os.path.isfile(INVOICE_FILE):
            # إذا لم يكن موجودًا، قم بإنشائه مع العناوين
            new_data.to_csv(INVOICE_FILE, index=False, encoding='utf-8-sig')
        else:
            # إذا كان موجودًا، قم بإضافة البيانات بدون العناوين
            new_data.to_csv(
                INVOICE_FILE, mode='a', header=False,
                index=False, encoding='utf-8-sig'
            )
        return True
    except Exception as e:
        st.error(f"خطأ في حفظ الفاتورة: {e}")
        return False


def load_invoices():
    """
    قراءة الفواتير المحفوظة من قاعدة البيانات أو ملف CSV

    Returns:
        pd.DataFrame: DataFrame يحتوي على بيانات الفواتير
    """
    if db_manager:
        try:
            return db_manager.get_all_invoices()
        except Exception as e:
            st.error(f"خطأ في قراءة الفواتير من قاعدة البيانات: {str(e)}")
            return load_invoices_csv_fallback()
    else:
        return load_invoices_csv_fallback()


def load_invoices_csv_fallback():
    """
    قراءة الفواتير المحفوظة من ملف CSV (نظام احتياطي)
    """
    if os.path.isfile(INVOICE_FILE):
        try:
            invoices_df = pd.read_csv(INVOICE_FILE, encoding='utf-8-sig')
            return invoices_df
        except (FileNotFoundError, pd.errors.EmptyDataError,
                pd.errors.ParserError) as e:
            st.error(f"خطأ في قراءة ملف الفواتير: {str(e)}")
            return pd.DataFrame()
    else:
        return pd.DataFrame()


def delete_invoice(invoice_index):
    """
    حذف فاتورة من الملف

    Args:
        invoice_index (int): فهرس الفاتورة المراد حذفها

    Returns:
        bool: True إذا تم الحذف بنجاح، False إذا فشل
    """
    invoices_df = load_invoices()
    if not invoices_df.empty and invoice_index < len(invoices_df):
        invoices_df = invoices_df.drop(invoice_index).reset_index(drop=True)
        invoices_df.to_csv(INVOICE_FILE, index=False, encoding='utf-8-sig')
        return True
    return False

class ExcelAnalyzer:
    """
    كلاس لتحليل ملفات Excel في مجلد معين
    """

    def __init__(self, excel_folder_path: str):
        """
        تهيئة محلل ملفات Excel

        Args:
            excel_folder_path (str): مسار المجلد الذي يحتوي على ملفات Excel
        """
        self.folder_path = Path(excel_folder_path)
        self.excel_files = []
        self.analysis_results = {}

    def find_excel_files(self) -> List[str]:
        """
        البحث عن جميع ملفات Excel في المجلد

        Returns:
            List[str]: قائمة بمسارات ملفات Excel الموجودة
        """
        excel_extensions = ['.xlsx', '.xls', '.csv']
        self.excel_files = []

        if self.folder_path.exists():
            for ext in excel_extensions:
                self.excel_files.extend(
                    list(self.folder_path.glob(f'*{ext}'))
                )

        return [str(file) for file in self.excel_files]

    def read_excel_file(self, excel_file_path: str) -> Dict[str, Any]:
        """
        قراءة ملف Excel واستخراج المعلومات الأساسية

        Args:
            excel_file_path (str): مسار ملف Excel

        Returns:
            Dict[str, Any]: معلومات الملف المستخرجة
        """
        excel_file_info = {
            'file_name': os.path.basename(excel_file_path),
            'file_path': excel_file_path,
            'sheets': [],
            'total_rows': 0,
            'total_columns': 0,
            'error': None,
            'data_preview': None
        }

        try:
            if excel_file_path.endswith('.csv'):
                # قراءة ملف CSV
                data_frame = pd.read_csv(excel_file_path, encoding='utf-8-sig')
                excel_file_info['sheets'] = ['Sheet1']
                excel_file_info['total_rows'] = len(data_frame)
                excel_file_info['total_columns'] = len(data_frame.columns)
                excel_file_info['data_preview'] = data_frame.head()
                excel_file_info['columns'] = list(data_frame.columns)

            else:
                # قراءة ملف Excel
                excel_file = pd.ExcelFile(excel_file_path)
                excel_file_info['sheets'] = excel_file.sheet_names

                # قراءة الورقة الأولى للمعاينة
                data_frame = pd.read_excel(excel_file_path, sheet_name=0)
                excel_file_info['total_rows'] = len(data_frame)
                excel_file_info['total_columns'] = len(data_frame.columns)
                excel_file_info['data_preview'] = data_frame.head()
                excel_file_info['columns'] = list(data_frame.columns)

        except (FileNotFoundError, pd.errors.EmptyDataError,
                pd.errors.ParserError, PermissionError) as e:
            excel_file_info['error'] = str(e)

        return excel_file_info

    def analyze_all_files(self) -> Dict[str, Any]:
        """
        تحليل جميع ملفات Excel في المجلد

        Returns:
            Dict[str, Any]: نتائج تحليل جميع الملفات
        """
        files = self.find_excel_files()

        if not files:
            return {}

        for excel_file_path in files:
            excel_file_info = self.read_excel_file(excel_file_path)
            self.analysis_results[excel_file_info['file_name']] = excel_file_info

        return self.analysis_results

# ===============================
# صفحة إنشاء فاتورة جديدة
# ===============================


if page == PAGE_CREATE_INVOICE:
    st.title(PAGE_CREATE_INVOICE)
    st.markdown("---")

    # --- حاوية المعلومات الأساسية ---
    with st.container(border=True):
        col1, col2 = st.columns([3, 1])
        with col1:
            customer_name = st.text_input("👤 اسم العميل:")
        with col2:
            invoice_date = st.date_input("🗓️ تاريخ الفاتورة")

        invoice_description = st.text_input(
            "📝 بيان الفاتورة (وصف مختصر):",
            placeholder="مثال: حلق موديل 123"
        )

    # --- حاوية حساب الذهب والأحجار ---
    with st.container(border=True):
        st.subheader("⚖️ حساب الذهب والأحجار")
        st.markdown("**الحساب بالدولار ($)**")
        w_col1, w_col2, w_col3 = st.columns([2, 2, 1])
        with w_col1:
            gold_weight = st.number_input(
                "وزن الذهب (جرام)", min_value=0.0, format="%.2f"
            )
        with w_col2:
            workmanship_price_usd = st.number_input(
                "سعر مصنعية الجرام ($)", min_value=0.0, format="%.2f"
            )
        workmanship_subtotal_usd = gold_weight * workmanship_price_usd
        with w_col3:
            st.metric(
                label="ناتج المصنعية",
                value=f"$ {workmanship_subtotal_usd:.2f}"
            )

        s_col1, s_col2, s_col3 = st.columns([2, 2, 1])
        with s_col1:
            stone_weight_carats = st.number_input(
                "وزن أحجار الورشة (قيراط)", min_value=0.0, format="%.3f"
            )
        with s_col2:
            stone_price_per_carat_usd = st.number_input(
                "سعر القيراط ($)", min_value=0.0, format="%.2f"
            )
        stone_cost_usd = stone_weight_carats * stone_price_per_carat_usd
        with s_col3:
            st.metric(
                label="ناتج سعر الأحجار",
                value=f"$ {stone_cost_usd:.2f}"
            )

        st.markdown("---")
        st.markdown("**الحساب بالجنيه المصري (EGP)**")

        e_col1, e_col2, e_col3 = st.columns([2, 2, 1])
        with e_col1:
            stone_count = st.number_input(
                "عدد أحجار العميل", min_value=0, step=1
            )
        with e_col2:
            stone_setting_price_egp = st.number_input(
                "سعر تركيب الحجر (EGP)", min_value=0.0, format="%.2f"
            )
        stone_setting_cost_egp = stone_count * stone_setting_price_egp
        with e_col3:
            st.metric(
                label="ناتج التركيب",
                value=f"{stone_setting_cost_egp:.2f} ج.م"
            )

    # --- حاوية الخدمات الإضافية ---
    with st.container(border=True):
        st.subheader("🔧 خدمات إضافية وتصليح (EGP)")
        serv_col1, serv_col2, serv_col3, serv_col4 = st.columns(4)
        with serv_col1:
            plating_white_egp = st.number_input(
                "بانيو أبيض", min_value=0.0, format="%.2f"
            )
        with serv_col2:
            plating_yellow_egp = st.number_input(
                "بانيو أصفر", min_value=0.0, format="%.2f"
            )
        with serv_col3:
            hallmark_egp = st.number_input(
                "دمغة", min_value=0.0, format="%.2f"
            )
        with serv_col4:
            repair_egp = st.number_input(
                "تصليح", min_value=0.0, format="%.2f"
            )

    # --- حاوية مواصفات الأحجار ---
    with st.container(border=True):
        st.subheader("📝 مواصفات الأحجار (اختياري)")
        spec_col1, spec_col2, spec_col3 = st.columns(3)
        with spec_col1:
            st.text_input("مقاس الحجر", key="size")
        with spec_col2:
            st.text_input("نوع الحجر", key="type")
        with spec_col3:
            st.text_input("اللون والنقاء (Quality)", key="quality")

    # --- الملخص النهائي ---
    st.write("---")
    with st.container(border=True):
        st.header("💵 الملخص النهائي للحساب")

        final_usd_charge = workmanship_subtotal_usd + stone_cost_usd
        final_egp_charge = (
            stone_setting_cost_egp + plating_white_egp +
            plating_yellow_egp + hallmark_egp + repair_egp
        )

        round_usd_checkbox = st.checkbox(
            "تقريب المبلغ بالدولار لأعلى رقم صحيح؟"
        )
        if round_usd_checkbox:
            final_usd_charge = math.ceil(final_usd_charge)

        res_col1, res_col2, res_col3 = st.columns(3)
        with res_col1:
            st.metric(
                "التغير في رصيد الذهب", f"{-gold_weight:.2f} جرام"
            )
        with res_col2:
            st.metric(
                "إجمالي المطلوب بالدولار", f"$ {final_usd_charge:.2f}"
            )
        with res_col3:
            st.metric(
                "إجمالي المطلوب بالجنيه", f"{final_egp_charge:.2f} جنيه"
            )

        # --- زر الحفظ ---
        if st.button("💾 حفظ الفاتورة", type="primary"):
            if customer_name.strip():
                success = save_invoice(
                    customer_name=customer_name,
                    invoice_date=str(invoice_date),
                    gold_amount=-gold_weight,
                    usd_amount=final_usd_charge,
                    egp_amount=final_egp_charge,
                    description=invoice_description
                )
                st.success(
                    f"✅ تم حفظ الفاتورة بنجاح للعميل: {customer_name}"
                )
                st.balloons()
            else:
                st.error("❌ يرجى إدخال اسم العميل")

# ===============================
# صفحة عرض الفواتير المحفوظة
# ===============================
elif page == PAGE_VIEW_INVOICES:
    st.title(PAGE_VIEW_INVOICES)
    st.markdown("---")

    # تحميل البيانات
    invoices_df = load_invoices()

    if invoices_df.empty:
        st.info(
            "📝 لا توجد فواتير محفوظة بعد. "
            "قم بإنشاء فاتورة جديدة أولاً."
        )
    else:
        # إحصائيات سريعة
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("إجمالي الفواتير", len(invoices_df))
        with col2:
            usd_total = pd.to_numeric(invoices_df[COL_USD_CHANGE], errors='coerce').sum()
            st.metric(
                "إجمالي الدولار",
                f"${usd_total:.2f}"
            )
        with col3:
            egp_total = pd.to_numeric(invoices_df[COL_EGP_CHANGE], errors='coerce').sum()
            st.metric(
                "إجمالي الجنيه",
                f"{egp_total:.2f} ج.م"
            )
        with col4:
            gold_total = pd.to_numeric(invoices_df[COL_GOLD_CHANGE], errors='coerce').sum()
            st.metric(
                "إجمالي الذهب",
                f"{gold_total:.2f} جرام"
            )

        st.markdown("---")

        # فلترة البيانات
        col1, col2 = st.columns([2, 1])
        with col1:
            search_customer = st.text_input("🔍 البحث باسم العميل:")
        with col2:
            sort_options = [
                "التاريخ (الأحدث)", "التاريخ (الأقدم)",
                "اسم العميل", "المبلغ بالدولار"
            ]
            sort_by = st.selectbox("ترتيب حسب:", sort_options)

        # تطبيق الفلترة
        filtered_df = invoices_df.copy()
        if search_customer:
            filtered_df = filtered_df[
                filtered_df[COL_CUSTOMER_NAME].str.contains(
                    search_customer, case=False, na=False
                )
            ]

        # تطبيق الترتيب
        if sort_by == "التاريخ (الأحدث)":
            filtered_df = filtered_df.sort_values(
                COL_TIMESTAMP, ascending=False
            )
        elif sort_by == "التاريخ (الأقدم)":
            filtered_df = filtered_df.sort_values(
                COL_TIMESTAMP, ascending=True
            )
        elif sort_by == "اسم العميل":
            filtered_df = filtered_df.sort_values(COL_CUSTOMER_NAME)
        elif sort_by == "المبلغ بالدولار":
            filtered_df = filtered_df.sort_values(
                COL_USD_CHANGE, ascending=False
            )

        # عرض الجدول
        st.subheader(f"📋 عرض {len(filtered_df)} فاتورة")

        # تحسين عرض الجدول
        display_df = filtered_df.copy()
        display_df[COL_USD_CHANGE] = display_df[COL_USD_CHANGE].apply(
            lambda x: f"${x:.2f}"
        )
        display_df[COL_EGP_CHANGE] = display_df[COL_EGP_CHANGE].apply(
            lambda x: f"{x:.2f} ج.م"
        )
        display_df[COL_GOLD_CHANGE] = display_df[COL_GOLD_CHANGE].apply(
            lambda x: f"{x:.2f} جرام"
        )

        # إعادة تسمية الأعمدة للعرض
        display_df = display_df.rename(columns={
            COL_CUSTOMER_NAME: 'اسم العميل',
            COL_DATE: 'تاريخ الفاتورة',
            COL_DESCRIPTION: 'البيان',
            COL_GOLD_CHANGE: 'تغيير الذهب',
            COL_USD_CHANGE: 'المبلغ بالدولار',
            COL_EGP_CHANGE: 'المبلغ بالجنيه',
            COL_TIMESTAMP: 'وقت الإنشاء'
        })

        st.dataframe(display_df, use_container_width=True)

        # خيارات إضافية
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📥 تصدير إلى Excel"):
                # تصدير البيانات
                export_filename = (
                    f"invoices_export_"
                    f"{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                )
                filtered_df.to_excel(export_filename, index=False)
                st.success(f"✅ تم تصدير البيانات إلى {export_filename}")

        with col2:
            if st.button("🗑️ حذف جميع الفواتير"):
                if st.session_state.get('confirm_delete', False):
                    if os.path.exists(INVOICE_FILE):
                        os.remove(INVOICE_FILE)
                    st.success("✅ تم حذف جميع الفواتير")
                    st.rerun()
                else:
                    st.session_state['confirm_delete'] = True
                    st.warning("⚠️ اضغط مرة أخرى للتأكيد")

        with col3:
            if st.button("🔄 تحديث البيانات"):
                st.rerun()

# ===============================
# صفحة حسابات العملاء
# ===============================
elif page == "👥 حسابات العملاء":
    st.title("📊 كشف حساب العملاء")
    st.markdown("---")

    # التحقق من وجود ملف البيانات
    df = load_invoices()

    if df.empty:
        st.warning("⚠️ لم يتم تسجيل أي فواتير بعد. يرجى إضافة فاتورة من الصفحة الرئيسية أولاً.")
        st.info("💡 لإنشاء فاتورة جديدة، انتقل إلى صفحة 'إنشاء فاتورة جديدة' وقم بملء بيانات الفاتورة.")
    else:
        # قائمة بأسماء العملاء بدون تكرار
        customer_list = df['customer_name'].unique()

        # شريط جانبي لاختيار العميل
        st.sidebar.header("🔍 اختيار العميل")
        selected_customer = st.sidebar.selectbox(
            "اختر العميل لعرض حسابه:",
            ["-- اختر عميل --"] + list(customer_list),
            key="customer_selector"
        )

        # إحصائيات عامة
        st.header("📈 إحصائيات عامة")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("إجمالي العملاء", len(customer_list))
        with col2:
            st.metric("إجمالي الفواتير", len(df))
        with col3:
            usd_total = pd.to_numeric(df['usd_change'], errors='coerce').sum()
            st.metric("إجمالي المبيعات ($)", f"${usd_total:.2f}")
        with col4:
            gold_total = pd.to_numeric(df['gold_change'], errors='coerce').sum()
            st.metric("إجمالي الذهب المستخدم", f"{abs(gold_total):.2f} جرام")

        st.markdown("---")

        if selected_customer and selected_customer != "-- اختر عميل --":
            st.header(f"👤 كشف حساب: {selected_customer}")

            # فلترة البيانات لعرض حساب العميل المختار فقط
            customer_df = df[df['customer_name'] == selected_customer].copy()

            # تحويل التاريخ إلى datetime للترتيب
            if 'timestamp' in customer_df.columns:
                customer_df['timestamp'] = pd.to_datetime(customer_df['timestamp'])
                customer_df = customer_df.sort_values('timestamp', ascending=False)
            else:
                customer_df['date'] = pd.to_datetime(customer_df['date'])
                customer_df = customer_df.sort_values('date', ascending=False)

            # --- حساب الأرصدة النهائية ---
            total_gold = pd.to_numeric(customer_df['gold_change'], errors='coerce').sum()
            total_usd = pd.to_numeric(customer_df['usd_change'], errors='coerce').sum()
            total_egp = pd.to_numeric(customer_df['egp_change'], errors='coerce').sum()
            invoice_count = len(customer_df)

            # --- عرض الأرصدة النهائية ---
            st.subheader("💰 الأرصدة النهائية")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("عدد الفواتير", invoice_count)
            with col2:
                st.metric("رصيد الذهب النهائي", f"{total_gold:.2f} جرام")
            with col3:
                st.metric("رصيد الدولار النهائي", f"$ {total_usd:.2f}")
            with col4:
                st.metric("رصيد الجنيه النهائي", f"{total_egp:.2f} ج.م")

            st.markdown("---")

            # --- الرسوم البيانية ---
            if len(customer_df) > 1:
                st.subheader("📊 الرسوم البيانية")

                col1, col2 = st.columns(2)

                with col1:
                    # رسم بياني لتطور المبيعات بالدولار
                    chart_df = customer_df.copy()
                    if 'timestamp' in chart_df.columns:
                        chart_df = chart_df.sort_values('timestamp')
                        fig1 = px.line(
                            chart_df,
                            x='timestamp',
                            y='usd_change',
                            title=f'تطور المبيعات بالدولار - {selected_customer}',
                            labels={'timestamp': 'التاريخ', 'usd_change': 'المبلغ ($)'}
                        )
                    else:
                        chart_df = chart_df.sort_values('date')
                        fig1 = px.line(
                            chart_df,
                            x='date',
                            y='usd_change',
                            title=f'تطور المبيعات بالدولار - {selected_customer}',
                            labels={'date': 'التاريخ', 'usd_change': 'المبلغ ($)'}
                        )
                    fig1.update_layout(xaxis_title="التاريخ", yaxis_title="المبلغ ($)")
                    st.plotly_chart(fig1, use_container_width=True)

                with col2:
                    # رسم بياني لاستهلاك الذهب
                    if 'timestamp' in chart_df.columns:
                        fig2 = px.bar(
                            chart_df,
                            x='timestamp',
                            y='gold_change',
                            title=f'استهلاك الذهب - {selected_customer}',
                            labels={'timestamp': 'التاريخ', 'gold_change': 'الذهب (جرام)'}
                        )
                    else:
                        fig2 = px.bar(
                            chart_df,
                            x='date',
                            y='gold_change',
                            title=f'استهلاك الذهب - {selected_customer}',
                            labels={'date': 'التاريخ', 'gold_change': 'الذهب (جرام)'}
                        )
                    fig2.update_layout(xaxis_title="التاريخ", yaxis_title="الذهب (جرام)")
                    st.plotly_chart(fig2, use_container_width=True)

            st.markdown("---")

            # --- عرض بيان العمليات (سجل الفواتير) ---
            st.subheader("📋 بيان العمليات")

            # خيارات الفلترة
            col1, col2 = st.columns([2, 1])
            with col1:
                date_filter = st.date_input(
                    "فلترة من تاريخ:",
                    value=None,
                    help="اختر تاريخ لعرض الفواتير من هذا التاريخ فما بعد",
                    key="date_filter_customer"
                )
            with col2:
                show_all = st.checkbox("عرض جميع الفواتير", value=True, key="show_all_customer")

            # تطبيق الفلترة
            filtered_df = customer_df.copy()
            if not show_all and date_filter:
                if 'timestamp' in filtered_df.columns:
                    filtered_df = filtered_df[filtered_df['timestamp'].dt.date >= date_filter]
                else:
                    filtered_df['date'] = pd.to_datetime(filtered_df['date'])
                    filtered_df = filtered_df[filtered_df['date'].dt.date >= date_filter]

            # إعادة تسمية الأعمدة لعرضها بشكل أفضل
            display_df = filtered_df.copy()
            if 'timestamp' in display_df.columns:
                display_df['timestamp'] = pd.to_datetime(display_df['timestamp']).dt.strftime('%Y-%m-%d %H:%M')
                display_df = display_df.rename(columns={
                    "timestamp": "📅 التاريخ والوقت",
                    "description": "📝 البيان",
                    "gold_change": "⚖️ التغير بالذهب (جرام)",
                    "usd_change": "💵 التغير بالدولار ($)",
                    "egp_change": "💰 التغير بالجنيه (ج.م)"
                })
                columns_to_show = ["📅 التاريخ والوقت", "📝 البيان", "⚖️ التغير بالذهب (جرام)", "💵 التغير بالدولار ($)", "💰 التغير بالجنيه (ج.م)"]
            else:
                display_df['date'] = pd.to_datetime(display_df['date']).dt.strftime('%Y-%m-%d')
                display_df = display_df.rename(columns={
                    "date": "📅 التاريخ",
                    "description": "📝 البيان",
                    "gold_change": "⚖️ التغير بالذهب (جرام)",
                    "usd_change": "💵 التغير بالدولار ($)",
                    "egp_change": "💰 التغير بالجنيه (ج.م)"
                })
                columns_to_show = ["📅 التاريخ", "📝 البيان", "⚖️ التغير بالذهب (جرام)", "💵 التغير بالدولار ($)", "💰 التغير بالجنيه (ج.م)"]

            # تنسيق الأرقام
            display_df["⚖️ التغير بالذهب (جرام)"] = display_df["⚖️ التغير بالذهب (جرام)"].apply(lambda x: f"{x:.2f}")
            display_df["💵 التغير بالدولار ($)"] = display_df["💵 التغير بالدولار ($)"].apply(lambda x: f"{x:.2f}")
            display_df["💰 التغير بالجنيه (ج.م)"] = display_df["💰 التغير بالجنيه (ج.م)"].apply(lambda x: f"{x:.2f}")

            # عرض الجدول
            st.dataframe(
                display_df[columns_to_show],
                use_container_width=True,
                hide_index=True
            )

            # --- خيارات إضافية ---
            st.markdown("---")
            st.subheader("🔧 خيارات إضافية")

            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button("📥 تصدير بيانات العميل", key="export_customer"):
                    export_filename = f"customer_{selected_customer}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                    customer_df.to_csv(export_filename, index=False, encoding='utf-8-sig')
                    st.success(f"✅ تم تصدير البيانات إلى {export_filename}")

            with col2:
                if st.button("📊 تقرير مفصل", key="detailed_report"):
                    st.info("🚧 ميزة التقرير المفصل قيد التطوير")

            with col3:
                if st.button("📧 إرسال كشف الحساب", key="send_statement"):
                    st.info("🚧 ميزة الإرسال قيد التطوير")

        else:
            # عرض قائمة جميع العملاء
            st.header("👥 قائمة جميع العملاء")

            # إنشاء ملخص لكل عميل
            customer_summary = []
            for customer in customer_list:
                customer_data = df[df['customer_name'] == customer]
                summary = {
                    'اسم العميل': customer,
                    'عدد الفواتير': len(customer_data),
                    'إجمالي الدولار': f"${customer_data['usd_change'].sum():.2f}",
                    'إجمالي الجنيه': f"{customer_data['egp_change'].sum():.2f} ج.م",
                    'إجمالي الذهب': f"{customer_data['gold_change'].sum():.2f} جرام",
                    'آخر فاتورة': customer_data['date'].max() if 'date' in customer_data.columns else customer_data['timestamp'].max()
                }
                customer_summary.append(summary)

            summary_df = pd.DataFrame(customer_summary)
            summary_df = summary_df.sort_values('عدد الفواتير', ascending=False)

            st.dataframe(summary_df, use_container_width=True, hide_index=True)

            # رسم بياني لأفضل العملاء
            st.subheader("🏆 أفضل العملاء حسب المبيعات")

            # تحضير البيانات للرسم البياني
            customer_sales = df.groupby('customer_name')['usd_change'].sum().sort_values(ascending=False).head(10)

            fig = px.bar(
                x=customer_sales.values,
                y=customer_sales.index,
                orientation='h',
                title='أفضل 10 عملاء حسب المبيعات بالدولار',
                labels={'x': 'المبيعات ($)', 'y': 'العميل'}
            )
            fig.update_layout(height=500)
            st.plotly_chart(fig, use_container_width=True)

# ===============================
# صفحة تحليل ملفات Excel
# ===============================
elif page == "🔍 تحليل ملفات Excel":
    st.title("🔍 تحليل ملفات Excel")
    st.markdown("---")

    # مسار المجلد
    st.subheader("📁 اختيار مجلد الملفات")

    col1, col2 = st.columns([3, 1])
    with col1:
        default_path = r"C:\Users\<USER>\OneDrive\Desktop\crestal diamond"
        folder_path = st.text_input("📂 مسار مجلد ملفات Excel:", value=default_path)

    with col2:
        st.write("")  # مساحة فارغة للمحاذاة
        st.write("")  # مساحة فارغة للمحاذاة
        analyze_button = st.button("🔍 تحليل الملفات", type="primary")

    if analyze_button:
        if folder_path and os.path.exists(folder_path):
            analyzer = ExcelAnalyzer(folder_path)

            with st.spinner("جاري تحليل الملفات..."):
                results = analyzer.analyze_all_files()

            if results:
                st.success(f"✅ تم تحليل {len(results)} ملف بنجاح!")

                # حفظ النتائج في session state
                st.session_state['analyzer'] = analyzer
                st.session_state['analysis_results'] = results

                # عرض ملخص سريع
                st.subheader("📊 ملخص سريع")
                col1, col2, col3, col4 = st.columns(4)

                total_files = len(results)
                total_rows = sum([info.get('total_rows', 0) for info in results.values() if not info.get('error')])
                total_columns = sum([info.get('total_columns', 0) for info in results.values() if not info.get('error')])
                error_files = sum([1 for info in results.values() if info.get('error')])

                with col1:
                    st.metric("إجمالي الملفات", total_files)
                with col2:
                    st.metric("إجمالي الصفوف", total_rows)
                with col3:
                    st.metric("إجمالي الأعمدة", total_columns)
                with col4:
                    st.metric("ملفات بها أخطاء", error_files, delta=f"-{error_files}" if error_files > 0 else None)

                st.markdown("---")

                # عرض تفاصيل كل ملف
                st.subheader("📋 تفاصيل الملفات")

                for file_name, file_info in results.items():
                    with st.expander(f"📄 {file_name}", expanded=False):
                        if file_info['error']:
                            st.error(f"❌ خطأ في قراءة الملف: {file_info['error']}")
                            continue

                        # معلومات أساسية
                        info_col1, info_col2, info_col3 = st.columns(3)
                        with info_col1:
                            st.metric("عدد الصفوف", file_info['total_rows'])
                        with info_col2:
                            st.metric("عدد الأعمدة", file_info['total_columns'])
                        with info_col3:
                            st.metric("عدد الأوراق", len(file_info['sheets']))

                        # أسماء الأعمدة
                        if 'columns' in file_info and file_info['columns']:
                            st.write("**أسماء الأعمدة:**")
                            # عرض الأعمدة في صفوف متعددة
                            cols_per_row = 4
                            columns = file_info['columns']
                            for i in range(0, len(columns), cols_per_row):
                                row_cols = st.columns(cols_per_row)
                                for j, col_name in enumerate(columns[i:i+cols_per_row]):
                                    with row_cols[j]:
                                        st.code(col_name)

                        # معاينة البيانات
                        if file_info['data_preview'] is not None and not file_info['data_preview'].empty:
                            st.write("**معاينة البيانات (أول 5 صفوف):**")
                            st.dataframe(file_info['data_preview'], use_container_width=True)

                        # خيارات إضافية
                        st.write("**خيارات:**")
                        action_col1, action_col2, action_col3 = st.columns(3)

                        with action_col1:
                            if st.button(f"📊 تحليل متقدم", key=f"analyze_{file_name}"):
                                st.info("🚧 ميزة التحليل المتقدم قيد التطوير")

                        with action_col2:
                            if st.button(f"📥 تصدير CSV", key=f"export_{file_name}"):
                                try:
                                    if file_info['data_preview'] is not None:
                                        # قراءة الملف كاملاً وتصديره
                                        if file_info['file_path'].endswith('.csv'):
                                            df_full = pd.read_csv(file_info['file_path'], encoding='utf-8-sig')
                                        else:
                                            df_full = pd.read_excel(file_info['file_path'])

                                        export_name = f"exported_{file_name.replace('.xlsx', '').replace('.xls', '')}.csv"
                                        df_full.to_csv(export_name, index=False, encoding='utf-8-sig')
                                        st.success(f"✅ تم تصدير الملف إلى {export_name}")
                                except Exception as e:
                                    st.error(f"❌ خطأ في التصدير: {str(e)}")

                        with action_col3:
                            if st.button(f"🔗 استيراد للنظام", key=f"import_{file_name}"):
                                st.info("🚧 ميزة الاستيراد للنظام قيد التطوير")

            else:
                st.error("❌ لم يتم العثور على ملفات Excel في المجلد المحدد")
        else:
            st.error("❌ المسار المحدد غير صحيح أو غير موجود")

    # عرض النتائج المحفوظة إذا كانت موجودة
    if 'analysis_results' in st.session_state and st.session_state['analysis_results']:
        st.markdown("---")
        st.subheader("📚 النتائج المحفوظة")
        st.info(f"تم تحليل {len(st.session_state['analysis_results'])} ملف مسبقاً. استخدم زر 'تحليل الملفات' لتحديث النتائج.")

        if st.button("🗑️ مسح النتائج المحفوظة"):
            del st.session_state['analysis_results']
            if 'analyzer' in st.session_state:
                del st.session_state['analyzer']
            st.rerun()

# ===============================
# صفحة الإحصائيات والتقارير
# ===============================
elif page == "📈 إحصائيات وتقارير":
    st.title("📈 إحصائيات وتقارير")
    st.markdown("---")
    
    df = load_invoices()
    
    if df.empty:
        st.info("📝 لا توجد بيانات لعرض الإحصائيات. قم بإنشاء بعض الفواتير أولاً.")
    else:
        # تحويل التاريخ
        df['date'] = pd.to_datetime(df['date'])
        df['month'] = df['date'].dt.to_period('M')
        
        # الإحصائيات العامة
        st.subheader("📊 الإحصائيات العامة")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("إجمالي العملاء", df['customer_name'].nunique())
        with col2:
            usd_mean = pd.to_numeric(df['usd_change'], errors='coerce').mean()
            st.metric("متوسط الفاتورة ($)", f"${usd_mean:.2f}")
        with col3:
            usd_max = pd.to_numeric(df['usd_change'], errors='coerce').max()
            st.metric("أعلى فاتورة ($)", f"${usd_max:.2f}")
        with col4:
            gold_total = pd.to_numeric(df['gold_change'], errors='coerce').sum()
            st.metric("إجمالي الذهب المستخدم", f"{abs(gold_total):.2f} جرام")
        
        # الرسوم البيانية
        st.markdown("---")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # رسم بياني للمبيعات الشهرية
            monthly_sales = df.groupby('month')['usd_change'].sum().reset_index()
            monthly_sales['month'] = monthly_sales['month'].astype(str)
            
            fig1 = px.bar(monthly_sales, x='month', y='usd_change', 
                         title='المبيعات الشهرية بالدولار',
                         labels={'month': 'الشهر', 'usd_change': 'المبيعات ($)'})
            st.plotly_chart(fig1, use_container_width=True)
        
        with col2:
            # رسم بياني لأفضل العملاء
            top_customers = df.groupby('customer_name')['usd_change'].sum().sort_values(ascending=False).head(10)
            
            fig2 = px.pie(values=top_customers.values, names=top_customers.index,
                         title='أفضل 10 عملاء حسب المبيعات')
            st.plotly_chart(fig2, use_container_width=True)
        
        # جدول أفضل العملاء
        st.markdown("---")
        st.subheader("🏆 أفضل العملاء")
        
        customer_stats = df.groupby('customer_name').agg({
            'usd_change': ['sum', 'count', 'mean'],
            'egp_change': 'sum',
            'gold_change': 'sum'
        }).round(2)
        
        customer_stats.columns = ['إجمالي الدولار', 'عدد الفواتير', 'متوسط الفاتورة', 'إجمالي الجنيه', 'إجمالي الذهب']
        customer_stats = customer_stats.sort_values('إجمالي الدولار', ascending=False)
        
        st.dataframe(customer_stats, use_container_width=True)

# ===============================
# صفحة الإعدادات
# ===============================
elif page == "⚙️ الإعدادات":
    st.title("⚙️ إعدادات النظام")
    st.markdown("---")
    
    st.subheader("📁 إدارة الملفات")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info("**ملف الفواتير الحالي:**")
        if os.path.exists('invoices.csv'):
            file_size = os.path.getsize('invoices.csv')
            st.write(f"📄 invoices.csv ({file_size} بايت)")
            
            if st.button("📥 تحميل نسخة احتياطية"):
                df = load_invoices()
                backup_name = f"backup_invoices_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df.to_csv(backup_name, index=False, encoding='utf-8-sig')
                st.success(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        else:
            st.write("❌ لا يوجد ملف فواتير")
    
    with col2:
        st.info("**استيراد البيانات:**")
        uploaded_file = st.file_uploader("اختر ملف CSV للاستيراد", type=['csv'])
        
        if uploaded_file is not None:
            try:
                new_df = pd.read_csv(uploaded_file)
                st.write("معاينة البيانات:")
                st.dataframe(new_df.head())
                
                if st.button("📤 استيراد البيانات"):
                    new_df.to_csv('invoices.csv', index=False, encoding='utf-8-sig')
                    st.success("✅ تم استيراد البيانات بنجاح")
                    st.rerun()
            except Exception as e:
                st.error(f"❌ خطأ في قراءة الملف: {str(e)}")
    
    st.markdown("---")
    st.subheader("ℹ️ معلومات النظام")
    
    st.info(f"""
    **نظام إدارة فواتير الورشة**
    - الإصدار: 2.0
    - تاريخ آخر تحديث: {datetime.now().strftime('%Y-%m-%d')}
    - المطور: Crestal Diamond Team
    """)

# تذييل الصفحة
st.sidebar.markdown("---")
st.sidebar.markdown("💎 **Crestal Diamond**")
st.sidebar.markdown("🔧 الإصدار 2.0")
st.sidebar.markdown(f"📅 {datetime.now().strftime('%Y-%m-%d')}")
