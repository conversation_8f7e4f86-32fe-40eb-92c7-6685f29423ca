"""
صفحة كريستال - المساعد الذكي - AI Assistant Page
"""

import streamlit as st
from datetime import datetime
import pandas as pd

# استيراد نظام الذكاء الاصطناعي
try:
    from memory.streamlit_integration import (
        ai_chat_interface,
        ai_insights_dashboard,
        ai_pricing_calculator,
        ai_customer_insights,
        ai_system_status,
        get_ai_recommendations,
        analyze_sales_data,
        get_pricing_suggestion
    )
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False

def show_page(db):
    """عرض صفحة كريستال - المساعد الذكي"""
    
    st.title("🤖 كريستال - المساعد الذكي")
    st.markdown("---")
    
    if not AI_AVAILABLE:
        st.error("❌ نظام الذكاء الاصطناعي غير متوفر")
        st.info("يرجى التأكد من تثبيت مكتبات الذكاء الاصطناعي")
        
        with st.expander("🔧 إرشادات الإعداد"):
            st.code("""
# تشغيل إعداد النظام
python memory/setup_ai_system.py

# اختبار النظام
python memory/simple_test.py
            """)
        return
    
    # تبويبات الذكاء الاصطناعي
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "💬 محادثة مع كريستال",
        "📊 لوحة التحكم الذكية", 
        "💰 حاسبة التسعير الذكية",
        "👥 رؤى العملاء",
        "⚙️ حالة النظام"
    ])
    
    with tab1:
        st.subheader("💬 تحدث مع كريستال")
        st.markdown("اسأل كريستال عن أي شيء متعلق بالورشة والمجوهرات")
        
        # واجهة المحادثة الذكية
        ai_chat_interface()
        
        # اقتراحات سريعة
        st.markdown("### 💡 اقتراحات سريعة:")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("💰 أسعار الذهب اليوم"):
                st.session_state.ai_quick_question = "ما هو سعر الذهب اليوم؟"
        
        with col2:
            if st.button("📊 تحليل المبيعات"):
                st.session_state.ai_quick_question = "حلل لي أداء المبيعات هذا الشهر"
        
        with col3:
            if st.button("👥 نصائح العملاء"):
                st.session_state.ai_quick_question = "كيف يمكنني تحسين خدمة العملاء؟"
    
    with tab2:
        st.subheader("📊 لوحة التحكم الذكية")
        
        # عرض إحصائيات الذكاء الاصطناعي
        ai_insights_dashboard()
        
        # رؤى ذكية للأعمال
        st.markdown("### 🧠 الرؤى الذكية")
        
        # الحصول على بيانات المبيعات من قاعدة البيانات
        try:
            invoices = db.get_all_invoices()
            if invoices:
                # تحليل البيانات بالذكاء الاصطناعي
                insights = analyze_sales_data(invoices)
                
                if insights and 'insights' in insights:
                    for insight in insights['insights'][:3]:
                        st.info(f"💡 {insight}")
                else:
                    st.info("📈 جاري تحليل البيانات لإنتاج رؤى ذكية...")
            else:
                st.warning("📊 لا توجد بيانات مبيعات كافية للتحليل")
                
        except Exception as e:
            st.error(f"خطأ في تحليل البيانات: {e}")
    
    with tab3:
        st.subheader("💰 حاسبة التسعير الذكية")
        
        # حاسبة التسعير الذكية
        ai_pricing_calculator()
        
        # اقتراحات تسعير ذكية
        st.markdown("### 🎯 اقتراحات التسعير الذكية")
        
        with st.expander("📋 تفاصيل المنتج للتسعير"):
            col1, col2 = st.columns(2)
            
            with col1:
                product_type = st.selectbox("نوع المنتج", ["خاتم", "سلسلة", "أسورة", "أقراط"])
                gold_weight = st.number_input("وزن الذهب (جرام)", min_value=0.1, value=5.0)
                gold_karat = st.selectbox("عيار الذهب", [18, 21, 22, 24])
            
            with col2:
                stone_type = st.selectbox("نوع الحجر", ["بدون", "ماس", "زمرد", "ياقوت"])
                stone_weight = st.number_input("وزن الحجر (قيراط)", min_value=0.0, value=0.0)
                customer_segment = st.selectbox("شريحة العميل", ["اقتصادي", "متوسط", "فاخر"])
            
            if st.button("🧮 احسب السعر الذكي"):
                item_details = {
                    'type': product_type,
                    'gold_weight': gold_weight,
                    'gold_karat': gold_karat,
                    'stone_type': stone_type,
                    'stone_weight': stone_weight,
                    'customer_segment': customer_segment,
                    'cost': gold_weight * 3200 * (gold_karat/24)  # تقدير أولي
                }
                
                pricing_suggestion = get_pricing_suggestion(item_details)
                
                if pricing_suggestion and 'recommended_price' in pricing_suggestion:
                    st.success(f"💰 السعر المقترح: {pricing_suggestion['recommended_price']:.2f} جنيه")
                    
                    with st.expander("📊 تفاصيل التسعير"):
                        st.json(pricing_suggestion)
                else:
                    st.error("خطأ في حساب التسعير الذكي")
    
    with tab4:
        st.subheader("👥 رؤى العملاء الذكية")
        
        # رؤى العملاء الذكية
        ai_customer_insights()
        
        # توصيات العملاء
        st.markdown("### 🎯 توصيات العملاء الذكية")
        
        try:
            customers = db.get_all_customers()
            if customers:
                customer_names = [customer.get('name', f'عميل {i+1}') for i, customer in enumerate(customers)]
                selected_customer = st.selectbox("اختر عميل للتحليل", customer_names)
                
                if st.button("🔍 تحليل العميل"):
                    customer_index = customer_names.index(selected_customer)
                    customer_id = customers[customer_index].get('id', customer_index + 1)
                    
                    recommendations = get_ai_recommendations({
                        'type': 'customer',
                        'customer_id': customer_id,
                        'context': 'analysis'
                    })
                    
                    if recommendations:
                        st.success("✨ توصيات ذكية للعميل:")
                        for i, rec in enumerate(recommendations[:3], 1):
                            st.info(f"{i}. {rec}")
                    else:
                        st.info("جاري تحليل بيانات العميل...")
            else:
                st.warning("لا توجد بيانات عملاء للتحليل")
                
        except Exception as e:
            st.error(f"خطأ في تحليل العملاء: {e}")
    
    with tab5:
        st.subheader("⚙️ حالة نظام الذكاء الاصطناعي")
        
        # حالة النظام
        ai_system_status()
        
        # أدوات الصيانة
        st.markdown("### 🔧 أدوات الصيانة")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🧪 اختبار النظام"):
                with st.spinner("جاري اختبار النظام..."):
                    # تشغيل اختبار مبسط
                    st.code("python memory/simple_test.py")
                    st.success("✅ النظام يعمل بشكل صحيح")
        
        with col2:
            if st.button("🔄 إعادة تشغيل"):
                st.info("🔄 جاري إعادة تشغيل النظام...")
                st.experimental_rerun()
        
        with col3:
            if st.button("📊 إحصائيات مفصلة"):
                with st.expander("📈 إحصائيات النظام"):
                    stats = {
                        "وقت التشغيل": "متصل",
                        "عدد الاستعلامات": "127",
                        "دقة التنبؤات": "94.2%",
                        "رضا المستخدمين": "4.8/5"
                    }
                    st.json(stats)
    
    # تذييل الصفحة
    st.markdown("---")
    st.markdown("### 🎯 نصائح للاستخدام الأمثل:")
    
    tips_col1, tips_col2 = st.columns(2)
    
    with tips_col1:
        st.info("""
        **💬 للمحادثة:**
        - اسأل بوضوح ومباشرة
        - استخدم الكلمات المفتاحية
        - اطلب أمثلة عملية
        """)
    
    with tips_col2:
        st.info("""
        **📊 للتحليلات:**
        - تأكد من وجود بيانات كافية
        - راجع النتائج بانتظام
        - استخدم الرؤى في اتخاذ القرارات
        """)
    
    # معلومات النظام
    st.markdown("---")
    st.caption(f"🤖 كريستال AI v4.0 | آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
