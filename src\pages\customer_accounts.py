"""
صفحة حسابات العملاء
Customer Accounts Page
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime


def show_page(db_manager):
    """
    عرض صفحة حسابات العملاء
    
    Args:
        db_manager: مدير قاعدة البيانات
    """
    st.title("📊 كشف حساب العملاء")
    st.markdown("---")

    # التحقق من وجود ملف البيانات
    invoices_df = db_manager.load_invoices()

    if invoices_df.empty:
        st.warning(
            "⚠️ لم يتم تسجيل أي فواتير بعد. "
            "يرجى إضافة فاتورة من الصفحة الرئيسية أولاً."
        )
        st.info(
            "💡 لإنشاء فاتورة جديدة، انتقل إلى صفحة "
            "'إنشاء فاتورة جديدة' وقم بملء بيانات الفاتورة."
        )
    else:
        # قائمة بأسماء العملاء بدون تكرار
        customer_list = invoices_df['customer_name'].unique()

        # شريط جانبي لاختيار العميل
        st.sidebar.header("🔍 اختيار العميل")
        selected_customer = st.sidebar.selectbox(
            "اختر العميل لعرض حسابه:",
            ["-- اختر عميل --"] + list(customer_list),
            key="customer_selector"
        )

        # إحصائيات عامة
        st.header("📈 إحصائيات عامة")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("إجمالي العملاء", len(customer_list))
        with col2:
            st.metric("إجمالي الفواتير", len(invoices_df))
        with col3:
            st.metric(
                "إجمالي المبيعات ($)",
                f"${invoices_df['usd_change'].sum():.2f}"
            )
        with col4:
            st.metric(
                "إجمالي الذهب المستخدم",
                f"{abs(invoices_df['gold_change'].sum()):.2f} جرام"
            )

        st.markdown("---")

        if selected_customer and selected_customer != "-- اختر عميل --":
            st.header(f"👤 كشف حساب: {selected_customer}")

            # فلترة البيانات لعرض حساب العميل المختار فقط
            customer_df = invoices_df[
                invoices_df['customer_name'] == selected_customer
            ].copy()

            # تحويل التاريخ إلى datetime للترتيب
            if 'timestamp' in customer_df.columns:
                customer_df['timestamp'] = pd.to_datetime(
                    customer_df['timestamp']
                )
                customer_df = customer_df.sort_values(
                    'timestamp', ascending=False
                )
            else:
                customer_df['date'] = pd.to_datetime(customer_df['date'])
                customer_df = customer_df.sort_values('date', ascending=False)

            # --- حساب الأرصدة النهائية ---
            total_gold = customer_df['gold_change'].sum()
            total_usd = customer_df['usd_change'].sum()
            total_egp = customer_df['egp_change'].sum()
            invoice_count = len(customer_df)

            # --- عرض الأرصدة النهائية ---
            st.subheader("💰 الأرصدة النهائية")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("عدد الفواتير", invoice_count)
            with col2:
                st.metric("رصيد الذهب النهائي", f"{total_gold:.2f} جرام")
            with col3:
                st.metric("رصيد الدولار النهائي", f"$ {total_usd:.2f}")
            with col4:
                st.metric("رصيد الجنيه النهائي", f"{total_egp:.2f} ج.م")

            st.markdown("---")

            # --- الرسوم البيانية ---
            if len(customer_df) > 1:
                st.subheader("📊 الرسوم البيانية")

                col1, col2 = st.columns(2)

                with col1:
                    # رسم بياني لتطور المبيعات بالدولار
                    chart_df = customer_df.copy()
                    if 'timestamp' in chart_df.columns:
                        chart_df = chart_df.sort_values('timestamp')
                        fig1 = px.line(
                            chart_df,
                            x='timestamp',
                            y='usd_change',
                            title=f'تطور المبيعات بالدولار - {selected_customer}',
                            labels={'timestamp': 'التاريخ', 'usd_change': 'المبلغ ($)'}
                        )
                    else:
                        chart_df = chart_df.sort_values('date')
                        fig1 = px.line(
                            chart_df,
                            x='date',
                            y='usd_change',
                            title=f'تطور المبيعات بالدولار - {selected_customer}',
                            labels={'date': 'التاريخ', 'usd_change': 'المبلغ ($)'}
                        )
                    fig1.update_layout(xaxis_title="التاريخ", yaxis_title="المبلغ ($)")
                    st.plotly_chart(fig1, use_container_width=True)

                with col2:
                    # رسم بياني لاستهلاك الذهب
                    if 'timestamp' in chart_df.columns:
                        fig2 = px.bar(
                            chart_df,
                            x='timestamp',
                            y='gold_change',
                            title=f'استهلاك الذهب - {selected_customer}',
                            labels={'timestamp': 'التاريخ', 'gold_change': 'الذهب (جرام)'}
                        )
                    else:
                        fig2 = px.bar(
                            chart_df,
                            x='date',
                            y='gold_change',
                            title=f'استهلاك الذهب - {selected_customer}',
                            labels={'date': 'التاريخ', 'gold_change': 'الذهب (جرام)'}
                        )
                    fig2.update_layout(xaxis_title="التاريخ", yaxis_title="الذهب (جرام)")
                    st.plotly_chart(fig2, use_container_width=True)

            st.markdown("---")

            # --- عرض بيان العمليات (سجل الفواتير) ---
            st.subheader("📋 بيان العمليات")

            # خيارات الفلترة
            col1, col2 = st.columns([2, 1])
            with col1:
                date_filter = st.date_input(
                    "فلترة من تاريخ:",
                    value=None,
                    help="اختر تاريخ لعرض الفواتير من هذا التاريخ فما بعد",
                    key="date_filter_customer"
                )
            with col2:
                show_all = st.checkbox(
                    "عرض جميع الفواتير", value=True, key="show_all_customer"
                )

            # تطبيق الفلترة
            filtered_df = customer_df.copy()
            if not show_all and date_filter:
                if 'timestamp' in filtered_df.columns:
                    filtered_df = filtered_df[
                        filtered_df['timestamp'].dt.date >= date_filter
                    ]
                else:
                    filtered_df['date'] = pd.to_datetime(filtered_df['date'])
                    filtered_df = filtered_df[
                        filtered_df['date'].dt.date >= date_filter
                    ]

            # إعادة تسمية الأعمدة لعرضها بشكل أفضل
            display_df = filtered_df.copy()
            if 'timestamp' in display_df.columns:
                display_df['timestamp'] = pd.to_datetime(
                    display_df['timestamp']
                ).dt.strftime('%Y-%m-%d %H:%M')
                display_df = display_df.rename(columns={
                    "timestamp": "📅 التاريخ والوقت",
                    "description": "📝 البيان",
                    "gold_change": "⚖️ التغير بالذهب (جرام)",
                    "usd_change": "💵 التغير بالدولار ($)",
                    "egp_change": "💰 التغير بالجنيه (ج.م)"
                })
                columns_to_show = [
                    "📅 التاريخ والوقت", "📝 البيان",
                    "⚖️ التغير بالذهب (جرام)",
                    "💵 التغير بالدولار ($)",
                    "💰 التغير بالجنيه (ج.م)"
                ]
            else:
                display_df['date'] = pd.to_datetime(
                    display_df['date']
                ).dt.strftime('%Y-%m-%d')
                display_df = display_df.rename(columns={
                    "date": "📅 التاريخ",
                    "description": "📝 البيان",
                    "gold_change": "⚖️ التغير بالذهب (جرام)",
                    "usd_change": "💵 التغير بالدولار ($)",
                    "egp_change": "💰 التغير بالجنيه (ج.م)"
                })
                columns_to_show = [
                    "📅 التاريخ", "📝 البيان",
                    "⚖️ التغير بالذهب (جرام)",
                    "💵 التغير بالدولار ($)",
                    "💰 التغير بالجنيه (ج.م)"
                ]

            # تنسيق الأرقام
            display_df["⚖️ التغير بالذهب (جرام)"] = display_df[
                "⚖️ التغير بالذهب (جرام)"
            ].apply(lambda x: f"{x:.2f}")
            display_df["💵 التغير بالدولار ($)"] = display_df[
                "💵 التغير بالدولار ($)"
            ].apply(lambda x: f"{x:.2f}")
            display_df["💰 التغير بالجنيه (ج.م)"] = display_df[
                "💰 التغير بالجنيه (ج.م)"
            ].apply(lambda x: f"{x:.2f}")

            # عرض الجدول
            st.dataframe(
                display_df[columns_to_show],
                use_container_width=True,
                hide_index=True
            )

            # --- خيارات إضافية ---
            st.markdown("---")
            st.subheader("🔧 خيارات إضافية")

            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button("📥 تصدير بيانات العميل", key="export_customer"):
                    export_filename = (
                        f"customer_{selected_customer}_"
                        f"{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                    )
                    customer_df.to_csv(
                        export_filename, index=False, encoding='utf-8-sig'
                    )
                    st.success(f"✅ تم تصدير البيانات إلى {export_filename}")

            with col2:
                if st.button("📊 تقرير مفصل", key="detailed_report"):
                    st.info("🚧 ميزة التقرير المفصل قيد التطوير")

            with col3:
                if st.button("📧 إرسال كشف الحساب", key="send_statement"):
                    st.info("🚧 ميزة الإرسال قيد التطوير")

        else:
            # عرض قائمة جميع العملاء
            st.header("👥 قائمة جميع العملاء")

            # إنشاء ملخص لكل عميل
            customer_summary = []
            for customer in customer_list:
                customer_data = invoices_df[
                    invoices_df['customer_name'] == customer
                ]
                summary = {
                    'اسم العميل': customer,
                    'عدد الفواتير': len(customer_data),
                    'إجمالي الدولار': f"${customer_data['usd_change'].sum():.2f}",
                    'إجمالي الجنيه': f"{customer_data['egp_change'].sum():.2f} ج.م",
                    'إجمالي الذهب': f"{customer_data['gold_change'].sum():.2f} جرام",
                    'آخر فاتورة': (
                        customer_data['date'].max() if 'date' in customer_data.columns
                        else customer_data['timestamp'].max()
                    )
                }
                customer_summary.append(summary)

            summary_df = pd.DataFrame(customer_summary)
            summary_df = summary_df.sort_values('عدد الفواتير', ascending=False)

            st.dataframe(summary_df, use_container_width=True, hide_index=True)

            # رسم بياني لأفضل العملاء
            st.subheader("🏆 أفضل العملاء حسب المبيعات")

            # تحضير البيانات للرسم البياني
            customer_sales = invoices_df.groupby('customer_name')[
                'usd_change'
            ].sum().sort_values(ascending=False).head(10)

            fig = px.bar(
                x=customer_sales.values,
                y=customer_sales.index,
                orientation='h',
                title='أفضل 10 عملاء حسب المبيعات بالدولار',
                labels={'x': 'المبيعات ($)', 'y': 'العميل'}
            )
            fig.update_layout(height=500)
            st.plotly_chart(fig, use_container_width=True)
