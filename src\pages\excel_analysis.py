"""
صفحة تحليل ملفات Excel
Excel Analysis Page
"""

import os
import streamlit as st
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any


class ExcelAnalyzer:
    """
    كلاس لتحليل ملفات Excel في مجلد معين
    """
    
    def __init__(self, excel_folder_path: str):
        """
        تهيئة محلل ملفات Excel
        
        Args:
            excel_folder_path (str): مسار المجلد الذي يحتوي على ملفات Excel
        """
        self.folder_path = Path(excel_folder_path)
        self.excel_files = []
        self.analysis_results = {}

    def find_excel_files(self) -> List[str]:
        """
        البحث عن جميع ملفات Excel في المجلد
        
        Returns:
            List[str]: قائمة بمسارات ملفات Excel الموجودة
        """
        excel_extensions = ['.xlsx', '.xls', '.csv']
        self.excel_files = []

        if self.folder_path.exists():
            for ext in excel_extensions:
                self.excel_files.extend(
                    list(self.folder_path.glob(f'*{ext}'))
                )

        return [str(file) for file in self.excel_files]

    def read_excel_file(self, excel_file_path: str) -> Dict[str, Any]:
        """
        قراءة ملف Excel واستخراج المعلومات الأساسية
        
        Args:
            excel_file_path (str): مسار ملف Excel
            
        Returns:
            Dict[str, Any]: معلومات الملف المستخرجة
        """
        excel_file_info = {
            'file_name': os.path.basename(excel_file_path),
            'file_path': excel_file_path,
            'sheets': [],
            'total_rows': 0,
            'total_columns': 0,
            'error': None,
            'data_preview': None
        }

        try:
            if excel_file_path.endswith('.csv'):
                # قراءة ملف CSV
                data_frame = pd.read_csv(excel_file_path, encoding='utf-8-sig')
                excel_file_info['sheets'] = ['Sheet1']
                excel_file_info['total_rows'] = len(data_frame)
                excel_file_info['total_columns'] = len(data_frame.columns)
                excel_file_info['data_preview'] = data_frame.head()
                excel_file_info['columns'] = list(data_frame.columns)

            else:
                # قراءة ملف Excel
                excel_file = pd.ExcelFile(excel_file_path)
                excel_file_info['sheets'] = excel_file.sheet_names

                # قراءة الورقة الأولى للمعاينة
                data_frame = pd.read_excel(excel_file_path, sheet_name=0)
                excel_file_info['total_rows'] = len(data_frame)
                excel_file_info['total_columns'] = len(data_frame.columns)
                excel_file_info['data_preview'] = data_frame.head()
                excel_file_info['columns'] = list(data_frame.columns)

        except (FileNotFoundError, pd.errors.EmptyDataError, 
                pd.errors.ParserError, PermissionError) as e:
            excel_file_info['error'] = str(e)

        return excel_file_info

    def analyze_all_files(self) -> Dict[str, Any]:
        """
        تحليل جميع ملفات Excel في المجلد
        
        Returns:
            Dict[str, Any]: نتائج تحليل جميع الملفات
        """
        files = self.find_excel_files()

        if not files:
            return {}

        for excel_file_path in files:
            excel_file_info = self.read_excel_file(excel_file_path)
            self.analysis_results[excel_file_info['file_name']] = excel_file_info

        return self.analysis_results


def show_page(db_manager):
    """
    عرض صفحة تحليل ملفات Excel
    
    Args:
        db_manager: مدير قاعدة البيانات
    """
    st.title("🔍 تحليل ملفات Excel")
    st.markdown("---")

    # مسار المجلد
    st.subheader("📁 اختيار مجلد الملفات")

    col1, col2 = st.columns([3, 1])
    with col1:
        default_path = r"C:\Users\<USER>\OneDrive\Desktop\crestal diamond"
        folder_path = st.text_input(
            "📂 مسار مجلد ملفات Excel:", value=default_path
        )

    with col2:
        st.write("")  # مساحة فارغة للمحاذاة
        st.write("")  # مساحة فارغة للمحاذاة
        analyze_button = st.button("🔍 تحليل الملفات", type="primary")

    if analyze_button:
        if folder_path and os.path.exists(folder_path):
            analyzer = ExcelAnalyzer(folder_path)

            with st.spinner("جاري تحليل الملفات..."):
                results = analyzer.analyze_all_files()

            if results:
                st.success(f"✅ تم تحليل {len(results)} ملف بنجاح!")

                # حفظ النتائج في session state
                st.session_state['analyzer'] = analyzer
                st.session_state['analysis_results'] = results

                # عرض ملخص سريع
                st.subheader("📊 ملخص سريع")
                col1, col2, col3, col4 = st.columns(4)

                total_files = len(results)
                total_rows = sum(
                    info.get('total_rows', 0) for info in results.values()
                    if not info.get('error')
                )
                total_columns = sum(
                    info.get('total_columns', 0) for info in results.values()
                    if not info.get('error')
                )
                error_files = sum(
                    1 for info in results.values() if info.get('error')
                )

                with col1:
                    st.metric("إجمالي الملفات", total_files)
                with col2:
                    st.metric("إجمالي الصفوف", total_rows)
                with col3:
                    st.metric("إجمالي الأعمدة", total_columns)
                with col4:
                    st.metric(
                        "ملفات بها أخطاء", error_files,
                        delta=f"-{error_files}" if error_files > 0 else None
                    )

                st.markdown("---")

                # عرض تفاصيل كل ملف
                st.subheader("📋 تفاصيل الملفات")

                for file_name, file_info in results.items():
                    with st.expander(f"📄 {file_name}", expanded=False):
                        if file_info['error']:
                            st.error(f"❌ خطأ في قراءة الملف: {file_info['error']}")
                            continue

                        # معلومات أساسية
                        info_col1, info_col2, info_col3 = st.columns(3)
                        with info_col1:
                            st.metric("عدد الصفوف", file_info['total_rows'])
                        with info_col2:
                            st.metric("عدد الأعمدة", file_info['total_columns'])
                        with info_col3:
                            st.metric("عدد الأوراق", len(file_info['sheets']))

                        # أسماء الأعمدة
                        if 'columns' in file_info and file_info['columns']:
                            st.write("**أسماء الأعمدة:**")
                            # عرض الأعمدة في صفوف متعددة
                            cols_per_row = 4
                            columns = file_info['columns']
                            for i in range(0, len(columns), cols_per_row):
                                row_cols = st.columns(cols_per_row)
                                for j, col_name in enumerate(
                                    columns[i:i+cols_per_row]
                                ):
                                    with row_cols[j]:
                                        st.code(col_name)

                        # معاينة البيانات
                        if (file_info['data_preview'] is not None and 
                            not file_info['data_preview'].empty):
                            st.write("**معاينة البيانات (أول 5 صفوف):**")
                            st.dataframe(
                                file_info['data_preview'],
                                use_container_width=True
                            )

                        # خيارات إضافية
                        st.write("**خيارات:**")
                        action_col1, action_col2, action_col3 = st.columns(3)

                        with action_col1:
                            if st.button(
                                "📊 تحليل متقدم", key=f"analyze_{file_name}"
                            ):
                                st.info("🚧 ميزة التحليل المتقدم قيد التطوير")

                        with action_col2:
                            if st.button(
                                "📥 تصدير CSV", key=f"export_{file_name}"
                            ):
                                try:
                                    if file_info['data_preview'] is not None:
                                        # قراءة الملف كاملاً وتصديره
                                        if file_info['file_path'].endswith('.csv'):
                                            df_full = pd.read_csv(
                                                file_info['file_path'],
                                                encoding='utf-8-sig'
                                            )
                                        else:
                                            df_full = pd.read_excel(
                                                file_info['file_path']
                                            )

                                        export_name = (
                                            f"exported_{file_name.replace('.xlsx', '').replace('.xls', '')}.csv"
                                        )
                                        df_full.to_csv(
                                            export_name, index=False,
                                            encoding='utf-8-sig'
                                        )
                                        st.success(
                                            f"✅ تم تصدير الملف إلى {export_name}"
                                        )
                                except Exception as e:
                                    st.error(f"❌ خطأ في التصدير: {str(e)}")

                        with action_col3:
                            if st.button(
                                "🔗 استيراد للنظام", key=f"import_{file_name}"
                            ):
                                st.info("🚧 ميزة الاستيراد للنظام قيد التطوير")

            else:
                st.error("❌ لم يتم العثور على ملفات Excel في المجلد المحدد")
        else:
            st.error("❌ المسار المحدد غير صحيح أو غير موجود")

    # عرض النتائج المحفوظة إذا كانت موجودة
    if ('analysis_results' in st.session_state and 
        st.session_state['analysis_results']):
        st.markdown("---")
        st.subheader("📚 النتائج المحفوظة")
        st.info(
            f"تم تحليل {len(st.session_state['analysis_results'])} ملف مسبقاً. "
            "استخدم زر 'تحليل الملفات' لتحديث النتائج."
        )

        if st.button("🗑️ مسح النتائج المحفوظة"):
            del st.session_state['analysis_results']
            if 'analyzer' in st.session_state:
                del st.session_state['analyzer']
            st.rerun()
