"""
صفحة إنشاء الفواتير - Invoice Creation Page
"""

import streamlit as st
import math
from datetime import datetime
from config.settings import MESSAGES, CURRENCIES


def show_page(db):
    """عرض صفحة إنشاء فاتورة جديدة"""
    
    st.title("📄 إنشاء فاتورة جديدة")
    st.markdown("---")

    # معلومات الفاتورة الأساسية
    with st.container(border=True):
        st.subheader("📋 معلومات الفاتورة الأساسية")
        col1, col2 = st.columns([3, 1])
        
        with col1:
            customer_name = st.text_input("👤 اسم العميل:")
        with col2:
            invoice_date = st.date_input("🗓️ تاريخ الفاتورة", value=datetime.now())
        
        invoice_description = st.text_input(
            "📝 بيان الفاتورة (وصف مختصر):", 
            placeholder="مثال: حلق موديل 123"
        )

    # حساب الذهب والأحجار
    with st.container(border=True):
        st.subheader("⚖️ حساب الذهب والأحجار")
        
        # القسم الخاص بالدولار
        st.markdown(f"**الحساب بالدولار ({CURRENCIES['USD']['symbol']})**")
        w_col1, w_col2, w_col3 = st.columns([2, 2, 1])
        
        with w_col1:
            gold_weight = st.number_input("وزن الذهب (جرام)", min_value=0.0, format="%.2f")
        with w_col2:
            workmanship_price_usd = st.number_input(
                f"سعر مصنعية الجرام ({CURRENCIES['USD']['symbol']})", 
                min_value=0.0, format="%.2f"
            )
        
        workmanship_subtotal_usd = gold_weight * workmanship_price_usd
        
        with w_col3:
            st.metric(
                label="ناتج المصنعية", 
                value=f"{CURRENCIES['USD']['symbol']} {workmanship_subtotal_usd:.2f}"
            )

        s_col1, s_col2, s_col3 = st.columns([2, 2, 1])
        
        with s_col1:
            stone_weight_carats = st.number_input(
                "وزن أحجار الورشة (قيراط)", 
                min_value=0.0, format="%.3f"
            )
        with s_col2:
            stone_price_per_carat_usd = st.number_input(
                f"سعر القيراط ({CURRENCIES['USD']['symbol']})", 
                min_value=0.0, format="%.2f"
            )
        
        stone_cost_usd = stone_weight_carats * stone_price_per_carat_usd
        
        with s_col3:
            st.metric(
                label="ناتج سعر الأحجار", 
                value=f"{CURRENCIES['USD']['symbol']} {stone_cost_usd:.2f}"
            )
        
        st.markdown("---")
        st.markdown(f"**الحساب بالجنيه المصري ({CURRENCIES['EGP']['symbol']})**")
        
        e_col1, e_col2, e_col3 = st.columns([2, 2, 1])
        
        with e_col1:
            stone_count = st.number_input("عدد أحجار العميل", min_value=0, step=1)
        with e_col2:
            stone_setting_price_egp = st.number_input(
                f"سعر تركيب الحجر ({CURRENCIES['EGP']['symbol']})", 
                min_value=0.0, format="%.2f"
            )
        
        stone_setting_cost_egp = stone_count * stone_setting_price_egp
        
        with e_col3:
            st.metric(
                label="ناتج التركيب", 
                value=f"{stone_setting_cost_egp:.2f} {CURRENCIES['EGP']['symbol']}"
            )

    # الخدمات الإضافية
    with st.container(border=True):
        st.subheader(f"🔧 خدمات إضافية وتصليح ({CURRENCIES['EGP']['symbol']})")
        serv_col1, serv_col2, serv_col3, serv_col4 = st.columns(4)
        
        with serv_col1:
            plating_white_egp = st.number_input("بانيو أبيض", min_value=0.0, format="%.2f")
        with serv_col2:
            plating_yellow_egp = st.number_input("بانيو أصفر", min_value=0.0, format="%.2f")
        with serv_col3:
            hallmark_egp = st.number_input("دمغة", min_value=0.0, format="%.2f")
        with serv_col4:
            repair_egp = st.number_input("تصليح", min_value=0.0, format="%.2f")

    # مواصفات الأحجار
    with st.container(border=True):
        st.subheader("📝 مواصفات الأحجار (اختياري)")
        spec_col1, spec_col2, spec_col3 = st.columns(3)
        
        with spec_col1:
            stone_size = st.text_input("مقاس الحجر", key="size")
        with spec_col2:
            stone_type = st.text_input("نوع الحجر", key="type")
        with spec_col3:
            stone_quality = st.text_input("اللون والنقاء (Quality)", key="quality")

    # الملخص النهائي
    st.markdown("---")
    with st.container(border=True):
        st.header("💵 الملخص النهائي للحساب")
        
        # حساب المجاميع
        final_usd_charge = workmanship_subtotal_usd + stone_cost_usd
        final_egp_charge = (stone_setting_cost_egp + plating_white_egp + 
                           plating_yellow_egp + hallmark_egp + repair_egp)

        # خيار التقريب
        round_usd_checkbox = st.checkbox("تقريب المبلغ بالدولار لأعلى رقم صحيح؟")
        if round_usd_checkbox:
            final_usd_charge = math.ceil(final_usd_charge)

        # عرض النتائج النهائية
        res_col1, res_col2, res_col3 = st.columns(3)
        
        with res_col1:
            st.metric("التغير في رصيد الذهب", f"{-gold_weight:.2f} جرام")
        with res_col2:
            st.metric(
                "إجمالي المطلوب بالدولار", 
                f"{CURRENCIES['USD']['symbol']} {final_usd_charge:.2f}"
            )
        with res_col3:
            st.metric(
                "إجمالي المطلوب بالجنيه", 
                f"{final_egp_charge:.2f} {CURRENCIES['EGP']['symbol']}"
            )
        
        # زر الحفظ
        if st.button("💾 حفظ الفاتورة", type="primary"):
            if customer_name.strip():
                # إعداد بيانات الفاتورة
                invoice_data = {
                    'customer_name': customer_name,
                    'date': str(invoice_date),
                    'description': invoice_description,
                    'gold_change': -gold_weight,
                    'usd_change': final_usd_charge,
                    'egp_change': final_egp_charge
                }
                
                # حفظ الفاتورة
                if db.save_invoice(invoice_data):
                    st.success(MESSAGES['success']['invoice_saved'] + f" للعميل: {customer_name}")
                    st.balloons()
                    
                    # إضافة العميل إلى قاعدة البيانات إذا لم يكن موجوداً
                    customers_df = db.load_customers()
                    if customers_df.empty or customer_name not in customers_df['name'].values:
                        customer_data = {
                            'name': customer_name,
                            'phone': None,
                            'email': None,
                            'address': None,
                            'notes': f"تم إنشاؤه تلقائياً من الفاتورة: {invoice_description}"
                        }
                        db.save_customer(customer_data)
                else:
                    st.error("❌ فشل في حفظ الفاتورة")
            else:
                st.error("❌ يرجى إدخال اسم العميل")

    # معلومات إضافية
    with st.expander("ℹ️ معلومات إضافية"):
        st.write("""
        **ملاحظات:**
        - يتم حفظ الفاتورة تلقائياً مع الوقت والتاريخ
        - يتم إنشاء نسخة احتياطية تلقائياً
        - يمكن مراجعة الفاتورة من صفحة "عرض الفواتير"
        - يتم إضافة العميل تلقائياً إذا لم يكن موجوداً
        """)
