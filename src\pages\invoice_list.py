"""
صفحة عرض الفواتير المحفوظة
Invoice List Page
"""

import streamlit as st
import pandas as pd
from datetime import datetime


def show_page(db_manager):
    """
    عرض صفحة الفواتير المحفوظة
    
    Args:
        db_manager: مدير قاعدة البيانات
    """
    st.title("📊 الفواتير المحفوظة")
    st.markdown("---")

    # تحميل البيانات
    invoices_df = db_manager.load_invoices()

    if invoices_df.empty:
        st.info(
            "📝 لا توجد فواتير محفوظة بعد. "
            "قم بإنشاء فاتورة جديدة أولاً."
        )
    else:
        # إحصائيات سريعة
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("إجمالي الفواتير", len(invoices_df))
        with col2:
            st.metric(
                "إجمالي الدولار",
                f"${invoices_df['usd_change'].sum():.2f}"
            )
        with col3:
            st.metric(
                "إجمالي الجنيه",
                f"{invoices_df['egp_change'].sum():.2f} ج.م"
            )
        with col4:
            st.metric(
                "إجمالي الذهب",
                f"{invoices_df['gold_change'].sum():.2f} جرام"
            )

        st.markdown("---")

        # فلترة البيانات
        col1, col2 = st.columns([2, 1])
        with col1:
            search_customer = st.text_input("🔍 البحث باسم العميل:")
        with col2:
            sort_options = [
                "التاريخ (الأحدث)", "التاريخ (الأقدم)",
                "اسم العميل", "المبلغ بالدولار"
            ]
            sort_by = st.selectbox("ترتيب حسب:", sort_options)

        # تطبيق الفلترة
        filtered_df = invoices_df.copy()
        if search_customer:
            filtered_df = filtered_df[
                filtered_df['customer_name'].str.contains(
                    search_customer, case=False, na=False
                )
            ]

        # تطبيق الترتيب
        if sort_by == "التاريخ (الأحدث)":
            filtered_df = filtered_df.sort_values(
                'timestamp', ascending=False
            )
        elif sort_by == "التاريخ (الأقدم)":
            filtered_df = filtered_df.sort_values(
                'timestamp', ascending=True
            )
        elif sort_by == "اسم العميل":
            filtered_df = filtered_df.sort_values('customer_name')
        elif sort_by == "المبلغ بالدولار":
            filtered_df = filtered_df.sort_values(
                'usd_change', ascending=False
            )

        # عرض الجدول
        st.subheader(f"📋 عرض {len(filtered_df)} فاتورة")

        # تحسين عرض الجدول
        display_df = filtered_df.copy()
        display_df['usd_change'] = display_df['usd_change'].apply(
            lambda x: f"${x:.2f}"
        )
        display_df['egp_change'] = display_df['egp_change'].apply(
            lambda x: f"{x:.2f} ج.م"
        )
        display_df['gold_change'] = display_df['gold_change'].apply(
            lambda x: f"{x:.2f} جرام"
        )

        # إعادة تسمية الأعمدة للعرض
        display_df = display_df.rename(columns={
            'customer_name': 'اسم العميل',
            'date': 'تاريخ الفاتورة',
            'description': 'البيان',
            'gold_change': 'تغيير الذهب',
            'usd_change': 'المبلغ بالدولار',
            'egp_change': 'المبلغ بالجنيه',
            'timestamp': 'وقت الإنشاء'
        })

        st.dataframe(display_df, use_container_width=True)

        # خيارات إضافية
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📥 تصدير إلى Excel"):
                # تصدير البيانات
                export_filename = (
                    f"invoices_export_"
                    f"{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                )
                filtered_df.to_excel(export_filename, index=False)
                st.success(f"✅ تم تصدير البيانات إلى {export_filename}")

        with col2:
            if st.button("🗑️ حذف جميع الفواتير"):
                if st.session_state.get('confirm_delete', False):
                    success = db_manager.delete_all_invoices()
                    if success:
                        st.success("✅ تم حذف جميع الفواتير")
                        st.rerun()
                    else:
                        st.error("❌ حدث خطأ أثناء حذف الفواتير")
                else:
                    st.session_state['confirm_delete'] = True
                    st.warning("⚠️ اضغط مرة أخرى للتأكيد")

        with col3:
            if st.button("🔄 تحديث البيانات"):
                st.rerun()
