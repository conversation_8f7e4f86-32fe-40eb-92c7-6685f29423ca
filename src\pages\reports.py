"""
صفحة الإحصائيات والتقارير
Reports and Statistics Page
"""

import streamlit as st
import pandas as pd
import plotly.express as px


def show_page(db_manager):
    """
    عرض صفحة الإحصائيات والتقارير
    
    Args:
        db_manager: مدير قاعدة البيانات
    """
    st.title("📈 إحصائيات وتقارير")
    st.markdown("---")
    
    invoices_df = db_manager.load_invoices()
    
    if invoices_df.empty:
        st.info(
            "📝 لا توجد بيانات لعرض الإحصائيات. "
            "قم بإنشاء بعض الفواتير أولاً."
        )
    else:
        # تحويل التاريخ
        invoices_df['date'] = pd.to_datetime(invoices_df['date'])
        invoices_df['month'] = invoices_df['date'].dt.to_period('M')
        
        # الإحصائيات العامة
        st.subheader("📊 الإحصائيات العامة")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("إجمالي العملاء", invoices_df['customer_name'].nunique())
        with col2:
            st.metric(
                "متوسط الفاتورة ($)",
                f"${invoices_df['usd_change'].mean():.2f}"
            )
        with col3:
            st.metric(
                "أعلى فاتورة ($)",
                f"${invoices_df['usd_change'].max():.2f}"
            )
        with col4:
            st.metric(
                "إجمالي الذهب المستخدم",
                f"{abs(invoices_df['gold_change'].sum()):.2f} جرام"
            )
        
        # الرسوم البيانية
        st.markdown("---")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # رسم بياني للمبيعات الشهرية
            monthly_sales = invoices_df.groupby('month')[
                'usd_change'
            ].sum().reset_index()
            monthly_sales['month'] = monthly_sales['month'].astype(str)
            
            fig1 = px.bar(
                monthly_sales, x='month', y='usd_change', 
                title='المبيعات الشهرية بالدولار',
                labels={'month': 'الشهر', 'usd_change': 'المبيعات ($)'}
            )
            st.plotly_chart(fig1, use_container_width=True)
        
        with col2:
            # رسم بياني لأفضل العملاء
            top_customers = invoices_df.groupby('customer_name')[
                'usd_change'
            ].sum().sort_values(ascending=False).head(10)
            
            fig2 = px.pie(
                values=top_customers.values, names=top_customers.index,
                title='أفضل 10 عملاء حسب المبيعات'
            )
            st.plotly_chart(fig2, use_container_width=True)
        
        # جدول أفضل العملاء
        st.markdown("---")
        st.subheader("🏆 أفضل العملاء")
        
        customer_stats = invoices_df.groupby('customer_name').agg({
            'usd_change': ['sum', 'count', 'mean'],
            'egp_change': 'sum',
            'gold_change': 'sum'
        }).round(2)
        
        customer_stats.columns = [
            'إجمالي الدولار', 'عدد الفواتير', 'متوسط الفاتورة',
            'إجمالي الجنيه', 'إجمالي الذهب'
        ]
        customer_stats = customer_stats.sort_values(
            'إجمالي الدولار', ascending=False
        )
        
        st.dataframe(customer_stats, use_container_width=True)

        # تحليل الاتجاهات
        st.markdown("---")
        st.subheader("📊 تحليل الاتجاهات")
        
        # رسم بياني لتطور المبيعات عبر الوقت
        daily_sales = invoices_df.groupby('date')[
            'usd_change'
        ].sum().reset_index()
        
        fig3 = px.line(
            daily_sales, x='date', y='usd_change',
            title='تطور المبيعات اليومية بالدولار',
            labels={'date': 'التاريخ', 'usd_change': 'المبيعات ($)'}
        )
        st.plotly_chart(fig3, use_container_width=True)
        
        # إحصائيات متقدمة
        st.markdown("---")
        st.subheader("🔍 إحصائيات متقدمة")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**توزيع المبيعات حسب الشهر:**")
            monthly_distribution = invoices_df.groupby(
                invoices_df['date'].dt.month_name()
            )['usd_change'].sum().sort_values(ascending=False)
            
            fig4 = px.bar(
                x=monthly_distribution.index,
                y=monthly_distribution.values,
                title='توزيع المبيعات حسب الشهر',
                labels={'x': 'الشهر', 'y': 'المبيعات ($)'}
            )
            st.plotly_chart(fig4, use_container_width=True)
        
        with col2:
            st.write("**توزيع استهلاك الذهب:**")
            gold_usage = invoices_df[invoices_df['gold_change'] < 0]
            if not gold_usage.empty:
                fig5 = px.histogram(
                    gold_usage, x='gold_change',
                    title='توزيع استهلاك الذهب',
                    labels={'gold_change': 'كمية الذهب (جرام)', 'count': 'التكرار'}
                )
                st.plotly_chart(fig5, use_container_width=True)
            else:
                st.info("لا توجد بيانات استهلاك ذهب")
        
        # ملخص الأداء
        st.markdown("---")
        st.subheader("📋 ملخص الأداء")
        
        performance_col1, performance_col2, performance_col3 = st.columns(3)
        
        with performance_col1:
            st.metric(
                "إجمالي الإيرادات ($)",
                f"${invoices_df['usd_change'].sum():.2f}"
            )
            st.metric(
                "إجمالي الإيرادات (ج.م)",
                f"{invoices_df['egp_change'].sum():.2f} ج.م"
            )
        
        with performance_col2:
            avg_invoice_value = invoices_df['usd_change'].mean()
            st.metric(
                "متوسط قيمة الفاتورة",
                f"${avg_invoice_value:.2f}"
            )
            
            # حساب معدل النمو (إذا كان هناك أكثر من شهر)
            if len(monthly_sales) > 1:
                growth_rate = (
                    (monthly_sales['usd_change'].iloc[-1] - 
                     monthly_sales['usd_change'].iloc[0]) /
                    monthly_sales['usd_change'].iloc[0] * 100
                )
                st.metric(
                    "معدل النمو الشهري",
                    f"{growth_rate:.1f}%"
                )
            else:
                st.metric("معدل النمو الشهري", "غير متاح")
        
        with performance_col3:
            most_active_customer = invoices_df['customer_name'].value_counts().index[0]
            most_active_count = invoices_df['customer_name'].value_counts().iloc[0]
            
            st.metric(
                "أكثر العملاء نشاطاً",
                most_active_customer
            )
            st.metric(
                "عدد فواتير العميل الأكثر نشاطاً",
                most_active_count
            )
        
        # خيارات التصدير
        st.markdown("---")
        st.subheader("📥 تصدير التقارير")
        
        export_col1, export_col2, export_col3 = st.columns(3)
        
        with export_col1:
            if st.button("📊 تصدير الإحصائيات"):
                # إنشاء ملف إحصائيات
                stats_summary = {
                    'إجمالي العملاء': [invoices_df['customer_name'].nunique()],
                    'إجمالي الفواتير': [len(invoices_df)],
                    'إجمالي الإيرادات ($)': [invoices_df['usd_change'].sum()],
                    'إجمالي الإيرادات (ج.م)': [invoices_df['egp_change'].sum()],
                    'متوسط الفاتورة ($)': [invoices_df['usd_change'].mean()],
                    'أعلى فاتورة ($)': [invoices_df['usd_change'].max()],
                    'إجمالي الذهب المستخدم (جرام)': [abs(invoices_df['gold_change'].sum())]
                }
                
                stats_df = pd.DataFrame(stats_summary)
                stats_filename = f"statistics_summary_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                stats_df.to_excel(stats_filename, index=False)
                st.success(f"✅ تم تصدير الإحصائيات إلى {stats_filename}")
        
        with export_col2:
            if st.button("👥 تصدير بيانات العملاء"):
                customer_filename = f"customer_analysis_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                customer_stats.to_excel(customer_filename)
                st.success(f"✅ تم تصدير بيانات العملاء إلى {customer_filename}")
        
        with export_col3:
            if st.button("📈 تصدير المبيعات الشهرية"):
                monthly_filename = f"monthly_sales_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                monthly_sales.to_excel(monthly_filename, index=False)
                st.success(f"✅ تم تصدير المبيعات الشهرية إلى {monthly_filename}")
