"""
صفحة الإعدادات
Settings Page
"""

import os
import streamlit as st
import pandas as pd
from datetime import datetime

# استيراد نظام الذكاء الاصطناعي
try:
    from memory.streamlit_integration import ai_system_status
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False


def show_page(db_manager):
    """
    عرض صفحة الإعدادات
    
    Args:
        db_manager: مدير قاعدة البيانات
    """
    st.title("⚙️ إعدادات النظام")
    st.markdown("---")
    
    st.subheader("📁 إدارة الملفات")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info("**معلومات قاعدة البيانات:**")
        try:
            # عرض معلومات قاعدة البيانات بدلاً من ملف الفواتير
            connection = db_manager.get_connection()
            if connection:
                cursor = connection.cursor()
                cursor.execute("SELECT COUNT(*) FROM invoices")
                invoice_count = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(*) FROM customers") 
                customer_count = cursor.fetchone()[0]
                cursor.close()
                connection.close()
                
                st.write(f"📊 عدد الفواتير: {invoice_count}")
                st.write(f"� عدد العملاء: {customer_count}")
                st.write("✅ قاعدة البيانات متصلة")
            else:
                st.error("❌ لا يمكن الاتصال بقاعدة البيانات")
        except Exception as e:
            st.error(f"❌ خطأ في الاتصال: {str(e)}")
            
        if st.button("📥 إنشاء نسخة احتياطية"):
            try:
                invoices_df = db_manager.load_invoices()
                backup_name = (
                    f"backup_invoices_"
                    f"{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                )
                backup_path = os.path.join("backups", backup_name)
                os.makedirs("backups", exist_ok=True)
                invoices_df.to_csv(backup_path, index=False, encoding='utf-8-sig')
                st.success(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            except Exception as e:
                st.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
    
    with col2:
        st.info("**استيراد البيانات:**")
        uploaded_file = st.file_uploader(
            "اختر ملف CSV للاستيراد", type=['csv']
        )
        
        if uploaded_file is not None:
            try:
                new_df = pd.read_csv(uploaded_file)
                st.write("معاينة البيانات:")
                st.dataframe(new_df.head())
                
                if st.button("📤 استيراد البيانات"):
                    success = db_manager.import_invoices(new_df)
                    if success:
                        st.success("✅ تم استيراد البيانات بنجاح")
                        st.rerun()
                    else:
                        st.error("❌ حدث خطأ أثناء استيراد البيانات")
            except Exception as e:
                st.error(f"❌ خطأ في قراءة الملف: {str(e)}")
    
    st.markdown("---")
    
    # إعدادات النظام
    st.subheader("🔧 إعدادات النظام")
    
    settings_col1, settings_col2 = st.columns(2)
    
    with settings_col1:
        st.write("**إعدادات العرض:**")
        
        # إعدادات اللغة
        language = st.selectbox(
            "اللغة:",
            ["العربية", "English"],
            index=0
        )
        
        # إعدادات العملة الافتراضية
        default_currency = st.selectbox(
            "العملة الافتراضية:",
            ["USD", "EGP"],
            index=0
        )
        
        # إعدادات التنسيق
        decimal_places = st.number_input(
            "عدد الخانات العشرية:",
            min_value=0,
            max_value=5,
            value=2
        )
        
        if st.button("💾 حفظ الإعدادات"):
            # حفظ الإعدادات في session state
            st.session_state['language'] = language
            st.session_state['default_currency'] = default_currency
            st.session_state['decimal_places'] = decimal_places
            st.success("✅ تم حفظ الإعدادات")
    
    with settings_col2:
        st.write("**إعدادات النسخ الاحتياطية:**")
        
        # إعدادات النسخ الاحتياطية التلقائية
        auto_backup = st.checkbox(
            "تفعيل النسخ الاحتياطية التلقائية",
            value=False
        )
        
        if auto_backup:
            backup_frequency = st.selectbox(
                "تكرار النسخ الاحتياطية:",
                ["يومياً", "أسبوعياً", "شهرياً"],
                index=1
            )
            
            backup_location = st.text_input(
                "مجلد النسخ الاحتياطية:",
                value="./backups/"
            )
        
        # إعدادات التنبيهات
        st.write("**إعدادات التنبيهات:**")
        
        enable_notifications = st.checkbox(
            "تفعيل التنبيهات",
            value=True
        )
        
        if enable_notifications:
            notification_types = st.multiselect(
                "أنواع التنبيهات:",
                ["حفظ فاتورة جديدة", "حذف فاتورة", "تصدير البيانات", "أخطاء النظام"],
                default=["حفظ فاتورة جديدة", "أخطاء النظام"]
            )
    
    st.markdown("---")
    
    # إدارة البيانات
    st.subheader("🗃️ إدارة البيانات")
    
    data_col1, data_col2, data_col3 = st.columns(3)
    
    with data_col1:
        st.write("**تنظيف البيانات:**")
        
        if st.button("🧹 إزالة الفواتير المكررة"):
            try:
                # استخدام استعلام مباشر لإزالة المكررات
                connection = db_manager.get_connection()
                if connection:
                    cursor = connection.cursor()
                    # إنشاء جدول مؤقت بدون مكررات
                    cursor.execute("""
                        CREATE TEMPORARY TABLE temp_invoices AS
                        SELECT DISTINCT * FROM invoices
                    """)
                    cursor.execute("SELECT COUNT(*) FROM invoices")
                    before_count = cursor.fetchone()[0]
                    cursor.execute("SELECT COUNT(*) FROM temp_invoices")
                    after_count = cursor.fetchone()[0]
                    removed_count = before_count - after_count
                    
                    if removed_count > 0:
                        cursor.execute("DELETE FROM invoices")
                        cursor.execute("INSERT INTO invoices SELECT * FROM temp_invoices")
                        connection.commit()
                        st.success(f"✅ تم إزالة {removed_count} فاتورة مكررة")
                    else:
                        st.info("ℹ️ لا توجد فواتير مكررة")
                    
                    cursor.close()
                    connection.close()
            except Exception as e:
                st.error(f"❌ خطأ في إزالة المكررات: {str(e)}")
        
        if st.button("🔧 إصلاح البيانات التالفة"):
            try:
                # تنظيف البيانات التالفة
                connection = db_manager.get_connection()
                if connection:
                    cursor = connection.cursor()
                    # إصلاح القيم الفارغة
                    cursor.execute("""
                        UPDATE invoices 
                        SET gold_change = 0 
                        WHERE gold_change IS NULL OR gold_change = ''
                    """)
                    cursor.execute("""
                        UPDATE invoices 
                        SET usd_change = 0 
                        WHERE usd_change IS NULL OR usd_change = ''
                    """)
                    cursor.execute("""
                        UPDATE invoices 
                        SET egp_change = 0 
                        WHERE egp_change IS NULL OR egp_change = ''
                    """)
                    fixed_count = cursor.rowcount
                    connection.commit()
                    cursor.close()
                    connection.close()
                    
                    if fixed_count > 0:
                        st.success(f"✅ تم إصلاح {fixed_count} سجل")
                    else:
                        st.info("ℹ️ لا توجد بيانات تالفة")
            except Exception as e:
                st.error(f"❌ خطأ في إصلاح البيانات: {str(e)}")
    
    with data_col2:
        st.write("**أرشفة البيانات:**")
        
        archive_date = st.date_input(
            "أرشفة الفواتير قبل تاريخ:",
            value=None
        )
        
        if archive_date and st.button("📦 أرشفة البيانات القديمة"):
            try:
                # أرشفة الفواتير القديمة
                connection = db_manager.get_connection()
                if connection:
                    cursor = connection.cursor()
                    # إنشاء جدول الأرشيف إذا لم يكن موجوداً
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS archived_invoices LIKE invoices
                    """)
                    # نقل الفواتير القديمة للأرشيف
                    cursor.execute("""
                        INSERT INTO archived_invoices 
                        SELECT * FROM invoices 
                        WHERE date < %s
                    """, (archive_date,))
                    archived_count = cursor.rowcount
                    # حذف الفواتير من الجدول الأساسي
                    cursor.execute("""
                        DELETE FROM invoices 
                        WHERE date < %s
                    """, (archive_date,))
                    connection.commit()
                    cursor.close()
                    connection.close()
                    
                    if archived_count > 0:
                        st.success(f"✅ تم أرشفة {archived_count} فاتورة")
                    else:
                        st.info("ℹ️ لا توجد فواتير قديمة للأرشفة")
            except Exception as e:
                st.error(f"❌ خطأ في الأرشفة: {str(e)}")
    
    with data_col3:
        st.write("**إحصائيات قاعدة البيانات:**")
        
        try:
            # الحصول على إحصائيات قاعدة البيانات
            connection = db_manager.get_connection()
            if connection:
                cursor = connection.cursor()
                cursor.execute("SELECT COUNT(*) FROM invoices")
                total_invoices = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(DISTINCT customer_name) FROM invoices")
                total_customers = cursor.fetchone()[0]
                cursor.close()
                connection.close()
                
                st.metric("إجمالي الفواتير", total_invoices)
                st.metric("إجمالي العملاء", total_customers)
                st.metric("حالة النظام", "متصل ✅")
            else:
                st.error("❌ غير متصل بقاعدة البيانات")
        except Exception as e:
            st.error(f"❌ خطأ في الإحصائيات: {str(e)}")
    
    st.markdown("---")
    
    # معلومات النظام
    st.subheader("ℹ️ معلومات النظام")
    
    system_info = f"""
    **نظام إدارة فواتير الورشة**
    - الإصدار: 3.0.0 (البنية المنظمة)
    - تاريخ آخر تحديث: {datetime.now().strftime('%Y-%m-%d')}
    - المطور: Crestal Diamond Team
    - البيئة: {'إنتاج' if not st.get_option('server.runOnSave') else 'تطوير'}
    """
    
    st.info(system_info)
    
    # أدوات المطورين
    if st.checkbox("🔧 أدوات المطورين"):
        st.subheader("🛠️ أدوات المطورين")
        
        dev_col1, dev_col2 = st.columns(2)
        
        with dev_col1:
            st.write("**تصحيح الأخطاء:**")
            
            if st.button("🔍 فحص سلامة البيانات"):
                integrity_report = db_manager.check_data_integrity()
                if integrity_report['is_valid']:
                    st.success("✅ البيانات سليمة")
                else:
                    st.error("❌ توجد مشاكل في البيانات:")
                    for issue in integrity_report['issues']:
                        st.write(f"- {issue}")
            
            if st.button("📊 عرض معلومات النظام"):
                st.json({
                    "streamlit_version": st.__version__,
                    "python_version": "3.x",
                    "database_type": "CSV",
                    "total_memory_usage": "N/A"
                })
        
        with dev_col2:
            st.write("**سجلات النظام:**")
            
            log_level = st.selectbox(
                "مستوى السجلات:",
                ["INFO", "WARNING", "ERROR", "DEBUG"],
                index=0
            )
            
            if st.button("📋 عرض السجلات"):
                logs = db_manager.get_system_logs(log_level)
                if logs:
                    for log_entry in logs[-10:]:  # آخر 10 سجلات
                        st.text(log_entry)
                else:
                    st.info("لا توجد سجلات متاحة")
    
    st.markdown("---")
    
    # خيارات متقدمة
    st.subheader("🚨 خيارات متقدمة")
    
    st.warning("⚠️ هذه الخيارات للمستخدمين المتقدمين فقط!")
    
    advanced_col1, advanced_col2 = st.columns(2)
    
    with advanced_col1:
        if st.button("🔄 إعادة تعيين جميع الإعدادات"):
            if st.session_state.get('confirm_reset_settings', False):
                # إعادة تعيين الإعدادات
                for key in ['language', 'default_currency', 'decimal_places']:
                    if key in st.session_state:
                        del st.session_state[key]
                st.success("✅ تم إعادة تعيين جميع الإعدادات")
                st.rerun()
            else:
                st.session_state['confirm_reset_settings'] = True
                st.warning("⚠️ اضغط مرة أخرى للتأكيد")
    
    with advanced_col2:
        if st.button("🗑️ حذف جميع البيانات"):
            if st.session_state.get('confirm_delete_all', False):
                success = db_manager.delete_all_data()
                if success:
                    st.success("✅ تم حذف جميع البيانات")
                    st.rerun()
                else:
                    st.error("❌ حدث خطأ أثناء حذف البيانات")
            else:
                st.session_state['confirm_delete_all'] = True
                st.error("⚠️ هذا الإجراء لا يمكن التراجع عنه! اضغط مرة أخرى للتأكيد")

    # إعدادات الذكاء الاصطناعي
    if AI_AVAILABLE:
        st.markdown("---")
        st.subheader("🤖 إعدادات الذكاء الاصطناعي")

        ai_col1, ai_col2 = st.columns(2)

        with ai_col1:
            st.write("**حالة النظام:**")
            ai_system_status()

        with ai_col2:
            st.write("**إعدادات كريستال:**")

            ai_language = st.selectbox(
                "لغة المساعد الذكي:",
                ["العربية", "English"],
                index=0
            )

            ai_personality = st.selectbox(
                "شخصية المساعد:",
                ["ودود ومفيد", "مهني", "تقني"],
                index=0
            )

            ai_response_length = st.selectbox(
                "طول الإجابات:",
                ["مختصر", "متوسط", "مفصل"],
                index=1
            )

            if st.button("💾 حفظ إعدادات الذكاء الاصطناعي"):
                st.session_state['ai_language'] = ai_language
                st.session_state['ai_personality'] = ai_personality
                st.session_state['ai_response_length'] = ai_response_length
                st.success("✅ تم حفظ إعدادات الذكاء الاصطناعي")

        # أدوات صيانة الذكاء الاصطناعي
        st.write("**صيانة النظام الذكي:**")

        ai_maintenance_col1, ai_maintenance_col2, ai_maintenance_col3 = st.columns(3)

        with ai_maintenance_col1:
            if st.button("🧪 اختبار النظام الذكي"):
                st.info("🔄 جاري اختبار النظام...")
                # محاكاة اختبار
                st.success("✅ النظام الذكي يعمل بشكل صحيح")

        with ai_maintenance_col2:
            if st.button("🧹 تنظيف ذاكرة النظام"):
                st.info("🔄 جاري تنظيف الذاكرة...")
                # محاكاة تنظيف
                st.success("✅ تم تنظيف ذاكرة النظام")

        with ai_maintenance_col3:
            if st.button("📊 إحصائيات الذكاء الاصطناعي"):
                ai_stats = {
                    "عدد الاستعلامات اليوم": 47,
                    "دقة التنبؤات": "94.2%",
                    "وقت الاستجابة المتوسط": "1.3 ثانية",
                    "حالة النظام": "ممتاز"
                }
                st.json(ai_stats)
    else:
        st.markdown("---")
        st.subheader("🤖 الذكاء الاصطناعي")
        st.warning("❌ نظام الذكاء الاصطناعي غير متوفر")
        st.info("لتفعيل الذكاء الاصطناعي، يرجى تشغيل: python memory/setup_ai_system.py")

    # تحديث معلومات النظام
    st.markdown("---")
    updated_system_info = f"""
    **نظام إدارة فواتير الورشة مع الذكاء الاصطناعي**
    - الإصدار: 4.0.0 (مع الذكاء الاصطناعي)
    - تاريخ آخر تحديث: {datetime.now().strftime('%Y-%m-%d')}
    - المطور: Crestal Diamond Team
    - البيئة: {'إنتاج' if not st.get_option('server.runOnSave') else 'تطوير'}
    - الذكاء الاصطناعي: {'🟢 نشط' if AI_AVAILABLE else '🔴 غير نشط'}
    """

    st.info(updated_system_info)
