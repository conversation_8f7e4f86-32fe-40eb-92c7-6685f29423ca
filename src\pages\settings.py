"""
صفحة الإعدادات
Settings Page
"""

import os
import streamlit as st
import pandas as pd
from datetime import datetime


def show_page(db_manager):
    """
    عرض صفحة الإعدادات
    
    Args:
        db_manager: مدير قاعدة البيانات
    """
    st.title("⚙️ إعدادات النظام")
    st.markdown("---")
    
    st.subheader("📁 إدارة الملفات")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info("**ملف الفواتير الحالي:**")
        invoice_file = db_manager.get_invoice_file_path()
        if os.path.exists(invoice_file):
            file_size = os.path.getsize(invoice_file)
            st.write(f"📄 {os.path.basename(invoice_file)} ({file_size} بايت)")
            
            if st.button("📥 تحميل نسخة احتياطية"):
                invoices_df = db_manager.load_invoices()
                backup_name = (
                    f"backup_invoices_"
                    f"{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                )
                invoices_df.to_csv(backup_name, index=False, encoding='utf-8-sig')
                st.success(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        else:
            st.write("❌ لا يوجد ملف فواتير")
    
    with col2:
        st.info("**استيراد البيانات:**")
        uploaded_file = st.file_uploader(
            "اختر ملف CSV للاستيراد", type=['csv']
        )
        
        if uploaded_file is not None:
            try:
                new_df = pd.read_csv(uploaded_file)
                st.write("معاينة البيانات:")
                st.dataframe(new_df.head())
                
                if st.button("📤 استيراد البيانات"):
                    success = db_manager.import_invoices(new_df)
                    if success:
                        st.success("✅ تم استيراد البيانات بنجاح")
                        st.rerun()
                    else:
                        st.error("❌ حدث خطأ أثناء استيراد البيانات")
            except Exception as e:
                st.error(f"❌ خطأ في قراءة الملف: {str(e)}")
    
    st.markdown("---")
    
    # إعدادات النظام
    st.subheader("🔧 إعدادات النظام")
    
    settings_col1, settings_col2 = st.columns(2)
    
    with settings_col1:
        st.write("**إعدادات العرض:**")
        
        # إعدادات اللغة
        language = st.selectbox(
            "اللغة:",
            ["العربية", "English"],
            index=0
        )
        
        # إعدادات العملة الافتراضية
        default_currency = st.selectbox(
            "العملة الافتراضية:",
            ["USD", "EGP"],
            index=0
        )
        
        # إعدادات التنسيق
        decimal_places = st.number_input(
            "عدد الخانات العشرية:",
            min_value=0,
            max_value=5,
            value=2
        )
        
        if st.button("💾 حفظ الإعدادات"):
            # حفظ الإعدادات في session state
            st.session_state['language'] = language
            st.session_state['default_currency'] = default_currency
            st.session_state['decimal_places'] = decimal_places
            st.success("✅ تم حفظ الإعدادات")
    
    with settings_col2:
        st.write("**إعدادات النسخ الاحتياطية:**")
        
        # إعدادات النسخ الاحتياطية التلقائية
        auto_backup = st.checkbox(
            "تفعيل النسخ الاحتياطية التلقائية",
            value=False
        )
        
        if auto_backup:
            backup_frequency = st.selectbox(
                "تكرار النسخ الاحتياطية:",
                ["يومياً", "أسبوعياً", "شهرياً"],
                index=1
            )
            
            backup_location = st.text_input(
                "مجلد النسخ الاحتياطية:",
                value="./backups/"
            )
        
        # إعدادات التنبيهات
        st.write("**إعدادات التنبيهات:**")
        
        enable_notifications = st.checkbox(
            "تفعيل التنبيهات",
            value=True
        )
        
        if enable_notifications:
            notification_types = st.multiselect(
                "أنواع التنبيهات:",
                ["حفظ فاتورة جديدة", "حذف فاتورة", "تصدير البيانات", "أخطاء النظام"],
                default=["حفظ فاتورة جديدة", "أخطاء النظام"]
            )
    
    st.markdown("---")
    
    # إدارة البيانات
    st.subheader("🗃️ إدارة البيانات")
    
    data_col1, data_col2, data_col3 = st.columns(3)
    
    with data_col1:
        st.write("**تنظيف البيانات:**")
        
        if st.button("🧹 إزالة الفواتير المكررة"):
            removed_count = db_manager.remove_duplicate_invoices()
            if removed_count > 0:
                st.success(f"✅ تم إزالة {removed_count} فاتورة مكررة")
            else:
                st.info("ℹ️ لا توجد فواتير مكررة")
        
        if st.button("🔧 إصلاح البيانات التالفة"):
            fixed_count = db_manager.fix_corrupted_data()
            if fixed_count > 0:
                st.success(f"✅ تم إصلاح {fixed_count} سجل")
            else:
                st.info("ℹ️ لا توجد بيانات تالفة")
    
    with data_col2:
        st.write("**أرشفة البيانات:**")
        
        archive_date = st.date_input(
            "أرشفة الفواتير قبل تاريخ:",
            value=None
        )
        
        if archive_date and st.button("📦 أرشفة البيانات القديمة"):
            archived_count = db_manager.archive_old_invoices(archive_date)
            if archived_count > 0:
                st.success(f"✅ تم أرشفة {archived_count} فاتورة")
            else:
                st.info("ℹ️ لا توجد فواتير قديمة للأرشفة")
    
    with data_col3:
        st.write("**إحصائيات قاعدة البيانات:**")
        
        db_stats = db_manager.get_database_statistics()
        
        st.metric("إجمالي الفواتير", db_stats.get('total_invoices', 0))
        st.metric("إجمالي العملاء", db_stats.get('total_customers', 0))
        st.metric("حجم قاعدة البيانات", f"{db_stats.get('db_size', 0)} KB")
    
    st.markdown("---")
    
    # معلومات النظام
    st.subheader("ℹ️ معلومات النظام")
    
    system_info = f"""
    **نظام إدارة فواتير الورشة**
    - الإصدار: 3.0.0 (البنية المنظمة)
    - تاريخ آخر تحديث: {datetime.now().strftime('%Y-%m-%d')}
    - المطور: Crestal Diamond Team
    - البيئة: {'إنتاج' if not st.get_option('server.runOnSave') else 'تطوير'}
    """
    
    st.info(system_info)
    
    # أدوات المطورين
    if st.checkbox("🔧 أدوات المطورين"):
        st.subheader("🛠️ أدوات المطورين")
        
        dev_col1, dev_col2 = st.columns(2)
        
        with dev_col1:
            st.write("**تصحيح الأخطاء:**")
            
            if st.button("🔍 فحص سلامة البيانات"):
                integrity_report = db_manager.check_data_integrity()
                if integrity_report['is_valid']:
                    st.success("✅ البيانات سليمة")
                else:
                    st.error("❌ توجد مشاكل في البيانات:")
                    for issue in integrity_report['issues']:
                        st.write(f"- {issue}")
            
            if st.button("📊 عرض معلومات النظام"):
                st.json({
                    "streamlit_version": st.__version__,
                    "python_version": "3.x",
                    "database_type": "CSV",
                    "total_memory_usage": "N/A"
                })
        
        with dev_col2:
            st.write("**سجلات النظام:**")
            
            log_level = st.selectbox(
                "مستوى السجلات:",
                ["INFO", "WARNING", "ERROR", "DEBUG"],
                index=0
            )
            
            if st.button("📋 عرض السجلات"):
                logs = db_manager.get_system_logs(log_level)
                if logs:
                    for log_entry in logs[-10:]:  # آخر 10 سجلات
                        st.text(log_entry)
                else:
                    st.info("لا توجد سجلات متاحة")
    
    st.markdown("---")
    
    # خيارات متقدمة
    st.subheader("🚨 خيارات متقدمة")
    
    st.warning("⚠️ هذه الخيارات للمستخدمين المتقدمين فقط!")
    
    advanced_col1, advanced_col2 = st.columns(2)
    
    with advanced_col1:
        if st.button("🔄 إعادة تعيين جميع الإعدادات"):
            if st.session_state.get('confirm_reset_settings', False):
                # إعادة تعيين الإعدادات
                for key in ['language', 'default_currency', 'decimal_places']:
                    if key in st.session_state:
                        del st.session_state[key]
                st.success("✅ تم إعادة تعيين جميع الإعدادات")
                st.rerun()
            else:
                st.session_state['confirm_reset_settings'] = True
                st.warning("⚠️ اضغط مرة أخرى للتأكيد")
    
    with advanced_col2:
        if st.button("🗑️ حذف جميع البيانات"):
            if st.session_state.get('confirm_delete_all', False):
                success = db_manager.delete_all_data()
                if success:
                    st.success("✅ تم حذف جميع البيانات")
                    st.rerun()
                else:
                    st.error("❌ حدث خطأ أثناء حذف البيانات")
            else:
                st.session_state['confirm_delete_all'] = True
                st.error("⚠️ هذا الإجراء لا يمكن التراجع عنه! اضغط مرة أخرى للتأكيد")
