@echo off
echo 🤖 تشغيل HORUS AI Agent...
echo ================================

cd /d "C:\Users\<USER>\HORUS-PROJECT\nodejs-version"

echo 📋 التحقق من المتطلبات...
if not exist "node_modules" (
    echo ❌ node_modules غير موجود. تشغيل npm install...
    npm install
)

echo 🔍 فحص Ollama...
npm run check-ollama

echo 🚀 تشغيل HORUS...
echo سيعمل على: http://localhost:8000
echo للإيقاف اضغط Ctrl+C

npm start

pause
