@echo off
echo 🚀 تشغيل نظام ورشة الماس مع الذكاء الاصطناعي
echo ================================================

echo 🔍 التحقق من النظام...
python -c "print('Python is working')"

if %ERRORLEVEL% EQU 0 (
    echo ✅ Python يعمل بشكل صحيح
    
    echo 🧪 اختبار الاستيرادات...
    python -c "import streamlit; print('Streamlit OK')"
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Streamlit متوفر
        
        echo 🚀 تشغيل النظام...
        echo.
        echo 🌐 سيتم فتح المتصفح على: http://localhost:8501
        echo 📋 الصفحات المتاحة:
        echo    🤖 كريستال - المساعد الذكي
        echo    📄 إنشاء فاتورة جديدة
        echo    📊 عرض الفواتير المحفوظة  
        echo    👥 حسابات العملاء
        echo    🔍 تحليل ملفات Excel
        echo    📈 إحصائيات وتقارير
        echo    ⚙️ الإعدادات
        echo.
        
        streamlit run main.py --server.port 8501 --server.address localhost
    ) else (
        echo ❌ Streamlit غير متوفر
        echo 📦 تثبيت Streamlit...
        pip install streamlit
        
        if %ERRORLEVEL% EQU 0 (
            echo ✅ تم تثبيت Streamlit
            streamlit run main.py
        ) else (
            echo ❌ فشل في تثبيت Streamlit
        )
    )
) else (
    echo ❌ Python غير متوفر أو لا يعمل
)

echo.
echo 💡 إذا لم يفتح المتصفح تلقائياً، اذهب إلى:
echo    http://localhost:8501
echo.
pause
