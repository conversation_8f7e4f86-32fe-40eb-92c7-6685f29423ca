"""
اختبار بسيط للتطبيق
"""

import streamlit as st

st.title("🧪 اختبار التطبيق")
st.write("إذا ظهرت هذه الرسالة، فإن Streamlit يعمل بشكل صحيح!")

# اختبار الاستيراد
try:
    from src.core.database import DatabaseManager
    st.success("✅ تم استيراد DatabaseManager بنجاح")
    
    db = DatabaseManager()
    st.success("✅ تم إنشاء DatabaseManager بنجاح")
    
except Exception as e:
    st.error(f"❌ خطأ في استيراد DatabaseManager: {e}")

try:
    from src.pages import invoice_creation
    st.success("✅ تم استيراد invoice_creation بنجاح")
except Exception as e:
    st.error(f"❌ خطأ في استيراد invoice_creation: {e}")

try:
    from src.pages import invoice_list
    st.success("✅ تم استيراد invoice_list بنجاح")
except Exception as e:
    st.error(f"❌ خطأ في استيراد invoice_list: {e}")
