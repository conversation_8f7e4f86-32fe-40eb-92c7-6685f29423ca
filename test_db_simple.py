#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لقاعدة البيانات
"""

import mysql.connector
from mysql.connector import Error

def test_database():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        print("🔍 محاولة الاتصال بقاعدة البيانات...")
        
        # إعدادات الاتصال
        connection = mysql.connector.connect(
            host='localhost',
            database='crestal_diamond_workshop',
            user='root',
            password='2452329511',
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        
        if connection.is_connected():
            print("✅ نجح الاتصال بقاعدة البيانات!")
            
            cursor = connection.cursor(dictionary=True)
            
            # اختبار إصدار MySQL
            cursor.execute("SELECT VERSION() as version")
            version = cursor.fetchone()
            print(f"📊 إصدار MySQL: {version['version']}")
            
            # اختبار الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 عدد الجداول: {len(tables)}")
            for table in tables:
                table_name = list(table.values())[0]
                print(f"  - {table_name}")
            
            # اختبار عدد الخدمات
            cursor.execute("SELECT COUNT(*) as count FROM services")
            services_count = cursor.fetchone()
            print(f"🔧 عدد الخدمات: {services_count['count']}")
            
            # اختبار الخدمات المتاحة
            if services_count['count'] > 0:
                cursor.execute("SELECT name, category, default_price FROM services LIMIT 3")
                services = cursor.fetchall()
                print("📋 الخدمات المتاحة:")
                for service in services:
                    print(f"  - {service['name']} ({service['category']}): {service['default_price']} جنيه")
            
            # اختبار العملاء
            cursor.execute("SELECT COUNT(*) as count FROM customers")
            customers_count = cursor.fetchone()
            print(f"👥 عدد العملاء: {customers_count['count']}")
            
            # اختبار الفواتير
            cursor.execute("SELECT COUNT(*) as count FROM invoices")
            invoices_count = cursor.fetchone()
            print(f"📄 عدد الفواتير: {invoices_count['count']}")
            
            cursor.close()
            connection.close()
            print("✅ تم إغلاق الاتصال بنجاح")
            return True
            
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار قاعدة البيانات")
    print("=" * 40)
    
    success = test_database()
    
    print("=" * 40)
    if success:
        print("🎉 جميع الاختبارات نجحت!")
    else:
        print("⚠️ فشل في بعض الاختبارات")
