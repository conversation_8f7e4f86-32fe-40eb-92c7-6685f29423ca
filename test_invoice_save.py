#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حفظ الفواتير مع التاريخ والوقت
Test Invoice Saving with Date and Time
"""

import sys
import os
from datetime import datetime

# إضافة المسار للوصول للمكتبات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_invoice_save():
    """اختبار حفظ فاتورة مع التاريخ والوقت"""
    print("🧪 اختبار حفظ الفواتير مع التاريخ والوقت")
    print("=" * 50)
    
    try:
        from src.core.database import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # إنشاء فاتورة تجريبية
        current_datetime = datetime.now()
        test_invoice = {
            'customer_name': 'عميل تجريبي',
            'date': current_datetime.strftime('%Y-%m-%d'),
            'timestamp': current_datetime.strftime('%Y-%m-%d %H:%M:%S'),
            'description': 'فاتورة اختبار مع التاريخ والوقت',
            'gold_change': -5.5,
            'usd_change': 150.0,
            'egp_change': 4650.0
        }
        
        print(f"📋 بيانات الفاتورة التجريبية:")
        print(f"   العميل: {test_invoice['customer_name']}")
        print(f"   التاريخ: {test_invoice['date']}")
        print(f"   الوقت: {test_invoice['timestamp']}")
        print(f"   الوصف: {test_invoice['description']}")
        
        # حفظ الفاتورة
        print("\n💾 محاولة حفظ الفاتورة...")
        success = db.save_invoice(test_invoice)
        
        if success:
            print("✅ تم حفظ الفاتورة بنجاح!")
            
            # التحقق من الحفظ
            print("\n🔍 التحقق من الحفظ...")
            invoices_df = db.load_invoices()
            
            if not invoices_df.empty:
                last_invoice = invoices_df.iloc[-1]
                print(f"✅ آخر فاتورة محفوظة:")
                print(f"   العميل: {last_invoice['customer_name']}")
                print(f"   التاريخ: {last_invoice['date']}")
                
                if 'timestamp' in last_invoice and last_invoice['timestamp']:
                    print(f"   الوقت: {last_invoice['timestamp']}")
                    print("✅ التاريخ والوقت محفوظان بشكل صحيح!")
                else:
                    print("❌ الوقت غير محفوظ!")
                    return False
                
                print(f"   الوصف: {last_invoice['description']}")
                print(f"   إجمالي الفواتير: {len(invoices_df)}")
                
                return True
            else:
                print("❌ لا توجد فواتير محفوظة!")
                return False
        else:
            print("❌ فشل في حفظ الفاتورة!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_backup_creation():
    """اختبار إنشاء النسخ الاحتياطية"""
    print("\n🔄 اختبار إنشاء النسخ الاحتياطية")
    print("-" * 30)
    
    try:
        from src.core.database import DatabaseManager
        
        db = DatabaseManager()
        
        # إنشاء نسخة احتياطية
        backup_file = db.create_backup('invoices')
        
        if backup_file and os.path.exists(backup_file):
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_file}")
            return True
        else:
            print("❌ فشل في إنشاء النسخة الاحتياطية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النسخ الاحتياطية: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    tests_passed = 0
    total_tests = 2
    
    # اختبار حفظ الفواتير
    if test_invoice_save():
        tests_passed += 1
    
    # اختبار النسخ الاحتياطية
    if test_backup_creation():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتيجة الاختبار: {tests_passed}/{total_tests} اختبار نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ حفظ الفواتير مع التاريخ والوقت يعمل بشكل صحيح")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
