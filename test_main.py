#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ملف main.py
Test main.py file
"""

import sys
import os

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        # اختبار استيراد Streamlit
        import streamlit as st
        print("✅ Streamlit imported successfully")
        
        # اختبار استيراد DatabaseManager
        from src.core.database import DatabaseManager
        print("✅ DatabaseManager imported successfully")
        
        # اختبار استيراد الصفحات
        from src.pages import (
            invoice_creation,
            invoice_list,
            customer_accounts,
            excel_analysis,
            reports,
            settings
        )
        print("✅ All pages imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_database_manager():
    """اختبار مدير قاعدة البيانات"""
    print("\n🗄️ اختبار مدير قاعدة البيانات...")
    
    try:
        from src.core.database import DatabaseManager
        
        # إنشاء مثيل
        db_manager = DatabaseManager()
        print("✅ DatabaseManager instance created")
        
        # اختبار الاتصال
        if hasattr(db_manager, 'connect'):
            print("✅ DatabaseManager has connect method")
        
        return True
        
    except Exception as e:
        print(f"❌ DatabaseManager error: {e}")
        return False

def test_pages():
    """اختبار الصفحات"""
    print("\n📄 اختبار الصفحات...")
    
    pages = [
        'invoice_creation',
        'invoice_list', 
        'customer_accounts',
        'excel_analysis',
        'reports',
        'settings'
    ]
    
    success_count = 0
    
    for page_name in pages:
        try:
            module = __import__(f'src.pages.{page_name}', fromlist=[page_name])
            
            # التحقق من وجود دالة show_page
            if hasattr(module, 'show_page'):
                print(f"✅ {page_name}: has show_page function")
                success_count += 1
            else:
                print(f"⚠️ {page_name}: missing show_page function")
                
        except Exception as e:
            print(f"❌ {page_name}: {e}")
    
    print(f"\n📊 نتيجة اختبار الصفحات: {success_count}/{len(pages)} صفحة تعمل")
    return success_count == len(pages)

def test_main_structure():
    """اختبار بنية main.py"""
    print("\n🏗️ اختبار بنية main.py...")
    
    try:
        # قراءة محتوى main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من العناصر المهمة
        checks = [
            ('st.set_page_config', 'إعدادات الصفحة'),
            ('st.sidebar', 'الشريط الجانبي'),
            ('selectbox', 'قائمة التنقل'),
            ('show_page', 'استدعاء الصفحات'),
            ('DatabaseManager', 'مدير قاعدة البيانات')
        ]
        
        success_count = 0
        for check, description in checks:
            if check in content:
                print(f"✅ {description}: موجود")
                success_count += 1
            else:
                print(f"❌ {description}: مفقود")
        
        print(f"\n📊 نتيجة اختبار البنية: {success_count}/{len(checks)} عنصر موجود")
        return success_count == len(checks)
        
    except Exception as e:
        print(f"❌ خطأ في قراءة main.py: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار ملف main.py")
    print("=" * 50)
    
    tests = [
        ("اختبار الاستيرادات", test_imports),
        ("اختبار مدير قاعدة البيانات", test_database_manager),
        ("اختبار الصفحات", test_pages),
        ("اختبار بنية main.py", test_main_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name}: اكتمل مع تحذيرات")
        except Exception as e:
            print(f"❌ {test_name}: فشل - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتيجة اختبار main.py: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 main.py جاهز للاستخدام!")
        print("\n🚀 يمكنك تشغيله باستخدام:")
        print("   streamlit run main.py")
        return True
    else:
        print("⚠️ main.py يحتاج بعض التحسينات")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
