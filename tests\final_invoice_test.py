#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لحفظ الفواتير مع التاريخ والوقت
Final Test for Invoice Saving with Date and Time
"""

import sys
import os
from datetime import datetime

# إضافة المسار للوصول للمكتبات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_invoice_flow():
    """اختبار تدفق كامل لإنشاء وحفظ فاتورة"""
    print("🧪 اختبار التدفق الكامل لحفظ الفواتير")
    print("=" * 50)
    
    try:
        from src.core.database import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # إنشاء فاتورة كاملة
        current_datetime = datetime.now()
        test_invoice = {
            'customer_name': 'أحمد محمد',
            'date': current_datetime.strftime('%Y-%m-%d'),
            'timestamp': current_datetime.strftime('%Y-%m-%d %H:%M:%S'),
            'description': 'خاتم ذهب عيار 21 مع حجر كريم',
            'gold_change': -3.5,
            'usd_change': 120.0,
            'egp_change': 3720.0
        }
        
        print(f"📋 بيانات الفاتورة:")
        print(f"   👤 العميل: {test_invoice['customer_name']}")
        print(f"   📅 التاريخ: {test_invoice['date']}")
        print(f"   🕐 الوقت: {test_invoice['timestamp']}")
        print(f"   📝 الوصف: {test_invoice['description']}")
        print(f"   ⚖️ الذهب: {test_invoice['gold_change']} جرام")
        print(f"   💵 الدولار: ${test_invoice['usd_change']}")
        print(f"   💰 الجنيه: {test_invoice['egp_change']} ج.م")
        
        # حفظ الفاتورة
        print("\n💾 حفظ الفاتورة...")
        success = db.save_invoice(test_invoice)
        
        if success:
            print("✅ تم حفظ الفاتورة بنجاح!")
            
            # التحقق من الحفظ
            print("\n🔍 التحقق من البيانات المحفوظة...")
            invoices_df = db.load_invoices()
            
            if not invoices_df.empty:
                print(f"📊 إجمالي الفواتير: {len(invoices_df)}")
                
                # عرض آخر فاتورة
                last_invoice = invoices_df.iloc[-1]
                print(f"\n📄 آخر فاتورة محفوظة:")
                print(f"   👤 العميل: {last_invoice['customer_name']}")
                print(f"   📅 التاريخ: {last_invoice['date']}")
                print(f"   🕐 الوقت: {last_invoice['timestamp']}")
                print(f"   📝 الوصف: {last_invoice['description']}")
                print(f"   ⚖️ الذهب: {last_invoice['gold_change']} جرام")
                print(f"   💵 الدولار: ${last_invoice['usd_change']}")
                print(f"   💰 الجنيه: {last_invoice['egp_change']} ج.م")
                
                # التحقق من وجود التاريخ والوقت
                if 'timestamp' in last_invoice and last_invoice['timestamp']:
                    print("\n✅ التاريخ والوقت محفوظان بشكل صحيح!")
                    return True
                else:
                    print("\n❌ مشكلة في حفظ التاريخ والوقت!")
                    return False
            else:
                print("❌ لا توجد فواتير محفوظة!")
                return False
        else:
            print("❌ فشل في حفظ الفاتورة!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_multiple_invoices():
    """اختبار حفظ عدة فواتير"""
    print("\n🔄 اختبار حفظ عدة فواتير")
    print("-" * 30)
    
    try:
        from src.core.database import DatabaseManager
        
        db = DatabaseManager()
        
        # إنشاء 3 فواتير تجريبية
        customers = ['سارة أحمد', 'محمد علي', 'فاطمة حسن']
        descriptions = ['سوار ذهب', 'قلادة مع حجر', 'أقراط ذهبية']
        
        for i, (customer, desc) in enumerate(zip(customers, descriptions)):
            current_datetime = datetime.now()
            invoice = {
                'customer_name': customer,
                'date': current_datetime.strftime('%Y-%m-%d'),
                'timestamp': current_datetime.strftime('%Y-%m-%d %H:%M:%S'),
                'description': desc,
                'gold_change': -(2.0 + i),
                'usd_change': 80.0 + (i * 20),
                'egp_change': 2480.0 + (i * 620)
            }
            
            success = db.save_invoice(invoice)
            if success:
                print(f"✅ تم حفظ فاتورة {customer}")
            else:
                print(f"❌ فشل في حفظ فاتورة {customer}")
                return False
        
        # التحقق من العدد الإجمالي
        invoices_df = db.load_invoices()
        print(f"\n📊 إجمالي الفواتير المحفوظة: {len(invoices_df)}")
        
        if len(invoices_df) >= 4:  # الفاتورة الأولى + 3 فواتير جديدة
            print("✅ تم حفظ جميع الفواتير بنجاح!")
            return True
        else:
            print("❌ لم يتم حفظ جميع الفواتير!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الفواتير المتعددة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    tests_passed = 0
    total_tests = 2
    
    # اختبار التدفق الكامل
    if test_complete_invoice_flow():
        tests_passed += 1
    
    # اختبار الفواتير المتعددة
    if test_multiple_invoices():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 النتيجة النهائية: {tests_passed}/{total_tests} اختبار نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ نظام حفظ الفواتير يعمل بشكل مثالي")
        print("✅ التاريخ والوقت يُحفظان بشكل صحيح")
        print("✅ النظام جاهز للاستخدام!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الاختبار")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
