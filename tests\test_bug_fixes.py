#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات البرمجية الشاملة
Test Bug Fixes - Comprehensive Testing

هذا الملف يختبر جميع الإصلاحات التي تم تطبيقها على النظام
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_numeric_conversion():
    """اختبار تحويل البيانات النصية إلى رقمية"""
    print("🔢 اختبار تحويل البيانات النصية إلى رقمية...")
    
    # بيانات اختبار مختلطة
    test_data = ['100', '200.5', '-50', 'نص', '', None, '0']
    
    try:
        # تحويل باستخدام pd.to_numeric
        numeric_data = pd.to_numeric(test_data, errors='coerce')
        
        # فحص النتائج
        assert not pd.isna(numeric_data[0])  # '100' يجب أن يتحول إلى 100
        assert not pd.isna(numeric_data[1])  # '200.5' يجب أن يتحول إلى 200.5
        assert not pd.isna(numeric_data[2])  # '-50' يجب أن يتحول إلى -50
        assert pd.isna(numeric_data[3])      # 'نص' يجب أن يصبح NaN
        
        print("✅ تحويل البيانات النصية إلى رقمية يعمل بشكل صحيح")
        return True
    except Exception as e:
        print(f"❌ خطأ في تحويل البيانات: {e}")
        return False

def test_csv_encoding():
    """اختبار قراءة ملفات CSV بترميزات مختلفة"""
    print("📁 اختبار قراءة ملفات CSV بترميزات مختلفة...")
    
    try:
        # إنشاء ملف CSV اختبار
        test_file = "test_encoding.csv"
        test_data = pd.DataFrame({
            'الاسم': ['أحمد', 'فاطمة', 'محمد'],
            'العمر': [25, 30, 35],
            'الراتب': [5000.5, 6000.0, 7500.25]
        })
        
        # حفظ بترميز utf-8-sig
        test_data.to_csv(test_file, index=False, encoding='utf-8-sig')
        
        # اختبار قراءة بترميزات متعددة
        encodings = ['utf-8-sig', 'utf-8', 'cp1256', 'iso-8859-1', 'latin-1']
        success = False
        
        for encoding in encodings:
            try:
                df = pd.read_csv(test_file, encoding=encoding)
                if not df.empty:
                    success = True
                    break
            except UnicodeDecodeError:
                continue
        
        # تنظيف
        if os.path.exists(test_file):
            os.remove(test_file)
        
        if success:
            print("✅ قراءة ملفات CSV بترميزات متعددة تعمل بشكل صحيح")
            return True
        else:
            print("❌ فشل في قراءة ملف CSV")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار ترميز CSV: {e}")
        return False

def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("🚨 اختبار معالجة الأخطاء...")
    
    try:
        # اختبار ملف غير موجود
        try:
            pd.read_csv("ملف_غير_موجود.csv")
        except FileNotFoundError as e:
            print("✅ معالجة FileNotFoundError تعمل بشكل صحيح")
        
        # اختبار ملف فارغ
        empty_file = "empty_test.csv"
        with open(empty_file, 'w', encoding='utf-8') as f:
            f.write("")
        
        try:
            pd.read_csv(empty_file)
        except pd.errors.EmptyDataError:
            print("✅ معالجة EmptyDataError تعمل بشكل صحيح")
        
        # تنظيف
        if os.path.exists(empty_file):
            os.remove(empty_file)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة الأخطاء: {e}")
        return False

def test_large_file_detection():
    """اختبار كشف الملفات الكبيرة"""
    print("📊 اختبار كشف الملفات الكبيرة...")
    
    try:
        # إنشاء DataFrame كبير (أكثر من 10,000 صف)
        large_data = pd.DataFrame({
            'id': range(15000),
            'value': np.random.randn(15000),
            'category': ['A', 'B', 'C'] * 5000
        })
        
        # فحص حجم البيانات
        is_large = len(large_data) > 10000
        
        if is_large:
            print("✅ كشف الملفات الكبيرة يعمل بشكل صحيح")
            return True
        else:
            print("❌ فشل في كشف الملفات الكبيرة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار كشف الملفات الكبيرة: {e}")
        return False

def test_data_formatting():
    """اختبار تنسيق البيانات"""
    print("🎨 اختبار تنسيق البيانات...")
    
    try:
        # بيانات اختبار
        test_values = [100.123, 200.456, 300.789, 'نص', None]
        
        formatted_values = []
        for x in test_values:
            if isinstance(x, (int, float)):
                formatted_values.append(f"{x:.2f}")
            elif str(x).replace('.','').replace('-','').isdigit():
                formatted_values.append(f"{float(x):.2f}")
            else:
                formatted_values.append(str(x))
        
        # فحص النتائج
        assert formatted_values[0] == "100.12"
        assert formatted_values[1] == "200.46"
        assert formatted_values[2] == "300.79"
        
        print("✅ تنسيق البيانات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنسيق البيانات: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار الإصلاحات البرمجية الشاملة")
    print("=" * 60)
    
    tests = [
        test_numeric_conversion,
        test_csv_encoding,
        test_error_handling,
        test_large_file_detection,
        test_data_formatting
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print("-" * 40)
        except Exception as e:
            print(f"❌ خطأ في تشغيل الاختبار: {e}")
            print("-" * 40)
    
    print("=" * 60)
    print(f"📊 نتائج الاختبارات: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي")
        return True
    else:
        print(f"⚠️ {total - passed} اختبار فشل - يحتاج مراجعة")
        return False

if __name__ == "__main__":
    print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print(f"🐼 إصدار Pandas: {pd.__version__}")
    print()
    
    success = run_all_tests()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)
