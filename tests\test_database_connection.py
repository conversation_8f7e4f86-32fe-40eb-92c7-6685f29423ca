"""
اختبار الاتصال بقاعدة البيانات
Database Connection Test Script
"""

import sys
import os
from datetime import datetime

def test_imports():
    """اختبار استيراد المكتبات المطلوبة"""
    print("🔍 اختبار استيراد المكتبات...")
    print("🔍 Testing library imports...")
    
    try:
        import mysql.connector
        print("✅ mysql-connector-python: متوفر")
        print(f"   الإصدار: {mysql.connector.__version__}")
    except ImportError:
        print("❌ mysql-connector-python: غير متوفر")
        return False
    
    try:
        import pandas as pd
        print("✅ pandas: متوفر")
        print(f"   الإصدار: {pd.__version__}")
    except ImportError:
        print("❌ pandas: غير متوفر")
        return False
    
    try:
        from database_config import get_database_config
        print("✅ database_config: متوفر")
    except ImportError:
        print("❌ database_config: غير متوفر")
        return False
    
    return True

def test_config_file():
    """اختبار ملف الإعدادات"""
    print("\n📁 اختبار ملف الإعدادات...")
    print("📁 Testing configuration file...")
    
    if os.path.exists('.env'):
        print("✅ ملف .env موجود")
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'DB_PASSWORD=' in content:
                print("✅ إعدادات قاعدة البيانات موجودة")
                return True
            else:
                print("⚠️ يرجى تحديث كلمة المرور في ملف .env")
                return False
    else:
        print("❌ ملف .env غير موجود")
        print("💡 قم بتشغيل: python database_config.py")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔗 اختبار الاتصال بقاعدة البيانات...")
    print("🔗 Testing database connection...")
    
    try:
        from database_config import get_database_config
        import mysql.connector
        
        config = get_database_config()
        print(f"🏠 الخادم: {config['host']}:{config['port']}")
        print(f"🗄️ قاعدة البيانات: {config['database']}")
        print(f"👤 المستخدم: {config['user']}")
        
        # محاولة الاتصال
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
            
            # اختبار الجداول
            cursor = connection.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"📊 عدد الجداول الموجودة: {len(tables)}")
            for table in tables:
                print(f"   - {table[0]}")
            
            # اختبار بيانات العينة
            cursor.execute("SELECT COUNT(*) FROM invoices")
            invoice_count = cursor.fetchone()[0]
            print(f"📄 عدد الفواتير: {invoice_count}")
            
            cursor.execute("SELECT COUNT(*) FROM customers")
            customer_count = cursor.fetchone()[0]
            print(f"👥 عدد العملاء: {customer_count}")
            
            cursor.close()
            connection.close()
            return True
            
    except mysql.connector.Error as e:
        print(f"❌ خطأ في الاتصال: {e}")
        print("\n💡 تحقق من:")
        print("   - تشغيل خادم MySQL")
        print("   - صحة كلمة المرور في ملف .env")
        print("   - وجود قاعدة البيانات crestal_diamond_workshop")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_database_manager():
    """اختبار DatabaseManager"""
    print("\n🔧 اختبار DatabaseManager...")
    print("🔧 Testing DatabaseManager...")
    
    try:
        from database_manager import DatabaseManager
        
        # إنشاء مثيل
        db = DatabaseManager(use_mysql=True)
        
        if db.use_mysql:
            print("✅ DatabaseManager يستخدم MySQL")
            
            # اختبار الاتصال
            if db.test_connection():
                print("✅ اختبار الاتصال نجح")
                
                # اختبار قراءة البيانات
                invoices = db.get_all_invoices()
                print(f"📊 تم قراءة {len(invoices)} فاتورة")
                
                db.close_connection()
                return True
            else:
                print("❌ فشل اختبار الاتصال")
                return False
        else:
            print("⚠️ DatabaseManager يستخدم نظام CSV")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في DatabaseManager: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار إعداد قاعدة البيانات")
    print("🧪 Database Setup Test")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # تشغيل الاختبارات
    tests = [
        ("استيراد المكتبات", test_imports),
        ("ملف الإعدادات", test_config_file),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("DatabaseManager", test_database_manager)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج - Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed == len(results):
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        print("🚀 يمكنك الآن تشغيل: streamlit run invoice_app.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع DATABASE_SETUP_GUIDE.md")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
