#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح بيانات الفواتير
Test Invoice Data Fix

يختبر هذا الملف صحة إصلاح بيانات الفواتير
"""

import sys
import os
import pandas as pd
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_invoice_data_structure():
    """اختبار بنية بيانات الفواتير"""
    print("🔍 اختبار بنية بيانات الفواتير...")
    
    try:
        # قراءة ملف الفواتير
        df = pd.read_csv("invoices.csv", encoding='utf-8-sig')
        
        # فحص الأعمدة المطلوبة
        required_columns = ['customer_name', 'date', 'description', 'gold_change', 'usd_change', 'egp_change', 'timestamp']
        
        for col in required_columns:
            if col not in df.columns:
                print(f"❌ العمود المطلوب '{col}' غير موجود")
                return False
        
        print("✅ جميع الأعمدة المطلوبة موجودة")
        
        # فحص ترتيب الأعمدة
        if list(df.columns) == required_columns:
            print("✅ ترتيب الأعمدة صحيح")
        else:
            print(f"⚠️ ترتيب الأعمدة: {list(df.columns)}")
            print(f"⚠️ المطلوب: {required_columns}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البنية: {str(e)}")
        return False

def test_data_types():
    """اختبار أنواع البيانات"""
    print("🔢 اختبار أنواع البيانات...")
    
    try:
        df = pd.read_csv("invoices.csv", encoding='utf-8-sig')
        
        # اختبار التواريخ
        try:
            pd.to_datetime(df['date'])
            print("✅ تواريخ الفواتير صحيحة")
        except:
            print("❌ مشكلة في تواريخ الفواتير")
            return False
        
        try:
            pd.to_datetime(df['timestamp'])
            print("✅ timestamps صحيحة")
        except:
            print("❌ مشكلة في timestamps")
            return False
        
        # اختبار الأرقام
        numeric_columns = ['gold_change', 'usd_change', 'egp_change']
        for col in numeric_columns:
            try:
                numeric_data = pd.to_numeric(df[col], errors='coerce')
                if numeric_data.isna().any():
                    print(f"⚠️ بعض القيم في {col} غير رقمية")
                else:
                    print(f"✅ {col} صحيح")
            except:
                print(f"❌ مشكلة في {col}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أنواع البيانات: {str(e)}")
        return False

def test_data_consistency():
    """اختبار تناسق البيانات"""
    print("🔄 اختبار تناسق البيانات...")
    
    try:
        df = pd.read_csv("invoices.csv", encoding='utf-8-sig')
        
        # فحص البيانات الفارغة
        empty_count = df.isnull().sum().sum()
        if empty_count > 0:
            print(f"⚠️ يوجد {empty_count} خلية فارغة")
            for col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    print(f"  - {col}: {null_count} خلية فارغة")
        else:
            print("✅ لا توجد بيانات فارغة")
        
        # فحص تناسق التواريخ
        dates = pd.to_datetime(df['date'])
        timestamps = pd.to_datetime(df['timestamp'])
        
        # التحقق من أن timestamp في نفس يوم date أو بعده
        date_consistency = (timestamps.dt.date >= dates.dt.date).all()
        if date_consistency:
            print("✅ التواريخ متناسقة")
        else:
            print("⚠️ بعض timestamps قبل تاريخ الفاتورة")
        
        # فحص القيم المنطقية
        if (df['gold_change'] <= 0).all():
            print("✅ قيم تغيير الذهب منطقية (سالبة أو صفر)")
        else:
            print("⚠️ بعض قيم تغيير الذهب موجبة")
        
        if (df['usd_change'] >= 0).all() and (df['egp_change'] >= 0).all():
            print("✅ قيم المبالغ منطقية (موجبة)")
        else:
            print("⚠️ بعض المبالغ سالبة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التناسق: {str(e)}")
        return False

def test_display_formatting():
    """اختبار تنسيق العرض"""
    print("🎨 اختبار تنسيق العرض...")
    
    try:
        df = pd.read_csv("invoices.csv", encoding='utf-8-sig')
        
        # محاكاة تنسيق العرض
        display_df = df.copy()
        
        # تنسيق التواريخ
        display_df['date'] = pd.to_datetime(display_df['date']).dt.strftime('%Y-%m-%d')
        display_df['timestamp'] = pd.to_datetime(display_df['timestamp']).dt.strftime('%Y-%m-%d %H:%M')
        
        # تنسيق الأرقام
        display_df['usd_change'] = display_df['usd_change'].apply(lambda x: f"${x:.2f}")
        display_df['egp_change'] = display_df['egp_change'].apply(lambda x: f"{x:.2f} ج.م")
        display_df['gold_change'] = display_df['gold_change'].apply(lambda x: f"{x:.2f} جرام")
        
        print("✅ تنسيق العرض يعمل بشكل صحيح")
        
        # عرض عينة
        print("\n📋 عينة من البيانات المنسقة:")
        print(display_df.head(2).to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التنسيق: {str(e)}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار إصلاح بيانات الفواتير")
    print("=" * 50)
    
    tests = [
        test_invoice_data_structure,
        test_data_types,
        test_data_consistency,
        test_display_formatting
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print("-" * 30)
        except Exception as e:
            print(f"❌ خطأ في تشغيل الاختبار: {e}")
            print("-" * 30)
    
    print("=" * 50)
    print(f"📊 نتائج الاختبارات: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات إصلاح البيانات نجحت!")
        print("✅ بيانات الفواتير جاهزة للاستخدام")
        return True
    else:
        print(f"⚠️ {total - passed} اختبار فشل - قد تحتاج مراجعة")
        return False

if __name__ == "__main__":
    print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 مجلد العمل: {os.getcwd()}")
    print()
    
    success = run_all_tests()
    
    if success:
        print("\n🚀 يمكنك الآن تشغيل التطبيق بأمان:")
        print("   streamlit run main.py")
        sys.exit(0)
    else:
        print("\n🔧 يرجى تشغيل سكريبت الإصلاح:")
        print("   python scripts/fix_invoice_data.py")
        sys.exit(1)
